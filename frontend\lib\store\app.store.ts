import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

interface AppState {
  // Application metadata
  version: string;
  buildNumber: string;
  environment: 'development' | 'staging' | 'production';
  
  // Feature flags
  features: {
    [key: string]: boolean;
  };
  
  // User preferences
  preferences: {
    language: string;
    timezone: string;
    dateFormat: string;
    timeFormat: '12h' | '24h';
    currency: string;
    autoSave: boolean;
    notifications: {
      email: boolean;
      push: boolean;
      desktop: boolean;
      sound: boolean;
    };
    accessibility: {
      reducedMotion: boolean;
      highContrast: boolean;
      fontSize: 'small' | 'medium' | 'large';
    };
  };
  
  // Recent activity
  recentItems: Array<{
    id: string;
    type: 'agent' | 'tool' | 'workflow' | 'template';
    name: string;
    path: string;
    timestamp: number;
  }>;
  
  // Search history
  searchHistory: Array<{
    query: string;
    timestamp: number;
    results: number;
  }>;
  
  // Workspace state
  workspace: {
    activeProject?: string;
    openTabs: Array<{
      id: string;
      type: string;
      title: string;
      path: string;
      modified: boolean;
    }>;
    activeTab?: string;
  };
}

interface AppStore extends AppState {
  // Feature flag actions
  setFeature: (feature: string, enabled: boolean) => void;
  getFeature: (feature: string) => boolean;
  
  // Preference actions
  setPreference: <K extends keyof AppState['preferences']>(
    key: K,
    value: AppState['preferences'][K]
  ) => void;
  updatePreferences: (preferences: Partial<AppState['preferences']>) => void;
  resetPreferences: () => void;
  
  // Recent items actions
  addRecentItem: (item: Omit<AppState['recentItems'][0], 'timestamp'>) => void;
  removeRecentItem: (id: string) => void;
  clearRecentItems: () => void;
  
  // Search history actions
  addSearchQuery: (query: string, results: number) => void;
  clearSearchHistory: () => void;
  
  // Workspace actions
  setActiveProject: (projectId: string) => void;
  addTab: (tab: Omit<AppState['workspace']['openTabs'][0], 'modified'>) => void;
  removeTab: (tabId: string) => void;
  setActiveTab: (tabId: string) => void;
  markTabModified: (tabId: string, modified: boolean) => void;
  closeAllTabs: () => void;
  
  // App metadata actions
  setAppMetadata: (metadata: Partial<Pick<AppState, 'version' | 'buildNumber' | 'environment'>>) => void;
}

const defaultPreferences: AppState['preferences'] = {
  language: 'en',
  timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
  dateFormat: 'MM/dd/yyyy',
  timeFormat: '12h',
  currency: 'USD',
  autoSave: true,
  notifications: {
    email: true,
    push: true,
    desktop: true,
    sound: false,
  },
  accessibility: {
    reducedMotion: false,
    highContrast: false,
    fontSize: 'medium',
  },
};

export const useAppStore = create<AppStore>()(
  persist(
    (set, get) => ({
      // Initial state
      version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
      buildNumber: process.env.NEXT_PUBLIC_BUILD_NUMBER || 'dev',
      environment: (process.env.NODE_ENV as any) || 'development',
      features: {},
      preferences: defaultPreferences,
      recentItems: [],
      searchHistory: [],
      workspace: {
        openTabs: [],
      },

      // Feature flag actions
      setFeature: (feature, enabled) => set((state) => ({
        features: { ...state.features, [feature]: enabled }
      })),
      
      getFeature: (feature) => get().features[feature] ?? false,

      // Preference actions
      setPreference: (key, value) => set((state) => ({
        preferences: { ...state.preferences, [key]: value }
      })),
      
      updatePreferences: (preferences) => set((state) => ({
        preferences: { ...state.preferences, ...preferences }
      })),
      
      resetPreferences: () => set({ preferences: defaultPreferences }),

      // Recent items actions
      addRecentItem: (item) => set((state) => {
        const newItem = { ...item, timestamp: Date.now() };
        const filtered = state.recentItems.filter(i => i.id !== item.id);
        return {
          recentItems: [newItem, ...filtered].slice(0, 20) // Keep only last 20 items
        };
      }),
      
      removeRecentItem: (id) => set((state) => ({
        recentItems: state.recentItems.filter(item => item.id !== id)
      })),
      
      clearRecentItems: () => set({ recentItems: [] }),

      // Search history actions
      addSearchQuery: (query, results) => set((state) => {
        const newQuery = { query, results, timestamp: Date.now() };
        const filtered = state.searchHistory.filter(q => q.query !== query);
        return {
          searchHistory: [newQuery, ...filtered].slice(0, 50) // Keep only last 50 searches
        };
      }),
      
      clearSearchHistory: () => set({ searchHistory: [] }),

      // Workspace actions
      setActiveProject: (activeProject) => set((state) => ({
        workspace: { ...state.workspace, activeProject }
      })),
      
      addTab: (tab) => set((state) => {
        const newTab = { ...tab, modified: false };
        const exists = state.workspace.openTabs.find(t => t.id === tab.id);
        if (exists) {
          return {
            workspace: {
              ...state.workspace,
              activeTab: tab.id
            }
          };
        }
        return {
          workspace: {
            ...state.workspace,
            openTabs: [...state.workspace.openTabs, newTab],
            activeTab: tab.id
          }
        };
      }),
      
      removeTab: (tabId) => set((state) => {
        const filtered = state.workspace.openTabs.filter(tab => tab.id !== tabId);
        const activeTab = state.workspace.activeTab === tabId 
          ? filtered[0]?.id 
          : state.workspace.activeTab;
        return {
          workspace: {
            ...state.workspace,
            openTabs: filtered,
            activeTab
          }
        };
      }),
      
      setActiveTab: (activeTab) => set((state) => ({
        workspace: { ...state.workspace, activeTab }
      })),
      
      markTabModified: (tabId, modified) => set((state) => ({
        workspace: {
          ...state.workspace,
          openTabs: state.workspace.openTabs.map(tab =>
            tab.id === tabId ? { ...tab, modified } : tab
          )
        }
      })),
      
      closeAllTabs: () => set((state) => ({
        workspace: {
          ...state.workspace,
          openTabs: [],
          activeTab: undefined
        }
      })),

      // App metadata actions
      setAppMetadata: (metadata) => set((state) => ({
        ...state,
        ...metadata
      })),
    }),
    {
      name: 'synapseai-app-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        features: state.features,
        preferences: state.preferences,
        recentItems: state.recentItems,
        searchHistory: state.searchHistory,
        workspace: state.workspace,
      }),
      version: 1,
    }
  )
);
