{"version": 3, "file": "sso.service.js", "sourceRoot": "", "sources": ["../../../../../../src/modules/auth/sso/sso.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAAwF;AACxF,2CAA+C;AAC/C,gEAA+D;AAC/D,4EAAgE;AAChE,oCAAoC;AAEpC,IAAK,QAKJ;AALD,WAAK,QAAQ;IACX,uCAA2B,CAAA;IAC3B,mCAAuB,CAAA;IACvB,mCAAuB,CAAA;IACvB,6BAAiB,CAAA;AACnB,CAAC,EALI,QAAQ,KAAR,QAAQ,QAKZ;AA2BM,IAAM,UAAU,GAAhB,MAAM,UAAU;IACrB,YACU,MAAqB,EACrB,aAA4B,EAC5B,MAAqB;QAFrB,WAAM,GAAN,MAAM,CAAe;QACrB,kBAAa,GAAb,aAAa,CAAe;QAC5B,WAAM,GAAN,MAAM,CAAe;IAC5B,CAAC;IAKJ,KAAK,CAAC,iBAAiB,CAAC,SAA+B;QACrD,IAAI,CAAC;YAEH,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;YAEzD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;gBACpD,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS,CAAC,IAAI;oBACpB,IAAI,EAAE,SAAS,CAAC,IAAI;oBACpB,cAAc,EAAE,SAAS,CAAC,cAAc;oBACxC,MAAM,EAAE,SAAS,CAAC,MAAM;oBACxB,QAAQ,EAAE,IAAI;iBACf;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,SAAS,EAAE;gBACnD,cAAc,EAAE,SAAS,CAAC,cAAc;gBACxC,QAAQ,EAAE,cAAc;gBACxB,OAAO,EAAE;oBACP,UAAU,EAAE,QAAQ,CAAC,EAAE;oBACvB,IAAI,EAAE,SAAS,CAAC,IAAI;oBACpB,IAAI,EAAE,SAAS,CAAC,IAAI;iBACrB;aACF,CAAC,CAAC;YAEH,OAAO,QAAuB,CAAC;QACjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YAC1D,MAAM,IAAI,4BAAmB,CAAC,+BAA+B,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,eAAe,CAAC,cAAsB;QAC1C,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;YACtC,KAAK,EAAE;gBACL,cAAc;gBACd,QAAQ,EAAE,IAAI;aACf;SACF,CAA2B,CAAC;IAC/B,CAAC;IAKD,KAAK,CAAC,kBAAkB,CACtB,UAAkB,EAClB,WAA2B;QAE3B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;gBACxD,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE;gBACzB,OAAO,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE;aAChC,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBACpC,MAAM,IAAI,8BAAqB,CAAC,oCAAoC,CAAC,CAAC;YACxE,CAAC;YAGD,IAAI,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;gBAC1C,KAAK,EAAE;oBACL,KAAK,EAAE,WAAW,CAAC,KAAK;oBACxB,cAAc,EAAE,QAAQ,CAAC,cAAc;iBACxC;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,EAAE,CAAC;gBAEV,IAAI,GAAG,MAAM,IAAI,CAAC,wBAAwB,CACxC,WAAW,EACX,QAAQ,CAAC,cAAc,EACvB,QAAQ,CAAC,MAAM,CAChB,CAAC;YACJ,CAAC;iBAAM,CAAC;gBAEN,IAAI,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;YACnE,CAAC;YAGD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAE/C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,SAAS,EAAE;gBACxC,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,cAAc,EAAE,QAAQ,CAAC,cAAc;gBACvC,QAAQ,EAAE,oBAAoB;gBAC9B,OAAO,EAAE;oBACP,UAAU;oBACV,YAAY,EAAE,QAAQ,CAAC,IAAI;oBAC3B,KAAK,EAAE,WAAW,CAAC,KAAK;iBACzB;aACF,CAAC,CAAC;YAEH,OAAO;gBACL,GAAG,MAAM;gBACT,IAAI,EAAE;oBACJ,EAAE,EAAE,IAAI,CAAC,EAAE;oBACX,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,SAAS,EAAE,IAAI,CAAC,SAAS;oBACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,cAAc,EAAE,IAAI,CAAC,cAAc;iBACpC;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YAC3D,MAAM,IAAI,8BAAqB,CAAC,2BAA2B,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAKO,iBAAiB,CAAC,IAAY,EAAE,MAA2B;QACjE,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,MAAM;gBACT,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;gBAChC,MAAM;YACR,KAAK,MAAM;gBACT,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;gBAChC,MAAM;YACR,KAAK,kBAAkB;gBACrB,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAAC,CAAC;gBAC3C,MAAM;YACR;gBACE,MAAM,IAAI,4BAAmB,CAAC,2BAA2B,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAEO,kBAAkB,CAAC,MAA2B;QACpD,MAAM,QAAQ,GAAG,CAAC,YAAY,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;QAClD,KAAK,MAAM,KAAK,IAAI,QAAQ,EAAE,CAAC;YAC7B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;gBACnB,MAAM,IAAI,4BAAmB,CAAC,+BAA+B,KAAK,EAAE,CAAC,CAAC;YACxE,CAAC;QACH,CAAC;IACH,CAAC;IAEO,kBAAkB,CAAC,MAA2B;QACpD,MAAM,QAAQ,GAAG,CAAC,QAAQ,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;QACxD,KAAK,MAAM,KAAK,IAAI,QAAQ,EAAE,CAAC;YAC7B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;gBACnB,MAAM,IAAI,4BAAmB,CAAC,+BAA+B,KAAK,EAAE,CAAC,CAAC;YACxE,CAAC;QACH,CAAC;IACH,CAAC;IAEO,6BAA6B,CAAC,MAA2B;QAC/D,MAAM,QAAQ,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;QAC3D,KAAK,MAAM,KAAK,IAAI,QAAQ,EAAE,CAAC;YAC7B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;gBACnB,MAAM,IAAI,4BAAmB,CAAC,2CAA2C,KAAK,EAAE,CAAC,CAAC;YACpF,CAAC;QACH,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,wBAAwB,CACpC,OAAuB,EACvB,cAAsB,EACtB,cAAmC;QAGnC,MAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,MAAM,IAAI,EAAE,EAAE,cAAc,CAAC,CAAC;QAE3E,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YAC7B,IAAI,EAAE;gBACJ,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,EAAE;gBAClC,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,EAAE;gBAChC,IAAI;gBACJ,cAAc;gBACd,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,IAAI;gBACjB,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB;SACF,CAAC,CAAC;IACL,CAAC;IAKO,KAAK,CAAC,wBAAwB,CACpC,MAAc,EACd,OAAuB;QAEvB,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YAC7B,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;YACrB,IAAI,EAAE;gBACJ,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,SAAS;gBACzC,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,SAAS;gBACvC,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB;SACF,CAAC,CAAC;IACL,CAAC;IAKO,kBAAkB,CAAC,MAAgB,EAAE,MAA2B;QACtE,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,IAAI,EAAE,CAAC;QAG7C,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YAC7D,OAAO,QAAQ,CAAC,SAAS,CAAC;QAC5B,CAAC;QAGD,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC,SAAS,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YACjE,OAAO,QAAQ,CAAC,SAAS,CAAC;QAC5B,CAAC;QAGD,OAAO,QAAQ,CAAC,MAAM,CAAC;IACzB,CAAC;IAKO,KAAK,CAAC,cAAc,CAAC,IAAS;QACpC,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,YAAY,CAAC,CAAC;QAC/D,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,gBAAgB,EAAE,IAAI,CAAC,CAAC;QAE5E,MAAM,OAAO,GAAG;YACd,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,cAAc,EAAE,IAAI,CAAC,cAAc;SACpC,CAAC;QAEF,MAAM,WAAW,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,EAAE,SAAS,EAAE,YAAY,EAAE,CAAC,CAAC;QAC9E,MAAM,YAAY,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAEnF,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,CAAC;IACvC,CAAC;CACF,CAAA;AAzPY,gCAAU;qBAAV,UAAU;IADtB,IAAA,mBAAU,GAAE;qCAGO,8BAAa;QACN,sBAAa;QACpB,8BAAa;GAJpB,UAAU,CAyPtB"}