import { PrismaService } from '@modules/prisma/prisma.service';
import { LoggerService } from '@services/logger/logger.service';
export interface TenantContext {
    organizationId: string;
    userId?: string;
    userRole?: string;
    permissions?: string[];
}
export declare class TenantService {
    private prismaService;
    private logger;
    constructor(prismaService: PrismaService, logger: LoggerService);
    createTenantClient(context: TenantContext): {
        organization: {
            findUnique: (args: any) => any;
            findFirst: (args: any) => any;
            update: (args: any) => any;
        };
        user: {
            findMany: (args?: any) => any;
            findUnique: (args: any) => any;
            findFirst: (args: any) => any;
            create: (args: any) => any;
            update: (args: any) => any;
            delete: (args: any) => any;
        };
        agent: {
            findMany: (args?: any) => any;
            findUnique: (args: any) => any;
            create: (args: any) => any;
            update: (args: any) => any;
            delete: (args: any) => any;
        };
        agentExecution: {
            findMany: (args?: any) => any;
            create: (args: any) => any;
        };
        tool: {
            findMany: (args?: any) => any;
            findUnique: (args: any) => any;
            create: (args: any) => any;
            update: (args: any) => any;
            delete: (args: any) => any;
        };
        toolExecution: {
            findMany: (args?: any) => any;
            create: (args: any) => any;
        };
        session: {
            findMany: (args?: any) => any;
            findUnique: (args: any) => any;
            create: (args: any) => any;
            update: (args: any) => any;
            delete: (args: any) => any;
        };
        analytics: {
            findMany: (args?: any) => any;
            create: (args: any) => any;
        };
        billing: {
            findMany: (args?: any) => any;
            findFirst: (args: any) => any;
            create: (args: any) => any;
            update: (args: any) => any;
        };
    };
    validateResourceAccess(context: TenantContext, resource: string, action: string, resourceId?: string): Promise<boolean>;
    private validateSpecificResourceAccess;
    enforceOrganizationBoundary(organizationId: string, userId?: string): Promise<void>;
}
