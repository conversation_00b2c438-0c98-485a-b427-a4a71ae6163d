'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { ContextualHelp, HelpSuggestion, HelpContent } from '@/lib/help/contextual-help';
import { useAppStore } from '@/lib/store';
import {
  QuestionMarkCircleIcon,
  XMarkIcon,
  MagnifyingGlassIcon,
  HandThumbUpIcon,
  HandThumbDownIcon,
  SparklesIcon,
  LightBulbIcon,
  ExclamationTriangleIcon,
  BookOpenIcon,
  PlayIcon,
  ChevronRightIcon,
  ChatBubbleLeftRightIcon,
} from '@heroicons/react/24/outline';

interface HelpWidgetProps {
  helpEngine: ContextualHelp;
  className?: string;
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  autoShow?: boolean;
}

export function HelpWidget({
  helpEngine,
  className,
  position = 'bottom-right',
  autoShow = false,
}: HelpWidgetProps) {
  const [isOpen, setIsOpen] = useState(autoShow);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [suggestions, setSuggestions] = useState<HelpSuggestion[]>([]);
  const [searchResults, setSearchResults] = useState<HelpContent[]>([]);
  const [activeTab, setActiveTab] = useState<'suggestions' | 'search' | 'tips'>('suggestions');
  const [tips, setTips] = useState<HelpContent[]>([]);
  const [expandedContent, setExpandedContent] = useState<string | null>(null);

  const widgetRef = useRef<HTMLDivElement>(null);
  const { addNotification } = useAppStore();

  useEffect(() => {
    // Set up help engine event listeners
    const handleProactiveHelp = (data: { suggestions: HelpSuggestion[] }) => {
      setSuggestions(data.suggestions);
      if (data.suggestions.some(s => s.content.priority === 'critical')) {
        setIsOpen(true);
        addNotification({
          type: 'info',
          title: 'Help Available',
          message: 'We noticed you might need assistance',
        });
      }
    };

    helpEngine.on('proactive_help_available', handleProactiveHelp);

    // Load initial suggestions and tips
    loadSuggestions();
    loadTips();

    return () => {
      helpEngine.off('proactive_help_available', handleProactiveHelp);
    };
  }, [helpEngine, addNotification]);

  const loadSuggestions = async () => {
    setIsLoading(true);
    try {
      const newSuggestions = await helpEngine.getHelp();
      setSuggestions(newSuggestions);
    } catch (error) {
      console.error('Failed to load help suggestions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadTips = async () => {
    try {
      const newTips = await helpEngine.getPersonalizedTips();
      setTips(newTips);
    } catch (error) {
      console.error('Failed to load tips:', error);
    }
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) return;

    setIsLoading(true);
    try {
      const results = await helpEngine.searchHelp(searchQuery);
      setSearchResults(results);
      setActiveTab('search');
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Search Failed',
        message: 'Could not search help content',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleHelpful = (contentId: string, helpful: boolean) => {
    helpEngine.markHelpful(contentId, helpful);
    addNotification({
      type: 'success',
      title: 'Feedback Recorded',
      message: helpful ? 'Thanks for the feedback!' : 'We\'ll improve this content',
    });
  };

  const handleContentAction = (action: string, data?: any) => {
    switch (action) {
      case 'highlight_required_fields':
        // Highlight required fields on the page
        document.querySelectorAll('[required]').forEach(el => {
          el.classList.add('border-error-500', 'animate-pulse');
          setTimeout(() => {
            el.classList.remove('border-error-500', 'animate-pulse');
          }, 3000);
        });
        break;
      
      case 'retry_connection':
        // Trigger a connection retry
        window.location.reload();
        break;
      
      case 'start_tutorial':
        // Start a specific tutorial
        addNotification({
          type: 'info',
          title: 'Tutorial Starting',
          message: 'Loading interactive tutorial...',
        });
        break;
      
      default:
        console.log('Unknown action:', action, data);
    }
  };

  const getContentTypeIcon = (type: HelpContent['type']) => {
    switch (type) {
      case 'explanation': return <BookOpenIcon className="h-4 w-4" />;
      case 'tutorial': return <PlayIcon className="h-4 w-4" />;
      case 'troubleshooting': return <ExclamationTriangleIcon className="h-4 w-4" />;
      case 'reference': return <BookOpenIcon className="h-4 w-4" />;
      case 'tip': return <LightBulbIcon className="h-4 w-4" />;
      default: return <QuestionMarkCircleIcon className="h-4 w-4" />;
    }
  };

  const getContentTypeColor = (type: HelpContent['type']) => {
    switch (type) {
      case 'explanation': return 'info';
      case 'tutorial': return 'success';
      case 'troubleshooting': return 'warning';
      case 'reference': return 'secondary';
      case 'tip': return 'info';
      default: return 'secondary';
    }
  };

  const getPriorityColor = (priority: HelpContent['priority']) => {
    switch (priority) {
      case 'critical': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'secondary';
      default: return 'secondary';
    }
  };

  const getPositionClasses = () => {
    switch (position) {
      case 'bottom-right': return 'bottom-6 right-6';
      case 'bottom-left': return 'bottom-6 left-6';
      case 'top-right': return 'top-6 right-6';
      case 'top-left': return 'top-6 left-6';
      default: return 'bottom-6 right-6';
    }
  };

  const renderHelpContent = (content: HelpContent, suggestion?: HelpSuggestion) => (
    <div
      key={content.id}
      className="border border-gray-200 rounded-lg p-4 space-y-3"
    >
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-2 flex-1">
          <div className="mt-0.5">
            {getContentTypeIcon(content.type)}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-1">
              <h4 className="font-medium text-sm text-gray-900 truncate">
                {content.title}
              </h4>
              <Badge variant={getContentTypeColor(content.type)} size="sm">
                {content.type}
              </Badge>
              {content.priority !== 'low' && (
                <Badge variant={getPriorityColor(content.priority)} size="sm">
                  {content.priority}
                </Badge>
              )}
            </div>
            
            <p className="text-xs text-gray-600 mb-2">
              {expandedContent === content.id ? content.content : 
               content.content.length > 100 ? 
               `${content.content.substring(0, 100)}...` : 
               content.content}
            </p>

            {content.content.length > 100 && (
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setExpandedContent(
                  expandedContent === content.id ? null : content.id
                )}
                className="text-xs p-0 h-auto"
              >
                {expandedContent === content.id ? 'Show less' : 'Show more'}
              </Button>
            )}

            {suggestion && (
              <div className="text-xs text-blue-600 italic mt-1">
                {suggestion.reasoning}
              </div>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-1">
          <Badge variant="outline" size="sm">
            {Math.round(content.confidence * 100)}%
          </Badge>
        </div>
      </div>

      {/* Media */}
      {content.media && (
        <div className="rounded overflow-hidden">
          {content.media.type === 'image' && (
            <img
              src={content.media.url}
              alt={content.media.alt}
              className="w-full h-auto max-h-32 object-cover"
            />
          )}
          {content.media.type === 'video' && (
            <video
              src={content.media.url}
              controls
              className="w-full h-auto max-h-32"
            />
          )}
        </div>
      )}

      {/* Actions */}
      {content.actions && content.actions.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {content.actions.map((action, index) => (
            <Button
              key={index}
              size="sm"
              variant="outline"
              onClick={() => handleContentAction(action.action, action.data)}
              leftIcon={<ChevronRightIcon className="h-3 w-3" />}
            >
              {action.label}
            </Button>
          ))}
        </div>
      )}

      {/* Tags */}
      {content.tags.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {content.tags.slice(0, 3).map((tag, index) => (
            <Badge key={index} variant="outline" size="sm">
              {tag}
            </Badge>
          ))}
          {content.tags.length > 3 && (
            <Badge variant="outline" size="sm">
              +{content.tags.length - 3}
            </Badge>
          )}
        </div>
      )}

      {/* Feedback */}
      <div className="flex items-center justify-between pt-2 border-t border-gray-100">
        <div className="text-xs text-gray-500">
          Was this helpful?
        </div>
        <div className="flex items-center space-x-1">
          <Button
            size="sm"
            variant="ghost"
            onClick={() => handleHelpful(content.id, true)}
            leftIcon={<HandThumbUpIcon className="h-3 w-3" />}
          />
          <Button
            size="sm"
            variant="ghost"
            onClick={() => handleHelpful(content.id, false)}
            leftIcon={<HandThumbDownIcon className="h-3 w-3" />}
          />
        </div>
      </div>
    </div>
  );

  if (!isOpen) {
    return (
      <div className={`fixed ${getPositionClasses()} z-50 ${className}`}>
        <Button
          onClick={() => setIsOpen(true)}
          className="rounded-full w-12 h-12 shadow-lg"
          leftIcon={<QuestionMarkCircleIcon className="h-6 w-6" />}
        />
      </div>
    );
  }

  return (
    <div className={`fixed ${getPositionClasses()} z-50 ${className}`}>
      <Card ref={widgetRef} className="w-96 max-h-96 shadow-xl">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <ChatBubbleLeftRightIcon className="h-5 w-5 text-primary-600" />
              <span>Help Assistant</span>
              {suggestions.some(s => s.content.source === 'ai') && (
                <SparklesIcon className="h-4 w-4 text-primary-600" />
              )}
            </CardTitle>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setIsOpen(false)}
              leftIcon={<XMarkIcon className="h-4 w-4" />}
            />
          </div>

          {/* Search */}
          <div className="flex space-x-2">
            <Input
              placeholder="Search for help..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              leftIcon={<MagnifyingGlassIcon className="h-4 w-4" />}
              className="flex-1"
            />
            <Button
              size="sm"
              onClick={handleSearch}
              loading={isLoading}
              disabled={!searchQuery.trim()}
            >
              Search
            </Button>
          </div>

          {/* Tabs */}
          <div className="flex space-x-1">
            {['suggestions', 'search', 'tips'].map((tab) => (
              <Button
                key={tab}
                size="sm"
                variant={activeTab === tab ? 'default' : 'ghost'}
                onClick={() => setActiveTab(tab as any)}
                className="capitalize"
              >
                {tab}
                {tab === 'suggestions' && suggestions.length > 0 && (
                  <Badge variant="secondary" size="sm" className="ml-1">
                    {suggestions.length}
                  </Badge>
                )}
              </Button>
            ))}
          </div>
        </CardHeader>

        <CardContent className="space-y-3 max-h-64 overflow-y-auto">
          {/* Loading State */}
          {isLoading && (
            <div className="flex items-center justify-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600" />
              <span className="ml-2 text-sm text-gray-600">Loading help...</span>
            </div>
          )}

          {/* Suggestions Tab */}
          {activeTab === 'suggestions' && !isLoading && (
            <div className="space-y-3">
              {suggestions.length > 0 ? (
                suggestions.map((suggestion) => 
                  renderHelpContent(suggestion.content, suggestion)
                )
              ) : (
                <div className="text-center py-4 text-gray-500">
                  <QuestionMarkCircleIcon className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No suggestions available</p>
                  <p className="text-xs">Try searching for specific topics</p>
                </div>
              )}
            </div>
          )}

          {/* Search Tab */}
          {activeTab === 'search' && !isLoading && (
            <div className="space-y-3">
              {searchResults.length > 0 ? (
                searchResults.map((content) => renderHelpContent(content))
              ) : searchQuery ? (
                <div className="text-center py-4 text-gray-500">
                  <MagnifyingGlassIcon className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No results found</p>
                  <p className="text-xs">Try different keywords</p>
                </div>
              ) : (
                <div className="text-center py-4 text-gray-500">
                  <MagnifyingGlassIcon className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">Search help content</p>
                  <p className="text-xs">Enter keywords above to find help</p>
                </div>
              )}
            </div>
          )}

          {/* Tips Tab */}
          {activeTab === 'tips' && !isLoading && (
            <div className="space-y-3">
              {tips.length > 0 ? (
                tips.map((tip) => renderHelpContent(tip))
              ) : (
                <div className="text-center py-4 text-gray-500">
                  <LightBulbIcon className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No tips available</p>
                  <p className="text-xs">Tips will appear based on your usage</p>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
