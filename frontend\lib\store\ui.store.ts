import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

interface UIState {
  // Theme
  theme: 'light' | 'dark' | 'system';
  
  // Layout
  sidebarCollapsed: boolean;
  sidebarWidth: number;
  
  // Modals and overlays
  modals: {
    [key: string]: {
      isOpen: boolean;
      data?: any;
    };
  };
  
  // Notifications
  notifications: Array<{
    id: string;
    type: 'success' | 'error' | 'warning' | 'info';
    title: string;
    message?: string;
    timestamp: number;
    read: boolean;
    persistent?: boolean;
  }>;
  
  // Loading states
  loadingStates: {
    [key: string]: boolean;
  };
  
  // Page preferences
  pagePreferences: {
    [page: string]: {
      view?: 'grid' | 'list' | 'table';
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
      filters?: Record<string, any>;
      pageSize?: number;
    };
  };
}

interface UIStore extends UIState {
  // Theme actions
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
  
  // Layout actions
  toggleSidebar: () => void;
  setSidebarCollapsed: (collapsed: boolean) => void;
  setSidebarWidth: (width: number) => void;
  
  // Modal actions
  openModal: (modalId: string, data?: any) => void;
  closeModal: (modalId: string) => void;
  closeAllModals: () => void;
  
  // Notification actions
  addNotification: (notification: Omit<UIState['notifications'][0], 'id' | 'timestamp' | 'read'>) => void;
  removeNotification: (id: string) => void;
  markNotificationAsRead: (id: string) => void;
  markAllNotificationsAsRead: () => void;
  clearNotifications: () => void;
  
  // Loading actions
  setLoading: (key: string, loading: boolean) => void;
  clearAllLoading: () => void;
  
  // Page preference actions
  setPagePreference: (page: string, preferences: Partial<UIState['pagePreferences'][string]>) => void;
  clearPagePreferences: (page?: string) => void;
}

export const useUIStore = create<UIStore>()(
  persist(
    (set, get) => ({
      // Initial state
      theme: 'system',
      sidebarCollapsed: false,
      sidebarWidth: 280,
      modals: {},
      notifications: [],
      loadingStates: {},
      pagePreferences: {},

      // Theme actions
      setTheme: (theme) => set({ theme }),

      // Layout actions
      toggleSidebar: () => set((state) => ({ 
        sidebarCollapsed: !state.sidebarCollapsed 
      })),
      
      setSidebarCollapsed: (sidebarCollapsed) => set({ sidebarCollapsed }),
      
      setSidebarWidth: (sidebarWidth) => set({ sidebarWidth }),

      // Modal actions
      openModal: (modalId, data) => set((state) => ({
        modals: {
          ...state.modals,
          [modalId]: { isOpen: true, data }
        }
      })),
      
      closeModal: (modalId) => set((state) => ({
        modals: {
          ...state.modals,
          [modalId]: { isOpen: false, data: undefined }
        }
      })),
      
      closeAllModals: () => set({ modals: {} }),

      // Notification actions
      addNotification: (notification) => {
        const id = `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        set((state) => ({
          notifications: [
            {
              ...notification,
              id,
              timestamp: Date.now(),
              read: false,
            },
            ...state.notifications,
          ].slice(0, 50) // Keep only last 50 notifications
        }));
      },
      
      removeNotification: (id) => set((state) => ({
        notifications: state.notifications.filter(n => n.id !== id)
      })),
      
      markNotificationAsRead: (id) => set((state) => ({
        notifications: state.notifications.map(n => 
          n.id === id ? { ...n, read: true } : n
        )
      })),
      
      markAllNotificationsAsRead: () => set((state) => ({
        notifications: state.notifications.map(n => ({ ...n, read: true }))
      })),
      
      clearNotifications: () => set({ notifications: [] }),

      // Loading actions
      setLoading: (key, loading) => set((state) => ({
        loadingStates: {
          ...state.loadingStates,
          [key]: loading
        }
      })),
      
      clearAllLoading: () => set({ loadingStates: {} }),

      // Page preference actions
      setPagePreference: (page, preferences) => set((state) => ({
        pagePreferences: {
          ...state.pagePreferences,
          [page]: {
            ...state.pagePreferences[page],
            ...preferences
          }
        }
      })),
      
      clearPagePreferences: (page) => set((state) => {
        if (page) {
          const { [page]: removed, ...rest } = state.pagePreferences;
          return { pagePreferences: rest };
        }
        return { pagePreferences: {} };
      }),
    }),
    {
      name: 'synapseai-ui-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        theme: state.theme,
        sidebarCollapsed: state.sidebarCollapsed,
        sidebarWidth: state.sidebarWidth,
        pagePreferences: state.pagePreferences,
      }),
      version: 1,
    }
  )
);
