"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/[...nextauth]/route";
exports.ids = ["app/api/auth/[...nextauth]/route"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5Claragon%5Cwww%5Cmax%5Ctrae%5Ckilo-teset%5CaugsyanapseAI%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5Cmax%5Ctrae%5Ckilo-teset%5CaugsyanapseAI%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5Claragon%5Cwww%5Cmax%5Ctrae%5Ckilo-teset%5CaugsyanapseAI%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5Cmax%5Ctrae%5Ckilo-teset%5CaugsyanapseAI%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/../node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/../node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_laragon_www_max_trae_kilo_teset_augsyanapseAI_frontend_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/auth/[...nextauth]/route.ts */ \"(rsc)/./app/api/auth/[...nextauth]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/[...nextauth]/route\",\n        pathname: \"/api/auth/[...nextauth]\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/[...nextauth]/route\"\n    },\n    resolvedPagePath: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\api\\\\auth\\\\[...nextauth]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_laragon_www_max_trae_kilo_teset_augsyanapseAI_frontend_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/auth/[...nextauth]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5Claragon%5Cwww%5Cmax%5Ctrae%5Ckilo-teset%5CaugsyanapseAI%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5Cmax%5Ctrae%5Ckilo-teset%5CaugsyanapseAI%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/auth/[...nextauth]/route.ts":
/*!*********************************************!*\
  !*** ./app/api/auth/[...nextauth]/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/../node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n\n\nconst handler = next_auth__WEBPACK_IMPORTED_MODULE_0___default()(_lib_auth__WEBPACK_IMPORTED_MODULE_1__.authOptions);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL2F1dGgvWy4uLm5leHRhdXRoXS9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFpQztBQUNRO0FBRXpDLE1BQU1FLFVBQVVGLGdEQUFRQSxDQUFDQyxrREFBV0E7QUFFTyIsInNvdXJjZXMiOlsid2VicGFjazovL3N5bmFwc2VhaS1mcm9udGVuZC8uL2FwcC9hcGkvYXV0aC9bLi4ubmV4dGF1dGhdL3JvdXRlLnRzP2M4YTQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IE5leHRBdXRoIGZyb20gJ25leHQtYXV0aCc7XG5pbXBvcnQgeyBhdXRoT3B0aW9ucyB9IGZyb20gJ0AvbGliL2F1dGgnO1xuXG5jb25zdCBoYW5kbGVyID0gTmV4dEF1dGgoYXV0aE9wdGlvbnMpO1xuXG5leHBvcnQgeyBoYW5kbGVyIGFzIEdFVCwgaGFuZGxlciBhcyBQT1NUIH07XG4iXSwibmFtZXMiOlsiTmV4dEF1dGgiLCJhdXRoT3B0aW9ucyIsImhhbmRsZXIiLCJHRVQiLCJQT1NUIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/api/auth/[...nextauth]/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/../node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/../node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/../node_modules/next-auth/providers/github.js\");\n\n\n\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                },\n                organizationId: {\n                    label: \"Organization ID\",\n                    type: \"text\",\n                    optional: true\n                },\n                twoFactorCode: {\n                    label: \"2FA Code\",\n                    type: \"text\",\n                    optional: true\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                try {\n                    const response = await fetch(`${\"http://localhost:3000\"}/api/v1/auth/login`, {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            email: credentials.email,\n                            password: credentials.password,\n                            organizationId: credentials.organizationId,\n                            twoFactorCode: credentials.twoFactorCode\n                        })\n                    });\n                    if (!response.ok) {\n                        const error = await response.json();\n                        throw new Error(error.message || \"Authentication failed\");\n                    }\n                    const data = await response.json();\n                    return {\n                        id: data.user.id,\n                        email: data.user.email,\n                        firstName: data.user.firstName,\n                        lastName: data.user.lastName,\n                        role: data.user.role,\n                        organizationId: data.user.organizationId,\n                        organizationName: data.user.organizationName,\n                        organizationRole: data.user.organizationRole,\n                        permissions: data.user.permissions,\n                        avatar: data.user.avatar,\n                        emailVerified: data.user.emailVerified,\n                        twoFactorEnabled: data.user.twoFactorEnabled,\n                        accessToken: data.accessToken,\n                        refreshToken: data.refreshToken,\n                        lastLoginAt: data.user.lastLoginAt,\n                        createdAt: data.user.createdAt\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    return null;\n                }\n            }\n        }),\n        // Google OAuth Provider\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET,\n            authorization: {\n                params: {\n                    prompt: \"consent\",\n                    access_type: \"offline\",\n                    response_type: \"code\"\n                }\n            }\n        }),\n        // GitHub OAuth Provider\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GITHUB_CLIENT_ID,\n            clientSecret: process.env.GITHUB_CLIENT_SECRET\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, user, account }) {\n            // Initial sign in\n            if (account && user) {\n                return {\n                    ...token,\n                    accessToken: user.accessToken,\n                    refreshToken: user.refreshToken,\n                    user: {\n                        id: user.id,\n                        email: user.email,\n                        firstName: user.firstName,\n                        lastName: user.lastName,\n                        role: user.role,\n                        organizationId: user.organizationId\n                    }\n                };\n            }\n            // Return previous token if the access token has not expired yet\n            if (Date.now() < token.accessTokenExpires) {\n                return token;\n            }\n            // Access token has expired, try to update it\n            return refreshAccessToken(token);\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user = token.user;\n                session.accessToken = token.accessToken;\n                session.error = token.error;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        error: \"/auth/error\"\n    },\n    session: {\n        strategy: \"jwt\",\n        maxAge: 24 * 60 * 60\n    },\n    jwt: {\n        maxAge: 24 * 60 * 60\n    },\n    secret: process.env.NEXTAUTH_SECRET || \"fallback-secret-for-development\"\n};\nasync function refreshAccessToken(token) {\n    try {\n        const response = await fetch(`${\"http://localhost:3000\"}/api/v1/auth/refresh`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                refreshToken: token.refreshToken\n            })\n        });\n        const refreshedTokens = await response.json();\n        if (!response.ok) {\n            throw refreshedTokens;\n        }\n        return {\n            ...token,\n            accessToken: refreshedTokens.accessToken,\n            refreshToken: refreshedTokens.refreshToken ?? token.refreshToken,\n            accessTokenExpires: Date.now() + refreshedTokens.expiresIn * 1000\n        };\n    } catch (error) {\n        console.error(\"Error refreshing access token:\", error);\n        return {\n            ...token,\n            error: \"RefreshAccessTokenError\"\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/uuid","vendor-chunks/oauth","vendor-chunks/@panva","vendor-chunks/preact-render-to-string","vendor-chunks/oidc-token-hash","vendor-chunks/preact","vendor-chunks/object-hash","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5Claragon%5Cwww%5Cmax%5Ctrae%5Ckilo-teset%5CaugsyanapseAI%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5Cmax%5Ctrae%5Ckilo-teset%5CaugsyanapseAI%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();