'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { configAssistant, ConfigSuggestion, ConfigContext } from '@/lib/ai/config-assistant';
import {
  SparklesIcon,
  LightBulbIcon,
  CheckIcon,
  XMarkIcon,
  ChatBubbleLeftRightIcon,
} from '@heroicons/react/24/outline';

interface ConfigAssistantProps {
  context: ConfigContext;
  onApplySuggestion: (suggestion: ConfigSuggestion) => void;
  onConfigChange: (config: Record<string, any>) => void;
  className?: string;
}

export function ConfigAssistant({
  context,
  onApplySuggestion,
  onConfigChange,
  className,
}: ConfigAssistantProps) {
  const [suggestions, setSuggestions] = useState<ConfigSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [userInput, setUserInput] = useState('');
  const [isExpanded, setIsExpanded] = useState(false);
  const [appliedSuggestions, setAppliedSuggestions] = useState<Set<string>>(new Set());

  useEffect(() => {
    loadSuggestions();
  }, [context.currentConfig, context.domain]);

  const loadSuggestions = async () => {
    setIsLoading(true);
    try {
      const response = await configAssistant.getSuggestions(context);
      setSuggestions(response.suggestions);
    } catch (error) {
      console.error('Failed to load suggestions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleApplySuggestion = (suggestion: ConfigSuggestion) => {
    onApplySuggestion(suggestion);
    setAppliedSuggestions(prev => new Set([...prev, suggestion.id]));
  };

  const handleDismissSuggestion = (suggestionId: string) => {
    setSuggestions(prev => prev.filter(s => s.id !== suggestionId));
  };

  const handleNaturalLanguageInput = async () => {
    if (!userInput.trim()) return;

    setIsLoading(true);
    try {
      const intentAnalysis = await configAssistant.analyzeIntent(userInput, context);
      
      if (intentAnalysis.intent === 'create' || intentAnalysis.intent === 'generate') {
        const generated = await configAssistant.generateFromDescription(
          userInput,
          context.domain,
        );
        onConfigChange(generated.config);
      } else {
        // Get new suggestions based on the input
        const response = await configAssistant.getSuggestions({
          ...context,
          userIntent: userInput,
        });
        setSuggestions(response.suggestions);
      }
      
      setUserInput('');
    } catch (error) {
      console.error('Failed to process natural language input:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'info';
      default: return 'secondary';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'optimization': return <SparklesIcon className="h-4 w-4" />;
      case 'field': return <LightBulbIcon className="h-4 w-4" />;
      default: return <ChatBubbleLeftRightIcon className="h-4 w-4" />;
    }
  };

  const activeSuggestions = suggestions.filter(s => !appliedSuggestions.has(s.id));

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <SparklesIcon className="h-5 w-5 text-primary-600" />
            <span>AI Assistant</span>
            {activeSuggestions.length > 0 && (
              <Badge variant="info" size="sm">
                {activeSuggestions.length} suggestion{activeSuggestions.length !== 1 ? 's' : ''}
              </Badge>
            )}
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? 'Collapse' : 'Expand'}
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Natural Language Input */}
        <div className="flex space-x-2">
          <Input
            placeholder="Describe what you want to build..."
            value={userInput}
            onChange={(e) => setUserInput(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleNaturalLanguageInput()}
          />
          <Button
            onClick={handleNaturalLanguageInput}
            loading={isLoading}
            disabled={!userInput.trim() || isLoading}
          >
            Ask AI
          </Button>
        </div>

        {/* Suggestions */}
        {isExpanded && (
          <div className="space-y-3">
            {isLoading && (
              <div className="flex items-center justify-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600" />
                <span className="ml-2 text-sm text-gray-600">Getting suggestions...</span>
              </div>
            )}

            {activeSuggestions.length === 0 && !isLoading && (
              <div className="text-center py-4 text-gray-500">
                <LightBulbIcon className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No suggestions available</p>
                <p className="text-xs">Try asking the AI for help or make changes to see suggestions</p>
              </div>
            )}

            {activeSuggestions.map((suggestion) => (
              <div
                key={suggestion.id}
                className="border border-gray-200 rounded-lg p-3 space-y-2"
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-2">
                    <div className="mt-0.5">
                      {getTypeIcon(suggestion.type)}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-sm">{suggestion.title}</h4>
                      <p className="text-xs text-gray-600 mt-1">{suggestion.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Badge variant={getImpactColor(suggestion.impact)} size="sm">
                      {suggestion.impact}
                    </Badge>
                    <span className="text-xs text-gray-500">
                      {Math.round(suggestion.confidence * 100)}%
                    </span>
                  </div>
                </div>

                {suggestion.reasoning && (
                  <p className="text-xs text-gray-500 italic">
                    {suggestion.reasoning}
                  </p>
                )}

                {suggestion.examples && suggestion.examples.length > 0 && (
                  <div className="text-xs">
                    <span className="font-medium">Examples: </span>
                    <span className="text-gray-600">
                      {suggestion.examples.join(', ')}
                    </span>
                  </div>
                )}

                <div className="flex items-center justify-between pt-2">
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      onClick={() => handleApplySuggestion(suggestion)}
                      leftIcon={<CheckIcon className="h-3 w-3" />}
                    >
                      Apply
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleDismissSuggestion(suggestion.id)}
                      leftIcon={<XMarkIcon className="h-3 w-3" />}
                    >
                      Dismiss
                    </Button>
                  </div>
                  <Badge variant="secondary" size="sm">
                    {suggestion.category}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Quick Actions */}
        {!isExpanded && activeSuggestions.length > 0 && (
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">
              {activeSuggestions.length} AI suggestion{activeSuggestions.length !== 1 ? 's' : ''} available
            </span>
            <Button
              size="sm"
              variant="outline"
              onClick={() => setIsExpanded(true)}
            >
              View All
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
