{"name": "synapseai-backend", "version": "1.0.0", "description": "SynapseAI Backend - NESTJS TypeScript Microservices", "main": "dist/main.js", "scripts": {"dev": "nest start --watch", "dev:backend": "nest start --watch", "build": "nest build", "start": "node dist/main.js", "start:prod": "node dist/main", "test": "jest", "test:watch": "jest --watch", "test:e2e": "jest --config ./test/jest-e2e.json", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "type-check": "tsc --noEmit", "db:migrate": "prisma migrate dev", "db:migrate:deploy": "prisma migrate deploy", "db:seed": "prisma db seed", "db:studio": "prisma studio", "db:generate": "prisma generate", "db:validate": "prisma validate", "db:reset": "prisma migrate reset", "db:status": "prisma migrate status"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "dependencies": {"@casl/ability": "^6.7.3", "@casl/prisma": "^1.5.1", "@nestjs/bull": "^10.0.1", "@nestjs/common": "^10.2.8", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.2.8", "@nestjs/jwt": "^10.2.0", "@nestjs/microservices": "^10.2.8", "@nestjs/passport": "^10.0.2", "@nestjs/platform-express": "^10.2.8", "@nestjs/platform-socket.io": "^10.2.8", "@nestjs/swagger": "^7.1.16", "@nestjs/terminus": "^10.1.1", "@nestjs/websockets": "^10.2.8", "@prisma/client": "^6.12.0", "@sentry/node": "^7.81.1", "@sentry/profiling-node": "^1.3.2", "bcryptjs": "^2.4.3", "bull": "^4.12.2", "bullmq": "^4.15.4", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "compression": "^1.7.4", "helmet": "^7.1.0", "ioredis": "^5.3.2", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "socket.io": "^4.7.4", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"@nestjs/cli": "^10.2.1", "@nestjs/schematics": "^10.0.3", "@nestjs/testing": "^10.2.8", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/express": "^4.17.21", "@types/node": "^20.8.10", "@types/passport-jwt": "^3.0.13", "@types/passport-local": "^1.0.38", "@typescript-eslint/eslint-plugin": "^6.9.1", "@typescript-eslint/parser": "^6.9.1", "eslint": "^8.52.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "prettier": "^3.0.3", "prisma": "^6.12.0", "ts-loader": "^9.5.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.2.2"}}