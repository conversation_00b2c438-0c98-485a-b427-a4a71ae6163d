// SynapseAI Database Schema
// Production-ready Prisma schema for universal AI orchestration platform

generator client {
  provider = "prisma-client-js"
  output   = "../node_modules/.prisma/client"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ============================================================================
// 2.1.1 Core Tables (Organizations, Users, Roles, Sessions)
// ============================================================================

model Organization {
  id        String   @id @default(cuid())
  name      String
  slug      String   @unique
  settings  Json     @default("{}")
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  users                 User[]
  roles                 Role[]
  sessions              Session[]
  templates             Template[]
  agents                Agent[]
  tools                 Tool[]
  hybrids               Hybrid[]
  workflows             Workflow[]
  providers             Provider[]
  providerUsage         ProviderUsage[]
  hitlRequests          HITLRequest[]
  documents             Document[]
  widgets               Widget[]
  analytics             Analytics[]
  metrics               Metrics[]
  billing               Billing[]
  usageMeters           UsageMeter[]
  quotas                Quota[]
  notifications         Notification[]
  notificationPrefs     NotificationPreference[]
  sandboxes             Sandbox[]
  agentExecutions       AgentExecution[]
  toolExecutions        ToolExecution[]
  hybridExecutions      HybridExecution[]
  workflowExecutions    WorkflowExecution[]
  widgetExecutions      WidgetExecution[]
  knowledgeSearches     KnowledgeSearch[]
  testResults           TestResult[]
  apiKeys               ApiKey[]

  @@map("organizations")
  @@index([slug])
  @@index([isActive])
  @@index([createdAt])
}

model User {
  id             String    @id @default(cuid())
  email          String
  passwordHash   String?
  firstName      String?
  lastName       String?
  role           UserRole  @default(VIEWER)
  organizationId String
  isActive       Boolean   @default(true)
  lastLoginAt    DateTime?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt

  // Relationships
  organization          Organization            @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  sessions              Session[]
  createdTemplates      Template[]              @relation("TemplateCreator")
  createdAgents         Agent[]                 @relation("AgentCreator")
  createdTools          Tool[]                  @relation("ToolCreator")
  createdHybrids        Hybrid[]                @relation("HybridCreator")
  createdWorkflows      Workflow[]              @relation("WorkflowCreator")
  assignedHitlRequests  HITLRequest[]           @relation("HITLAssignee")
  decidedHitlRequests   HITLDecision[]          @relation("HITLDecider")
  uploadedDocuments     Document[]              @relation("DocumentUploader")
  createdWidgets        Widget[]                @relation("WidgetCreator")
  createdSandboxes      Sandbox[]               @relation("SandboxCreator")
  notificationPrefs     NotificationPreference[]
  receivedNotifications Notification[]          @relation("NotificationRecipient")
  createdApiKeys        ApiKey[]                @relation("ApiKeyCreator")

  @@unique([email, organizationId])
  @@map("users")
  @@index([organizationId])
  @@index([email])
  @@index([role])
  @@index([isActive])
  @@index([lastLoginAt])
}

model Role {
  id             String   @id @default(cuid())
  name           String
  permissions    Json     @default("[]")
  organizationId String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relationships
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@unique([name, organizationId])
  @@map("roles")
  @@index([organizationId])
}

model Session {
  id             String    @id @default(cuid())
  userId         String?
  organizationId String
  agentId        String?
  toolId         String?
  workflowId     String?
  context        Json      @default("{}")
  expiresAt      DateTime
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt

  // Relationships
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user         User?        @relation(fields: [userId], references: [id], onDelete: SetNull)
  agent        Agent?       @relation(fields: [agentId], references: [id], onDelete: SetNull)
  tool         Tool?        @relation(fields: [toolId], references: [id], onDelete: SetNull)
  workflow     Workflow?    @relation(fields: [workflowId], references: [id], onDelete: SetNull)

  @@map("sessions")
  @@index([organizationId])
  @@index([userId])
  @@index([agentId])
  @@index([toolId])
  @@index([workflowId])
  @@index([expiresAt])
  @@index([createdAt])
}

// ============================================================================
// 2.1.2 Template System Tables (Templates, TemplateVersions)
// ============================================================================

model Template {
  id             String   @id @default(cuid())
  name           String
  description    String?
  category       String
  content        String
  variables      Json     @default("[]")
  metadata       Json     @default("{}")
  isPublic       Boolean  @default(false)
  organizationId String
  createdBy      String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relationships
  organization Organization      @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  creator      User              @relation("TemplateCreator", fields: [createdBy], references: [id], onDelete: Restrict)
  versions     TemplateVersion[]
  agents       Agent[]

  @@map("templates")
  @@index([organizationId])
  @@index([createdBy])
  @@index([category])
  @@index([isPublic])
  @@index([createdAt])
}

model TemplateVersion {
  id         String   @id @default(cuid())
  templateId String
  version    String
  content    String
  variables  Json     @default("[]")
  changelog  String?
  isActive   Boolean  @default(false)
  createdAt  DateTime @default(now())

  // Relationships
  template Template @relation(fields: [templateId], references: [id], onDelete: Cascade)

  @@unique([templateId, version])
  @@map("template_versions")
  @@index([templateId])
  @@index([isActive])
  @@index([createdAt])
}

// ============================================================================
// Enums
// ============================================================================

enum UserRole {
  SUPER_ADMIN
  ORG_ADMIN
  DEVELOPER
  VIEWER
}

enum ExecutionStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED
  CANCELLED
}

enum ApprovalStatus {
  PENDING
  APPROVED
  REJECTED
  EXPIRED
}

enum NotificationChannel {
  EMAIL
  SMS
  PUSH
  IN_APP
  WEBHOOK
}

// ============================================================================
// 2.1.3 Agent System Tables (Agents, AgentExecutions)
// ============================================================================

model Agent {
  id             String   @id @default(cuid())
  name           String
  description    String?
  templateId     String?
  configuration  Json     @default("{}")
  isActive       Boolean  @default(true)
  organizationId String
  createdBy      String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relationships
  organization Organization     @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  creator      User             @relation("AgentCreator", fields: [createdBy], references: [id], onDelete: Restrict)
  template     Template?        @relation(fields: [templateId], references: [id], onDelete: SetNull)
  executions   AgentExecution[]
  sessions     Session[]
  hybrids      Hybrid[]

  @@map("agents")
  @@index([organizationId])
  @@index([createdBy])
  @@index([templateId])
  @@index([isActive])
  @@index([createdAt])
}

model AgentExecution {
  id             String          @id @default(cuid())
  agentId        String
  sessionId      String?
  input          Json
  output         Json?
  status         ExecutionStatus @default(PENDING)
  startedAt      DateTime        @default(now())
  completedAt    DateTime?
  duration       Int?            // milliseconds
  errorMessage   String?
  metadata       Json            @default("{}")
  organizationId String

  // Relationships
  agent        Agent        @relation(fields: [agentId], references: [id], onDelete: Cascade)
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@map("agent_executions")
  @@index([agentId])
  @@index([organizationId])
  @@index([sessionId])
  @@index([status])
  @@index([startedAt])
  @@index([completedAt])
}

// ============================================================================
// 2.1.4 Tool System Tables (Tools, ToolExecutions)
// ============================================================================

model Tool {
  id             String   @id @default(cuid())
  name           String
  description    String?
  type           String   // API, FUNCTION, WEBHOOK, etc.
  configuration  Json     @default("{}")
  schema         Json     @default("{}")
  isActive       Boolean  @default(true)
  organizationId String
  createdBy      String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relationships
  organization Organization    @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  creator      User            @relation("ToolCreator", fields: [createdBy], references: [id], onDelete: Restrict)
  executions   ToolExecution[]
  sessions     Session[]

  @@map("tools")
  @@index([organizationId])
  @@index([createdBy])
  @@index([type])
  @@index([isActive])
  @@index([createdAt])
}

model ToolExecution {
  id             String          @id @default(cuid())
  toolId         String
  sessionId      String?
  input          Json
  output         Json?
  status         ExecutionStatus @default(PENDING)
  startedAt      DateTime        @default(now())
  completedAt    DateTime?
  duration       Int?            // milliseconds
  errorMessage   String?
  metadata       Json            @default("{}")
  organizationId String

  // Relationships
  tool         Tool         @relation(fields: [toolId], references: [id], onDelete: Cascade)
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@map("tool_executions")
  @@index([toolId])
  @@index([organizationId])
  @@index([sessionId])
  @@index([status])
  @@index([startedAt])
  @@index([completedAt])
}

// ============================================================================
// 2.1.5 Hybrid System Tables (Hybrids, HybridExecutions)
// ============================================================================

model Hybrid {
  id             String   @id @default(cuid())
  name           String
  description    String?
  agentId        String?
  toolIds        String[] // Array of tool IDs
  configuration  Json     @default("{}")
  workflow       Json     @default("{}")
  isActive       Boolean  @default(true)
  organizationId String
  createdBy      String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relationships
  organization Organization      @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  creator      User              @relation("HybridCreator", fields: [createdBy], references: [id], onDelete: Restrict)
  agent        Agent?            @relation(fields: [agentId], references: [id], onDelete: SetNull)
  executions   HybridExecution[]

  @@map("hybrids")
  @@index([organizationId])
  @@index([createdBy])
  @@index([agentId])
  @@index([isActive])
  @@index([createdAt])
}

model HybridExecution {
  id             String          @id @default(cuid())
  hybridId       String
  sessionId      String?
  input          Json
  output         Json?
  status         ExecutionStatus @default(PENDING)
  steps          Json            @default("[]")
  currentStep    Int             @default(0)
  startedAt      DateTime        @default(now())
  completedAt    DateTime?
  duration       Int?            // milliseconds
  errorMessage   String?
  metadata       Json            @default("{}")
  organizationId String

  // Relationships
  hybrid       Hybrid       @relation(fields: [hybridId], references: [id], onDelete: Cascade)
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@map("hybrid_executions")
  @@index([hybridId])
  @@index([organizationId])
  @@index([sessionId])
  @@index([status])
  @@index([startedAt])
  @@index([completedAt])
}

// ============================================================================
// 2.1.6 Workflow System Tables (Workflows, WorkflowExecutions)
// ============================================================================

model Workflow {
  id             String   @id @default(cuid())
  name           String
  description    String?
  definition     Json     @default("{}")
  agentIds       String[] // Array of agent IDs
  toolIds        String[] // Array of tool IDs
  isActive       Boolean  @default(true)
  organizationId String
  createdBy      String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relationships
  organization Organization        @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  creator      User                @relation("WorkflowCreator", fields: [createdBy], references: [id], onDelete: Restrict)
  executions   WorkflowExecution[]
  sessions     Session[]

  @@map("workflows")
  @@index([organizationId])
  @@index([createdBy])
  @@index([isActive])
  @@index([createdAt])
}

model WorkflowExecution {
  id             String          @id @default(cuid())
  workflowId     String
  sessionId      String?
  input          Json
  output         Json?
  status         ExecutionStatus @default(PENDING)
  steps          Json            @default("[]")
  currentStep    Int             @default(0)
  startedAt      DateTime        @default(now())
  completedAt    DateTime?
  duration       Int?            // milliseconds
  errorMessage   String?
  metadata       Json            @default("{}")
  organizationId String

  // Relationships
  workflow     Workflow     @relation(fields: [workflowId], references: [id], onDelete: Cascade)
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@map("workflow_executions")
  @@index([workflowId])
  @@index([organizationId])
  @@index([sessionId])
  @@index([status])
  @@index([startedAt])
  @@index([completedAt])
}

// ============================================================================
// 2.1.7 Provider System Tables (Providers, ProviderUsage)
// ============================================================================

model Provider {
  id             String   @id @default(cuid())
  name           String   // OpenAI, Anthropic, Google, Mistral, Groq
  type           String   // LLM, EMBEDDING, IMAGE, etc.
  configuration  Json     @default("{}")
  credentials    Json     @default("{}")
  isActive       Boolean  @default(true)
  organizationId String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relationships
  organization Organization    @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  usage        ProviderUsage[]

  @@unique([name, organizationId])
  @@map("providers")
  @@index([organizationId])
  @@index([type])
  @@index([isActive])
  @@index([createdAt])
}

model ProviderUsage {
  id             String   @id @default(cuid())
  providerId     String
  model          String
  tokensUsed     Int      @default(0)
  requestCount   Int      @default(0)
  cost           Decimal  @default(0) @db.Decimal(10, 4)
  date           DateTime @default(now()) @db.Date
  organizationId String

  // Relationships
  provider     Provider     @relation(fields: [providerId], references: [id], onDelete: Cascade)
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@unique([providerId, model, date])
  @@map("provider_usage")
  @@index([providerId])
  @@index([organizationId])
  @@index([date])
  @@index([model])
}

// ============================================================================
// 2.1.8 HITL System Tables (HITLRequests, HITLDecisions)
// ============================================================================

model HITLRequest {
  id             String         @id @default(cuid())
  type           String         // APPROVAL, INPUT, REVIEW, etc.
  title          String
  description    String?
  context        Json           @default("{}")
  priority       Int            @default(0)
  status         ApprovalStatus @default(PENDING)
  assignedTo     String?
  expiresAt      DateTime?
  organizationId String
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt

  // Relationships
  organization Organization   @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  assignee     User?          @relation("HITLAssignee", fields: [assignedTo], references: [id], onDelete: SetNull)
  decisions    HITLDecision[]

  @@map("hitl_requests")
  @@index([organizationId])
  @@index([assignedTo])
  @@index([status])
  @@index([priority])
  @@index([expiresAt])
  @@index([createdAt])
}

model HITLDecision {
  id            String         @id @default(cuid())
  requestId     String
  decision      ApprovalStatus
  reason        String?
  data          Json           @default("{}")
  decidedBy     String
  decidedAt     DateTime       @default(now())

  // Relationships
  request HITLRequest @relation(fields: [requestId], references: [id], onDelete: Cascade)
  decider User        @relation("HITLDecider", fields: [decidedBy], references: [id], onDelete: Restrict)

  @@map("hitl_decisions")
  @@index([requestId])
  @@index([decidedBy])
  @@index([decision])
  @@index([decidedAt])
}

// ============================================================================
// 2.1.9 Knowledge System Tables (Documents, DocumentChunks, KnowledgeSearches)
// ============================================================================

model Document {
  id             String   @id @default(cuid())
  name           String
  type           String   // PDF, TXT, URL, etc.
  content        String?  @db.Text
  url            String?
  metadata       Json     @default("{}")
  size           Int?     // bytes
  isProcessed    Boolean  @default(false)
  organizationId String
  uploadedBy     String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relationships
  organization Organization       @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  uploader     User               @relation("DocumentUploader", fields: [uploadedBy], references: [id], onDelete: Restrict)
  chunks       DocumentChunk[]

  @@map("documents")
  @@index([organizationId])
  @@index([uploadedBy])
  @@index([type])
  @@index([isProcessed])
  @@index([createdAt])
}

model DocumentChunk {
  id         String  @id @default(cuid())
  documentId String
  content    String  @db.Text
  embedding  Float[] // Vector embedding
  metadata   Json    @default("{}")
  chunkIndex Int
  startPos   Int?
  endPos     Int?

  // Relationships
  document Document @relation(fields: [documentId], references: [id], onDelete: Cascade)

  @@unique([documentId, chunkIndex])
  @@map("document_chunks")
  @@index([documentId])
  @@index([chunkIndex])
}

model KnowledgeSearch {
  id             String   @id @default(cuid())
  query          String
  documentIds    String[] // Array of document IDs found
  results        Json     @default("[]")
  metadata       Json     @default("{}")
  organizationId String
  createdAt      DateTime @default(now())

  // Relationships
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@map("knowledge_searches")
  @@index([organizationId])
  @@index([query])
  @@index([createdAt])
}

// ============================================================================
// 2.1.10 Widget System Tables (Widgets, WidgetExecutions)
// ============================================================================

model Widget {
  id             String   @id @default(cuid())
  name           String
  type           String   // AGENT, TOOL, HYBRID, WORKFLOW
  targetId       String   // ID of the target (agent, tool, etc.)
  configuration  Json     @default("{}")
  styling        Json     @default("{}")
  embedCode      String?  @db.Text
  isActive       Boolean  @default(true)
  organizationId String
  createdBy      String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relationships
  organization Organization      @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  creator      User              @relation("WidgetCreator", fields: [createdBy], references: [id], onDelete: Restrict)
  executions   WidgetExecution[]

  @@map("widgets")
  @@index([organizationId])
  @@index([createdBy])
  @@index([type])
  @@index([targetId])
  @@index([isActive])
  @@index([createdAt])
}

model WidgetExecution {
  id             String          @id @default(cuid())
  widgetId       String
  sessionId      String?
  input          Json
  output         Json?
  status         ExecutionStatus @default(PENDING)
  startedAt      DateTime        @default(now())
  completedAt    DateTime?
  duration       Int?            // milliseconds
  errorMessage   String?
  metadata       Json            @default("{}")
  organizationId String

  // Relationships
  widget       Widget       @relation(fields: [widgetId], references: [id], onDelete: Cascade)
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@map("widget_executions")
  @@index([widgetId])
  @@index([organizationId])
  @@index([sessionId])
  @@index([status])
  @@index([startedAt])
  @@index([completedAt])
}

// ============================================================================
// 2.1.11 Analytics System Tables (Analytics, Metrics)
// ============================================================================

model Analytics {
  id             String   @id @default(cuid())
  event          String
  category       String
  properties     Json     @default("{}")
  userId         String?
  sessionId      String?
  timestamp      DateTime @default(now())
  organizationId String

  // Relationships
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@map("analytics")
  @@index([organizationId])
  @@index([event])
  @@index([category])
  @@index([userId])
  @@index([sessionId])
  @@index([timestamp])
}

model Metrics {
  id             String   @id @default(cuid())
  name           String
  value          Decimal  @db.Decimal(15, 4)
  unit           String?
  tags           Json     @default("{}")
  timestamp      DateTime @default(now())
  organizationId String

  // Relationships
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@unique([name, organizationId, timestamp])
  @@map("metrics")
  @@index([organizationId])
  @@index([name])
  @@index([timestamp])
}

// ============================================================================
// 2.1.12 Billing System Tables (Billing, UsageMeters, Quotas)
// ============================================================================

model Billing {
  id             String   @id @default(cuid())
  period         String   // YYYY-MM format
  totalCost      Decimal  @default(0) @db.Decimal(10, 2)
  breakdown      Json     @default("{}")
  status         String   @default("PENDING") // PENDING, PAID, OVERDUE
  dueDate        DateTime?
  paidAt         DateTime?
  organizationId String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relationships
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@unique([organizationId, period])
  @@map("billing")
  @@index([organizationId])
  @@index([period])
  @@index([status])
  @@index([dueDate])
}

model UsageMeter {
  id             String   @id @default(cuid())
  name           String   // agent_executions, tool_calls, etc.
  value          Int      @default(0)
  unit           String   // count, tokens, bytes, etc.
  period         String   // YYYY-MM-DD format
  organizationId String
  updatedAt      DateTime @updatedAt

  // Relationships
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@unique([name, organizationId, period])
  @@map("usage_meters")
  @@index([organizationId])
  @@index([name])
  @@index([period])
}

model Quota {
  id             String   @id @default(cuid())
  name           String   // agent_executions, tool_calls, etc.
  limit          Int
  used           Int      @default(0)
  period         String   // MONTHLY, DAILY, etc.
  organizationId String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relationships
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@unique([name, organizationId])
  @@map("quotas")
  @@index([organizationId])
  @@index([name])
  @@index([period])
}

// ============================================================================
// 2.1.13 Notification System Tables (Notifications, NotificationPreferences)
// ============================================================================

model Notification {
  id             String              @id @default(cuid())
  title          String
  message        String              @db.Text
  type           String              // INFO, WARNING, ERROR, SUCCESS
  channel        NotificationChannel
  recipientId    String
  data           Json                @default("{}")
  isRead         Boolean             @default(false)
  sentAt         DateTime?
  organizationId String
  createdAt      DateTime            @default(now())

  // Relationships
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  recipient    User         @relation("NotificationRecipient", fields: [recipientId], references: [id], onDelete: Cascade)

  @@map("notifications")
  @@index([organizationId])
  @@index([recipientId])
  @@index([type])
  @@index([channel])
  @@index([isRead])
  @@index([sentAt])
  @@index([createdAt])
}

model NotificationPreference {
  id             String              @id @default(cuid())
  userId         String
  channel        NotificationChannel
  type           String              // INFO, WARNING, ERROR, SUCCESS
  enabled        Boolean             @default(true)
  organizationId String

  // Relationships
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, channel, type])
  @@map("notification_preferences")
  @@index([organizationId])
  @@index([userId])
  @@index([channel])
  @@index([type])
}

// ============================================================================
// 2.1.14 Testing System Tables (Sandboxes, TestResults)
// ============================================================================

model Sandbox {
  id             String   @id @default(cuid())
  name           String
  type           String   // AGENT, TOOL, HYBRID, WORKFLOW
  targetId       String   // ID of the target being tested
  configuration  Json     @default("{}")
  isActive       Boolean  @default(true)
  organizationId String
  createdBy      String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relationships
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  creator      User         @relation("SandboxCreator", fields: [createdBy], references: [id], onDelete: Restrict)
  testResults  TestResult[]

  @@map("sandboxes")
  @@index([organizationId])
  @@index([createdBy])
  @@index([type])
  @@index([targetId])
  @@index([isActive])
  @@index([createdAt])
}

model TestResult {
  id             String          @id @default(cuid())
  sandboxId      String
  testName       String
  input          Json
  expectedOutput Json?
  actualOutput   Json?
  status         ExecutionStatus @default(PENDING)
  passed         Boolean?
  errorMessage   String?
  duration       Int?            // milliseconds
  metadata       Json            @default("{}")
  organizationId String
  createdAt      DateTime        @default(now())

  // Relationships
  sandbox      Sandbox      @relation(fields: [sandboxId], references: [id], onDelete: Cascade)
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@map("test_results")
  @@index([sandboxId])
  @@index([organizationId])
  @@index([testName])
  @@index([status])
  @@index([passed])
  @@index([createdAt])
}

// ============================================================================
// API Key Management (Authentication Extension)
// ============================================================================

model ApiKey {
  id             String    @id @default(cuid())
  name           String
  description    String?
  keyHash        String    @unique
  keyPrefix      String
  permissions    Json      // Array of permission strings
  expiresAt      DateTime?
  isActive       Boolean   @default(true)
  lastUsedAt     DateTime?
  organizationId String
  createdBy      String
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt

  // Relationships
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  creator        User         @relation("ApiKeyCreator", fields: [createdBy], references: [id], onDelete: Restrict)

  // Indexing
  @@index([organizationId])
  @@index([keyPrefix])
  @@index([isActive])
  @@index([expiresAt])
  @@map("api_keys")
}


