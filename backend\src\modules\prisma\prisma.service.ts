import { Injectable, OnModuleInit, OnM<PERSON>ule<PERSON><PERSON><PERSON>, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaClient } from '@prisma/client';

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(PrismaService.name);

  constructor(private configService: ConfigService) {
    super({
      datasources: {
        db: {
          url: configService.get('DATABASE_URL'),
        },
      },
      log: [
        {
          emit: 'event',
          level: 'query',
        },
        {
          emit: 'event',
          level: 'error',
        },
        {
          emit: 'event',
          level: 'info',
        },
        {
          emit: 'event',
          level: 'warn',
        },
      ],
    });

    // Log database queries in development
    if (configService.get('NODE_ENV') === 'development') {
      this.$on('query', (e) => {
        this.logger.debug(`Query: ${e.query}`);
        this.logger.debug(`Params: ${e.params}`);
        this.logger.debug(`Duration: ${e.duration}ms`);
      });
    }

    this.$on('error', (e) => {
      this.logger.error('Database error:', e);
    });

    this.$on('info', (e) => {
      this.logger.log(`Database info: ${e.message}`);
    });

    this.$on('warn', (e) => {
      this.logger.warn(`Database warning: ${e.message}`);
    });
  }

  async onModuleInit() {
    try {
      await this.$connect();
      this.logger.log('Successfully connected to database');
    } catch (error) {
      this.logger.error('Failed to connect to database:', error);
      throw error;
    }
  }

  async onModuleDestroy() {
    try {
      await this.$disconnect();
      this.logger.log('Successfully disconnected from database');
    } catch (error) {
      this.logger.error('Error disconnecting from database:', error);
    }
  }

  /**
   * Execute a transaction with automatic retry logic
   */
  async executeTransaction<T>(
    fn: (prisma: PrismaClient) => Promise<T>,
    maxRetries: number = 3,
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await this.$transaction(fn);
      } catch (error) {
        lastError = error as Error;
        this.logger.warn(`Transaction attempt ${attempt} failed:`, error);

        if (attempt === maxRetries) {
          break;
        }

        // Wait before retrying (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }

    this.logger.error(`Transaction failed after ${maxRetries} attempts:`, lastError);
    throw lastError;
  }

  /**
   * Health check for database connection
   */
  async isHealthy(): Promise<boolean> {
    try {
      await this.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      this.logger.error('Database health check failed:', error);
      return false;
    }
  }

  /**
   * Get organization-scoped query helper
   */
  forOrganization(organizationId: string) {
    return {
      // Core entities
      users: this.user.findMany({ where: { organizationId } }),
      roles: this.role.findMany({ where: { organizationId } }),
      sessions: this.session.findMany({ where: { organizationId } }),

      // Template system
      templates: this.template.findMany({ where: { organizationId } }),
      templateVersions: (templateId: string) =>
        this.templateVersion.findMany({ where: { template: { organizationId }, templateId } }),

      // Agent system
      agents: this.agent.findMany({ where: { organizationId } }),
      agentExecutions: this.agentExecution.findMany({ where: { organizationId } }),

      // Tool system
      tools: this.tool.findMany({ where: { organizationId } }),
      toolExecutions: this.toolExecution.findMany({ where: { organizationId } }),

      // Hybrid system
      hybrids: this.hybrid.findMany({ where: { organizationId } }),
      hybridExecutions: this.hybridExecution.findMany({ where: { organizationId } }),

      // Workflow system
      workflows: this.workflow.findMany({ where: { organizationId } }),
      workflowExecutions: this.workflowExecution.findMany({ where: { organizationId } }),

      // Provider system
      providers: this.provider.findMany({ where: { organizationId } }),
      providerUsage: this.providerUsage.findMany({ where: { organizationId } }),

      // HITL system
      hitlRequests: this.hITLRequest.findMany({ where: { organizationId } }),

      // Knowledge system
      documents: this.document.findMany({ where: { organizationId } }),
      knowledgeSearches: this.knowledgeSearch.findMany({ where: { organizationId } }),

      // Widget system
      widgets: this.widget.findMany({ where: { organizationId } }),
      widgetExecutions: this.widgetExecution.findMany({ where: { organizationId } }),

      // Analytics system
      analytics: this.analytics.findMany({ where: { organizationId } }),
      metrics: this.metrics.findMany({ where: { organizationId } }),

      // Billing system
      billing: this.billing.findMany({ where: { organizationId } }),
      usageMeters: this.usageMeter.findMany({ where: { organizationId } }),
      quotas: this.quota.findMany({ where: { organizationId } }),

      // Notification system
      notifications: this.notification.findMany({ where: { organizationId } }),
      notificationPreferences: this.notificationPreference.findMany({ where: { organizationId } }),

      // Testing system
      sandboxes: this.sandbox.findMany({ where: { organizationId } }),
      testResults: this.testResult.findMany({ where: { organizationId } }),
    };
  }

  /**
   * Validate organization access for a user
   */
  async validateOrganizationAccess(userId: string, organizationId: string): Promise<boolean> {
    try {
      const user = await this.user.findFirst({
        where: {
          id: userId,
          organizationId,
          isActive: true,
        },
      });
      return !!user;
    } catch (error) {
      this.logger.error('Organization access validation failed:', error);
      return false;
    }
  }
}
