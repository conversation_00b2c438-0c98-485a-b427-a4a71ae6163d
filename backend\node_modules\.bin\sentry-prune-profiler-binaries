#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/laragon/www/max/trae/kilo-teset/node_modules/.pnpm/@sentry+profiling-node@1.3.5_@sentry+node@7.120.3/node_modules/@sentry/profiling-node/scripts/node_modules:/mnt/c/laragon/www/max/trae/kilo-teset/node_modules/.pnpm/@sentry+profiling-node@1.3.5_@sentry+node@7.120.3/node_modules/@sentry/profiling-node/node_modules:/mnt/c/laragon/www/max/trae/kilo-teset/node_modules/.pnpm/@sentry+profiling-node@1.3.5_@sentry+node@7.120.3/node_modules/@sentry/node_modules:/mnt/c/laragon/www/max/trae/kilo-teset/node_modules/.pnpm/@sentry+profiling-node@1.3.5_@sentry+node@7.120.3/node_modules:/mnt/c/laragon/www/max/trae/kilo-teset/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/laragon/www/max/trae/kilo-teset/node_modules/.pnpm/@sentry+profiling-node@1.3.5_@sentry+node@7.120.3/node_modules/@sentry/profiling-node/scripts/node_modules:/mnt/c/laragon/www/max/trae/kilo-teset/node_modules/.pnpm/@sentry+profiling-node@1.3.5_@sentry+node@7.120.3/node_modules/@sentry/profiling-node/node_modules:/mnt/c/laragon/www/max/trae/kilo-teset/node_modules/.pnpm/@sentry+profiling-node@1.3.5_@sentry+node@7.120.3/node_modules/@sentry/node_modules:/mnt/c/laragon/www/max/trae/kilo-teset/node_modules/.pnpm/@sentry+profiling-node@1.3.5_@sentry+node@7.120.3/node_modules:/mnt/c/laragon/www/max/trae/kilo-teset/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../node_modules/.pnpm/@sentry+profiling-node@1.3.5_@sentry+node@7.120.3/node_modules/@sentry/profiling-node/scripts/prune-profiler-binaries.mjs" "$@"
else
  exec node  "$basedir/../../../../node_modules/.pnpm/@sentry+profiling-node@1.3.5_@sentry+node@7.120.3/node_modules/@sentry/profiling-node/scripts/prune-profiler-binaries.mjs" "$@"
fi
