import { OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis, { Cluster } from 'ioredis';
export declare class RedisService implements OnModuleInit, OnModuleDestroy {
    private configService;
    private readonly logger;
    private client;
    private pubClient;
    private subClient;
    constructor(configService: ConfigService);
    onModuleInit(): Promise<void>;
    onModuleDestroy(): Promise<void>;
    private initializeClients;
    private setupEventListeners;
    getClient(): Redis | Cluster;
    getPubSubClients(): {
        pub: Redis | Cluster;
        sub: Redis | Cluster;
    };
    createSession(organizationId: string, userId?: string, agentId?: string, context?: any): Promise<string>;
    getSession(sessionId: string, organizationId?: string): Promise<any>;
    updateSession(sessionId: string, context: any, organizationId?: string): Promise<any>;
    deleteSession(sessionId: string, organizationId?: string): Promise<void>;
    getOrganizationSessions(organizationId: string): Promise<any[]>;
    cleanupExpiredSessions(organizationId: string): Promise<void>;
    set(key: string, value: any, ttl?: number, organizationId?: string): Promise<void>;
    get(key: string, organizationId?: string): Promise<any>;
    del(key: string, organizationId?: string): Promise<void>;
    exists(key: string, organizationId?: string): Promise<number>;
    getOrganizationKeys(organizationId: string, pattern?: string): Promise<string[]>;
    deleteOrganizationData(organizationId: string): Promise<number>;
    private getNamespacedKey;
    keys(pattern: string): Promise<string[]>;
    setex(key: string, ttl: number, value: any, organizationId?: string): Promise<void>;
    isHealthy(): Promise<boolean>;
    publish(channel: string, message: any): Promise<void>;
    subscribe(channel: string, callback: (message: any) => void): Promise<void>;
    unsubscribe(channel: string): Promise<void>;
}
