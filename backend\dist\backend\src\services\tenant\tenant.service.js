"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TenantService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../modules/prisma/prisma.service");
const logger_service_1 = require("../logger/logger.service");
let TenantService = class TenantService {
    constructor(prismaService, logger) {
        this.prismaService = prismaService;
        this.logger = logger;
    }
    createTenantClient(context) {
        const { organizationId, userId, userRole } = context;
        return {
            organization: {
                findUnique: (args) => this.prismaService.organization.findUnique({
                    ...args,
                    where: { ...args.where, id: organizationId },
                }),
                findFirst: (args) => this.prismaService.organization.findFirst({
                    ...args,
                    where: { ...args.where, id: organizationId },
                }),
                update: (args) => this.prismaService.organization.update({
                    ...args,
                    where: { ...args.where, id: organizationId },
                }),
            },
            user: {
                findMany: (args = {}) => this.prismaService.user.findMany({
                    ...args,
                    where: { ...args.where, organizationId },
                }),
                findUnique: (args) => this.prismaService.user.findUnique({
                    ...args,
                    where: { ...args.where, organizationId },
                }),
                findFirst: (args) => this.prismaService.user.findFirst({
                    ...args,
                    where: { ...args.where, organizationId },
                }),
                create: (args) => this.prismaService.user.create({
                    ...args,
                    data: { ...args.data, organizationId },
                }),
                update: (args) => this.prismaService.user.update({
                    ...args,
                    where: { ...args.where, organizationId },
                }),
                delete: (args) => this.prismaService.user.delete({
                    ...args,
                    where: { ...args.where, organizationId },
                }),
            },
            agent: {
                findMany: (args = {}) => this.prismaService.agent.findMany({
                    ...args,
                    where: { ...args.where, organizationId },
                }),
                findUnique: (args) => this.prismaService.agent.findUnique({
                    ...args,
                    where: { ...args.where, organizationId },
                }),
                create: (args) => this.prismaService.agent.create({
                    ...args,
                    data: {
                        ...args.data,
                        organizationId,
                        createdBy: userId || args.data.createdBy,
                    },
                }),
                update: (args) => this.prismaService.agent.update({
                    ...args,
                    where: { ...args.where, organizationId },
                }),
                delete: (args) => this.prismaService.agent.delete({
                    ...args,
                    where: { ...args.where, organizationId },
                }),
            },
            agentExecution: {
                findMany: (args = {}) => this.prismaService.agentExecution.findMany({
                    ...args,
                    where: { ...args.where, organizationId },
                }),
                create: (args) => this.prismaService.agentExecution.create({
                    ...args,
                    data: { ...args.data, organizationId },
                }),
            },
            tool: {
                findMany: (args = {}) => this.prismaService.tool.findMany({
                    ...args,
                    where: { ...args.where, organizationId },
                }),
                findUnique: (args) => this.prismaService.tool.findUnique({
                    ...args,
                    where: { ...args.where, organizationId },
                }),
                create: (args) => this.prismaService.tool.create({
                    ...args,
                    data: {
                        ...args.data,
                        organizationId,
                        createdBy: userId || args.data.createdBy,
                    },
                }),
                update: (args) => this.prismaService.tool.update({
                    ...args,
                    where: { ...args.where, organizationId },
                }),
                delete: (args) => this.prismaService.tool.delete({
                    ...args,
                    where: { ...args.where, organizationId },
                }),
            },
            toolExecution: {
                findMany: (args = {}) => this.prismaService.toolExecution.findMany({
                    ...args,
                    where: { ...args.where, organizationId },
                }),
                create: (args) => this.prismaService.toolExecution.create({
                    ...args,
                    data: { ...args.data, organizationId },
                }),
            },
            session: {
                findMany: (args = {}) => this.prismaService.session.findMany({
                    ...args,
                    where: { ...args.where, organizationId },
                }),
                findUnique: (args) => this.prismaService.session.findUnique({
                    ...args,
                    where: { ...args.where, organizationId },
                }),
                create: (args) => this.prismaService.session.create({
                    ...args,
                    data: { ...args.data, organizationId },
                }),
                update: (args) => this.prismaService.session.update({
                    ...args,
                    where: { ...args.where, organizationId },
                }),
                delete: (args) => this.prismaService.session.delete({
                    ...args,
                    where: { ...args.where, organizationId },
                }),
            },
            analytics: {
                findMany: (args = {}) => this.prismaService.analytics.findMany({
                    ...args,
                    where: { ...args.where, organizationId },
                }),
                create: (args) => this.prismaService.analytics.create({
                    ...args,
                    data: { ...args.data, organizationId },
                }),
            },
            billing: {
                findMany: (args = {}) => this.prismaService.billing.findMany({
                    ...args,
                    where: { ...args.where, organizationId },
                }),
                findFirst: (args) => this.prismaService.billing.findFirst({
                    ...args,
                    where: { ...args.where, organizationId },
                }),
                create: (args) => this.prismaService.billing.create({
                    ...args,
                    data: { ...args.data, organizationId },
                }),
                update: (args) => this.prismaService.billing.update({
                    ...args,
                    where: { ...args.where, organizationId },
                }),
            },
        };
    }
    async validateResourceAccess(context, resource, action, resourceId) {
        const { organizationId, userId, userRole, permissions = [] } = context;
        if (userRole === 'SUPER_ADMIN' || permissions.includes('*')) {
            return true;
        }
        const requiredPermission = `${resource}:${action}`;
        if (permissions.includes(requiredPermission)) {
            return true;
        }
        if (resourceId) {
            const hasAccess = await this.validateSpecificResourceAccess(organizationId, userId, resource, resourceId);
            if (!hasAccess) {
                this.logger.security('RESOURCE_ACCESS_DENIED', 'medium', {
                    organizationId,
                    userId,
                    details: {
                        resource: 'tenant_boundary',
                        userRole,
                        action,
                        resourceId,
                    },
                });
                return false;
            }
        }
        return true;
    }
    async validateSpecificResourceAccess(organizationId, userId, resource, resourceId) {
        try {
            switch (resource) {
                case 'agents':
                    const agent = await this.prismaService.agent.findFirst({
                        where: { id: resourceId, organizationId },
                    });
                    return !!agent;
                case 'tools':
                    const tool = await this.prismaService.tool.findFirst({
                        where: { id: resourceId, organizationId },
                    });
                    return !!tool;
                case 'workflows':
                    const workflow = await this.prismaService.workflow.findFirst({
                        where: { id: resourceId, organizationId },
                    });
                    return !!workflow;
                case 'users':
                    const user = await this.prismaService.user.findFirst({
                        where: { id: resourceId, organizationId },
                    });
                    return !!user;
                default:
                    return true;
            }
        }
        catch (error) {
            this.logger.error('Resource access validation failed:', error);
            return false;
        }
    }
    async enforceOrganizationBoundary(organizationId, userId) {
        if (!userId) {
            return;
        }
        const user = await this.prismaService.user.findFirst({
            where: { id: userId, organizationId, isActive: true },
        });
        if (!user) {
            throw new common_1.ForbiddenException('Access denied: User does not belong to this organization');
        }
    }
};
exports.TenantService = TenantService;
exports.TenantService = TenantService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        logger_service_1.LoggerService])
], TenantService);
//# sourceMappingURL=tenant.service.js.map