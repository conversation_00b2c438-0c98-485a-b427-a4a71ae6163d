import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectQueue } from '@nestjs/bull';
import { Queue, Job, JobOptions } from 'bull';
import { RedisService } from '@modules/redis/redis.service';

export interface QueueJobData {
  type: string;
  payload: any;
  organizationId: string;
  userId?: string;
  priority?: number;
  metadata?: Record<string, any>;
}

export interface QueueJobResult {
  success: boolean;
  result?: any;
  error?: string;
  duration: number;
  timestamp: string;
}

@Injectable()
export class QueueService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(QueueService.name);

  constructor(
    private configService: ConfigService,
    private redisService: RedisService,
    @InjectQueue('agent-execution') private agentQueue: Queue,
    @InjectQueue('tool-execution') private toolQueue: Queue,
    @InjectQueue('workflow-execution') private workflowQueue: Queue,
    @InjectQueue('notification') private notificationQueue: Queue,
    @InjectQueue('analytics') private analyticsQueue: Queue,
    @InjectQueue('billing') private billingQueue: Queue,
  ) {}

  async onModuleInit() {
    await this.setupQueueProcessors();
    await this.setupQueueEvents();
    this.logger.log('Queue service initialized successfully');
  }

  async onModuleDestroy() {
    const queues = [
      this.agentQueue,
      this.toolQueue,
      this.workflowQueue,
      this.notificationQueue,
      this.analyticsQueue,
      this.billingQueue,
    ];

    await Promise.all(queues.map(queue => queue.close()));
    this.logger.log('Queue service shut down successfully');
  }

  /**
   * Add job to agent execution queue
   */
  async addAgentJob(
    jobData: QueueJobData,
    options: JobOptions = {},
  ): Promise<Job<QueueJobData>> {
    const defaultOptions: JobOptions = {
      priority: jobData.priority || 0,
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
      removeOnComplete: 100,
      removeOnFail: 50,
    };

    return this.agentQueue.add('execute-agent', jobData, {
      ...defaultOptions,
      ...options,
    });
  }

  /**
   * Add job to tool execution queue
   */
  async addToolJob(
    jobData: QueueJobData,
    options: JobOptions = {},
  ): Promise<Job<QueueJobData>> {
    const defaultOptions: JobOptions = {
      priority: jobData.priority || 0,
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 1000,
      },
      removeOnComplete: 100,
      removeOnFail: 50,
    };

    return this.toolQueue.add('execute-tool', jobData, {
      ...defaultOptions,
      ...options,
    });
  }

  /**
   * Add job to workflow execution queue
   */
  async addWorkflowJob(
    jobData: QueueJobData,
    options: JobOptions = {},
  ): Promise<Job<QueueJobData>> {
    const defaultOptions: JobOptions = {
      priority: jobData.priority || 0,
      attempts: 5,
      backoff: {
        type: 'exponential',
        delay: 3000,
      },
      removeOnComplete: 50,
      removeOnFail: 25,
    };

    return this.workflowQueue.add('execute-workflow', jobData, {
      ...defaultOptions,
      ...options,
    });
  }

  /**
   * Add job to notification queue
   */
  async addNotificationJob(
    jobData: QueueJobData,
    options: JobOptions = {},
  ): Promise<Job<QueueJobData>> {
    const defaultOptions: JobOptions = {
      priority: jobData.priority || 5, // Higher priority for notifications
      attempts: 3,
      backoff: {
        type: 'fixed',
        delay: 5000,
      },
      removeOnComplete: 200,
      removeOnFail: 100,
    };

    return this.notificationQueue.add('send-notification', jobData, {
      ...defaultOptions,
      ...options,
    });
  }

  /**
   * Add job to analytics queue
   */
  async addAnalyticsJob(
    jobData: QueueJobData,
    options: JobOptions = {},
  ): Promise<Job<QueueJobData>> {
    const defaultOptions: JobOptions = {
      priority: jobData.priority || -5, // Lower priority for analytics
      attempts: 2,
      backoff: {
        type: 'fixed',
        delay: 10000,
      },
      removeOnComplete: 500,
      removeOnFail: 100,
    };

    return this.analyticsQueue.add('process-analytics', jobData, {
      ...defaultOptions,
      ...options,
    });
  }

  /**
   * Add job to billing queue
   */
  async addBillingJob(
    jobData: QueueJobData,
    options: JobOptions = {},
  ): Promise<Job<QueueJobData>> {
    const defaultOptions: JobOptions = {
      priority: jobData.priority || 10, // High priority for billing
      attempts: 5,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
      removeOnComplete: 1000,
      removeOnFail: 200,
    };

    return this.billingQueue.add('process-billing', jobData, {
      ...defaultOptions,
      ...options,
    });
  }

  /**
   * Get queue statistics
   */
  async getQueueStats() {
    const queues = [
      { name: 'agent-execution', queue: this.agentQueue },
      { name: 'tool-execution', queue: this.toolQueue },
      { name: 'workflow-execution', queue: this.workflowQueue },
      { name: 'notification', queue: this.notificationQueue },
      { name: 'analytics', queue: this.analyticsQueue },
      { name: 'billing', queue: this.billingQueue },
    ];

    const stats = await Promise.all(
      queues.map(async ({ name, queue }) => {
        const [waiting, active, completed, failed, delayed] = await Promise.all([
          queue.getWaiting(),
          queue.getActive(),
          queue.getCompleted(),
          queue.getFailed(),
          queue.getDelayed(),
        ]);

        return {
          name,
          waiting: waiting.length,
          active: active.length,
          completed: completed.length,
          failed: failed.length,
          delayed: delayed.length,
        };
      }),
    );

    return stats;
  }

  /**
   * Pause/Resume queue operations
   */
  async pauseQueue(queueName: string) {
    const queue = this.getQueueByName(queueName);
    if (queue) {
      await queue.pause();
      this.logger.log(`Queue ${queueName} paused`);
    }
  }

  async resumeQueue(queueName: string) {
    const queue = this.getQueueByName(queueName);
    if (queue) {
      await queue.resume();
      this.logger.log(`Queue ${queueName} resumed`);
    }
  }

  /**
   * Clean up completed/failed jobs
   */
  async cleanQueue(queueName: string, grace: number = 24 * 60 * 60 * 1000) {
    const queue = this.getQueueByName(queueName);
    if (queue) {
      await queue.clean(grace, 'completed');
      await queue.clean(grace, 'failed');
      this.logger.log(`Queue ${queueName} cleaned`);
    }
  }

  private getQueueByName(name: string): Queue | null {
    const queueMap = {
      'agent-execution': this.agentQueue,
      'tool-execution': this.toolQueue,
      'workflow-execution': this.workflowQueue,
      'notification': this.notificationQueue,
      'analytics': this.analyticsQueue,
      'billing': this.billingQueue,
    };

    return queueMap[name] || null;
  }

  private async setupQueueProcessors() {
    // Queue processors will be implemented in subsequent tasks
    this.logger.log('Queue processors setup completed');
  }

  private async setupQueueEvents() {
    const queues = [
      { name: 'agent-execution', queue: this.agentQueue },
      { name: 'tool-execution', queue: this.toolQueue },
      { name: 'workflow-execution', queue: this.workflowQueue },
      { name: 'notification', queue: this.notificationQueue },
      { name: 'analytics', queue: this.analyticsQueue },
      { name: 'billing', queue: this.billingQueue },
    ];

    queues.forEach(({ name, queue }) => {
      queue.on('completed', (job: Job, result: QueueJobResult) => {
        this.logger.log(`Job completed in ${name}: ${job.id}`);
      });

      queue.on('failed', (job: Job, error: Error) => {
        this.logger.error(`Job failed in ${name}: ${job.id}`, error);
      });

      queue.on('stalled', (job: Job) => {
        this.logger.warn(`Job stalled in ${name}: ${job.id}`);
      });

      queue.on('progress', (job: Job, progress: number) => {
        this.logger.debug(`Job progress in ${name}: ${job.id} - ${progress}%`);
      });
    });
  }
}
