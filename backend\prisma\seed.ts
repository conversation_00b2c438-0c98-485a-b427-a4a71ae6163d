import { PrismaClient, UserRole } from '@prisma/client';
import * as bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create default organization
  const defaultOrg = await prisma.organization.upsert({
    where: { slug: 'synapseai-demo' },
    update: {},
    create: {
      name: 'SynapseAI Demo Organization',
      slug: 'synapseai-demo',
      settings: {
        allowedDomains: ['synapseai.com'],
        ssoEnabled: false,
        maxUsers: 100,
        features: {
          agents: true,
          tools: true,
          workflows: true,
          analytics: true,
          widgets: true,
        },
      },
      isActive: true,
    },
  });

  console.log('✅ Created default organization:', defaultOrg.name);

  // Create default roles
  const adminRole = await prisma.role.upsert({
    where: { 
      name_organizationId: { 
        name: 'Admin', 
        organizationId: defaultOrg.id 
      } 
    },
    update: {},
    create: {
      name: 'Admin',
      organizationId: defaultOrg.id,
      permissions: [
        'organization:read',
        'organization:write',
        'users:read',
        'users:write',
        'agents:read',
        'agents:write',
        'tools:read',
        'tools:write',
        'workflows:read',
        'workflows:write',
        'analytics:read',
        'billing:read',
      ],
    },
  });

  const developerRole = await prisma.role.upsert({
    where: { 
      name_organizationId: { 
        name: 'Developer', 
        organizationId: defaultOrg.id 
      } 
    },
    update: {},
    create: {
      name: 'Developer',
      organizationId: defaultOrg.id,
      permissions: [
        'agents:read',
        'agents:write',
        'tools:read',
        'tools:write',
        'workflows:read',
        'workflows:write',
        'sandboxes:read',
        'sandboxes:write',
      ],
    },
  });

  console.log('✅ Created default roles');

  // Create default super admin user
  const hashedPassword = await bcrypt.hash('admin123!', 12);
  
  const adminUser = await prisma.user.upsert({
    where: { 
      email_organizationId: { 
        email: '<EMAIL>', 
        organizationId: defaultOrg.id 
      } 
    },
    update: {},
    create: {
      email: '<EMAIL>',
      passwordHash: hashedPassword,
      firstName: 'System',
      lastName: 'Administrator',
      role: UserRole.SUPER_ADMIN,
      organizationId: defaultOrg.id,
      isActive: true,
    },
  });

  console.log('✅ Created admin user:', adminUser.email);

  // Create demo developer user
  const demoUser = await prisma.user.upsert({
    where: { 
      email_organizationId: { 
        email: '<EMAIL>', 
        organizationId: defaultOrg.id 
      } 
    },
    update: {},
    create: {
      email: '<EMAIL>',
      passwordHash: await bcrypt.hash('demo123!', 12),
      firstName: 'Demo',
      lastName: 'Developer',
      role: UserRole.DEVELOPER,
      organizationId: defaultOrg.id,
      isActive: true,
    },
  });

  console.log('✅ Created demo user:', demoUser.email);

  // Create default AI providers
  const openaiProvider = await prisma.provider.upsert({
    where: { 
      name_organizationId: { 
        name: 'OpenAI', 
        organizationId: defaultOrg.id 
      } 
    },
    update: {},
    create: {
      name: 'OpenAI',
      type: 'LLM',
      organizationId: defaultOrg.id,
      configuration: {
        baseUrl: 'https://api.openai.com/v1',
        models: ['gpt-4', 'gpt-3.5-turbo', 'gpt-4-turbo'],
        defaultModel: 'gpt-4',
      },
      credentials: {
        apiKey: process.env.OPENAI_API_KEY || 'your-openai-api-key',
      },
      isActive: true,
    },
  });

  const anthropicProvider = await prisma.provider.upsert({
    where: { 
      name_organizationId: { 
        name: 'Anthropic', 
        organizationId: defaultOrg.id 
      } 
    },
    update: {},
    create: {
      name: 'Anthropic',
      type: 'LLM',
      organizationId: defaultOrg.id,
      configuration: {
        baseUrl: 'https://api.anthropic.com',
        models: ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku'],
        defaultModel: 'claude-3-sonnet',
      },
      credentials: {
        apiKey: process.env.ANTHROPIC_API_KEY || 'your-anthropic-api-key',
      },
      isActive: true,
    },
  });

  console.log('✅ Created default AI providers');

  // Create sample template
  const sampleTemplate = await prisma.template.create({
    data: {
      name: 'Customer Support Assistant',
      description: 'A helpful customer support agent template',
      category: 'Customer Service',
      content: `You are a helpful customer support assistant for SynapseAI. 

Your role is to:
- Answer questions about our AI platform
- Help users troubleshoot issues
- Provide guidance on best practices
- Escalate complex issues when needed

Always be polite, professional, and helpful. If you don't know something, admit it and offer to find the answer.`,
      variables: [
        { name: 'company_name', type: 'string', required: true },
        { name: 'support_email', type: 'string', required: false },
      ],
      isPublic: true,
      organizationId: defaultOrg.id,
      createdBy: adminUser.id,
    },
  });

  // Create template version
  await prisma.templateVersion.create({
    data: {
      templateId: sampleTemplate.id,
      version: '1.0.0',
      content: sampleTemplate.content,
      variables: sampleTemplate.variables,
      isActive: true,
    },
  });

  console.log('✅ Created sample template');

  // Create sample agent
  const sampleAgent = await prisma.agent.create({
    data: {
      name: 'Demo Support Agent',
      description: 'A demo customer support agent',
      templateId: sampleTemplate.id,
      configuration: {
        provider: 'OpenAI',
        model: 'gpt-4',
        temperature: 0.7,
        maxTokens: 1000,
        systemPrompt: sampleTemplate.content,
      },
      organizationId: defaultOrg.id,
      createdBy: adminUser.id,
      isActive: true,
    },
  });

  console.log('✅ Created sample agent');

  // Create sample tool
  const sampleTool = await prisma.tool.create({
    data: {
      name: 'Email Validator',
      description: 'Validates email addresses',
      type: 'FUNCTION',
      configuration: {
        function: 'validateEmail',
        timeout: 5000,
      },
      schema: {
        input: {
          type: 'object',
          properties: {
            email: { type: 'string', format: 'email' },
          },
          required: ['email'],
        },
        output: {
          type: 'object',
          properties: {
            isValid: { type: 'boolean' },
            reason: { type: 'string' },
          },
        },
      },
      organizationId: defaultOrg.id,
      createdBy: adminUser.id,
      isActive: true,
    },
  });

  console.log('✅ Created sample tool');

  // Create initial usage quotas
  await prisma.quota.createMany({
    data: [
      {
        name: 'agent_executions',
        limit: 1000,
        period: 'MONTHLY',
        organizationId: defaultOrg.id,
      },
      {
        name: 'tool_calls',
        limit: 5000,
        period: 'MONTHLY',
        organizationId: defaultOrg.id,
      },
      {
        name: 'workflow_steps',
        limit: 2000,
        period: 'MONTHLY',
        organizationId: defaultOrg.id,
      },
    ],
  });

  console.log('✅ Created initial quotas');

  // Create notification preferences for users
  const notificationChannels = ['EMAIL', 'IN_APP'];
  const notificationTypes = ['INFO', 'WARNING', 'ERROR', 'SUCCESS'];

  for (const user of [adminUser, demoUser]) {
    for (const channel of notificationChannels) {
      for (const type of notificationTypes) {
        await prisma.notificationPreference.create({
          data: {
            userId: user.id,
            organizationId: defaultOrg.id,
            channel: channel as any,
            type,
            enabled: true,
          },
        });
      }
    }
  }

  console.log('✅ Created notification preferences');

  console.log('🎉 Database seeding completed successfully!');
  console.log('\n📋 Default credentials:');
  console.log('Admin: <EMAIL> / admin123!');
  console.log('Demo: <EMAIL> / demo123!');
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
