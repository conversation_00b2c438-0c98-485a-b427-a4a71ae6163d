"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.configValidationSchema = void 0;
const Joi = require("joi");
exports.configValidationSchema = Joi.object({
    NODE_ENV: Joi.string()
        .valid('development', 'production', 'test', 'staging')
        .default('development'),
    PORT: Joi.number().port().default(3001),
    HOST: Joi.string().default('0.0.0.0'),
    APP_VERSION: Joi.string().default('1.0.0'),
    DATABASE_URL: Joi.string().uri().required(),
    DATABASE_SSL: Joi.boolean().default(false),
    DATABASE_POOL_SIZE: Joi.number().min(1).max(100).default(10),
    REDIS_HOST: Joi.string().default('localhost'),
    REDIS_PORT: Joi.number().port().default(6379),
    REDIS_PASSWORD: Joi.string().allow('').optional(),
    REDIS_DB: Joi.number().min(0).max(15).default(0),
    REDIS_CLUSTER_NODES: Joi.string().optional(),
    JWT_SECRET: Joi.string().min(32).required(),
    JWT_EXPIRES_IN: Joi.string().default('1h'),
    JWT_REFRESH_EXPIRES_IN: Joi.string().default('7d'),
    CORS_ORIGINS: Joi.string().default('*'),
    RATE_LIMIT_WINDOW_MS: Joi.number().default(900000),
    RATE_LIMIT_MAX_REQUESTS: Joi.number().default(100),
    SENTRY_DSN: Joi.string().uri().optional(),
    NEW_RELIC_LICENSE_KEY: Joi.string().optional(),
    LOG_LEVEL: Joi.string()
        .valid('error', 'warn', 'info', 'debug', 'verbose')
        .default('info'),
    LOG_DIR: Joi.string().default('./logs'),
    OPENAI_API_KEY: Joi.string().optional(),
    ANTHROPIC_API_KEY: Joi.string().optional(),
    GOOGLE_AI_API_KEY: Joi.string().optional(),
    MISTRAL_API_KEY: Joi.string().optional(),
    GROQ_API_KEY: Joi.string().optional(),
    AWS_ACCESS_KEY_ID: Joi.string().optional(),
    AWS_SECRET_ACCESS_KEY: Joi.string().optional(),
    AWS_REGION: Joi.string().default('us-east-1'),
    S3_BUCKET: Joi.string().optional(),
    SENDGRID_API_KEY: Joi.string().optional(),
    RESEND_API_KEY: Joi.string().optional(),
    SMTP_HOST: Joi.string().optional(),
    SMTP_PORT: Joi.number().port().optional(),
    SMTP_USER: Joi.string().optional(),
    SMTP_PASS: Joi.string().optional(),
    TWILIO_ACCOUNT_SID: Joi.string().optional(),
    TWILIO_AUTH_TOKEN: Joi.string().optional(),
    TWILIO_PHONE_NUMBER: Joi.string().optional(),
    STRIPE_SECRET_KEY: Joi.string().optional(),
    STRIPE_WEBHOOK_SECRET: Joi.string().optional(),
    PINECONE_API_KEY: Joi.string().optional(),
    PINECONE_ENVIRONMENT: Joi.string().optional(),
    WEAVIATE_URL: Joi.string().uri().optional(),
    WEAVIATE_API_KEY: Joi.string().optional(),
    CLOUDFLARE_API_TOKEN: Joi.string().optional(),
    CLOUDFLARE_ZONE_ID: Joi.string().optional(),
    SLACK_WEBHOOK_URL: Joi.string().uri().optional(),
    DISCORD_WEBHOOK_URL: Joi.string().uri().optional(),
});
//# sourceMappingURL=config.validation.js.map