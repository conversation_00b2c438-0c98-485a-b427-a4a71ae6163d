import { Injectable, BadRequestException, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '@modules/prisma/prisma.service';
import { LoggerService } from '@services/logger/logger.service';
import * as jwt from 'jsonwebtoken';

enum UserRole {
  SUPER_ADMIN = 'SUPER_ADMIN',
  ORG_ADMIN = 'ORG_ADMIN',
  DEVELOPER = 'DEVELOPER',
  VIEWER = 'VIEWER'
}

interface SsoProvider {
  id: string;
  name: string;
  type: 'SAML' | 'OIDC' | 'ACTIVE_DIRECTORY';
  organizationId: string;
  isActive: boolean;
  config: Record<string, any>;
}

interface SsoUserProfile {
  email: string;
  firstName?: string;
  lastName?: string;
  groups?: string[];
  attributes?: Record<string, any>;
}

interface CreateSsoProviderDto {
  name: string;
  type: 'SAML' | 'OIDC' | 'ACTIVE_DIRECTORY';
  organizationId: string;
  config: Record<string, any>;
}

@Injectable()
export class SsoService {
  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
    private logger: LoggerService,
  ) {}

  /**
   * Create SSO provider configuration
   */
  async createSsoProvider(createDto: CreateSsoProviderDto): Promise<SsoProvider> {
    try {
      // Validate configuration based on provider type
      this.validateSsoConfig(createDto.type, createDto.config);

      const provider = await this.prisma.ssoProvider.create({
        data: {
          name: createDto.name,
          type: createDto.type,
          organizationId: createDto.organizationId,
          config: createDto.config,
          isActive: true,
        },
      });

      this.logger.audit('SSO_PROVIDER_CREATED', 'success', {
        organizationId: createDto.organizationId,
        resource: 'sso_provider',
        details: {
          providerId: provider.id,
          type: createDto.type,
          name: createDto.name,
        },
      });

      return provider as SsoProvider;
    } catch (error) {
      this.logger.error('Failed to create SSO provider', error);
      throw new BadRequestException('Failed to create SSO provider');
    }
  }

  /**
   * Get SSO providers for organization
   */
  async getSsoProviders(organizationId: string): Promise<SsoProvider[]> {
    return this.prisma.ssoProvider.findMany({
      where: {
        organizationId,
        isActive: true,
      },
    }) as Promise<SsoProvider[]>;
  }

  /**
   * Process SSO authentication callback
   */
  async processSsoCallback(
    providerId: string,
    userProfile: SsoUserProfile,
  ): Promise<{ accessToken: string; refreshToken: string; user: any }> {
    try {
      const provider = await this.prisma.ssoProvider.findUnique({
        where: { id: providerId },
        include: { organization: true },
      });

      if (!provider || !provider.isActive) {
        throw new UnauthorizedException('SSO provider not found or inactive');
      }

      // Find or create user
      let user = await this.prisma.user.findFirst({
        where: {
          email: userProfile.email,
          organizationId: provider.organizationId,
        },
      });

      if (!user) {
        // Create new user from SSO profile
        user = await this.createUserFromSsoProfile(
          userProfile,
          provider.organizationId,
          provider.config,
        );
      } else {
        // Update user profile from SSO
        user = await this.updateUserFromSsoProfile(user.id, userProfile);
      }

      // Generate tokens
      const tokens = await this.generateTokens(user);

      this.logger.audit('SSO_LOGIN', 'success', {
        userId: user.id,
        organizationId: provider.organizationId,
        resource: 'sso_authentication',
        details: {
          providerId,
          providerType: provider.type,
          email: userProfile.email,
        },
      });

      return {
        ...tokens,
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          organizationId: user.organizationId,
        },
      };
    } catch (error) {
      this.logger.error('SSO callback processing failed', error);
      throw new UnauthorizedException('SSO authentication failed');
    }
  }

  /**
   * Validate SSO provider configuration
   */
  private validateSsoConfig(type: string, config: Record<string, any>): void {
    switch (type) {
      case 'SAML':
        this.validateSamlConfig(config);
        break;
      case 'OIDC':
        this.validateOidcConfig(config);
        break;
      case 'ACTIVE_DIRECTORY':
        this.validateActiveDirectoryConfig(config);
        break;
      default:
        throw new BadRequestException('Invalid SSO provider type');
    }
  }

  private validateSamlConfig(config: Record<string, any>): void {
    const required = ['entryPoint', 'issuer', 'cert'];
    for (const field of required) {
      if (!config[field]) {
        throw new BadRequestException(`SAML configuration missing: ${field}`);
      }
    }
  }

  private validateOidcConfig(config: Record<string, any>): void {
    const required = ['issuer', 'clientId', 'clientSecret'];
    for (const field of required) {
      if (!config[field]) {
        throw new BadRequestException(`OIDC configuration missing: ${field}`);
      }
    }
  }

  private validateActiveDirectoryConfig(config: Record<string, any>): void {
    const required = ['url', 'baseDN', 'username', 'password'];
    for (const field of required) {
      if (!config[field]) {
        throw new BadRequestException(`Active Directory configuration missing: ${field}`);
      }
    }
  }

  /**
   * Create user from SSO profile
   */
  private async createUserFromSsoProfile(
    profile: SsoUserProfile,
    organizationId: string,
    providerConfig: Record<string, any>,
  ): Promise<any> {
    // Map SSO groups to roles
    const role = this.mapSsoGroupsToRole(profile.groups || [], providerConfig);

    return this.prisma.user.create({
      data: {
        email: profile.email,
        firstName: profile.firstName || '',
        lastName: profile.lastName || '',
        role,
        organizationId,
        isActive: true,
        ssoProvider: true,
        lastLoginAt: new Date(),
      },
    });
  }

  /**
   * Update user from SSO profile
   */
  private async updateUserFromSsoProfile(
    userId: string,
    profile: SsoUserProfile,
  ): Promise<any> {
    return this.prisma.user.update({
      where: { id: userId },
      data: {
        firstName: profile.firstName || undefined,
        lastName: profile.lastName || undefined,
        lastLoginAt: new Date(),
      },
    });
  }

  /**
   * Map SSO groups to user roles
   */
  private mapSsoGroupsToRole(groups: string[], config: Record<string, any>): UserRole {
    const roleMapping = config.roleMapping || {};
    
    // Check for admin groups first
    if (groups.some(group => roleMapping.admin?.includes(group))) {
      return UserRole.ORG_ADMIN;
    }
    
    // Check for developer groups
    if (groups.some(group => roleMapping.developer?.includes(group))) {
      return UserRole.DEVELOPER;
    }
    
    // Default to viewer
    return UserRole.VIEWER;
  }

  /**
   * Generate JWT tokens
   */
  private async generateTokens(user: any): Promise<{ accessToken: string; refreshToken: string }> {
    const jwtSecret = this.configService.get<string>('JWT_SECRET');
    const jwtExpiresIn = this.configService.get<string>('JWT_EXPIRES_IN', '1h');
    
    const payload = {
      userId: user.id,
      email: user.email,
      role: user.role,
      organizationId: user.organizationId,
    };

    const accessToken = jwt.sign(payload, jwtSecret, { expiresIn: jwtExpiresIn });
    const refreshToken = jwt.sign({ userId: user.id }, jwtSecret, { expiresIn: '7d' });

    return { accessToken, refreshToken };
  }
}
