import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { ApiKeyService } from '../api-key.service';
import { CaslAbilityFactory } from '../casl/casl-ability.factory';

@Injectable()
export class ApiKeyGuard implements CanActivate {
  constructor(
    private apiKeyService: ApiKeyService,
    private caslAbilityFactory: CaslAbilityFactory,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const apiKey = this.extractApiKey(request);

    if (!apiKey) {
      throw new UnauthorizedException('API key required');
    }

    try {
      const apiKeyData = await this.apiKeyService.validateApiKey(apiKey);
      
      // Create ability for API key
      const ability = this.caslAbilityFactory.createForApiKey(
        apiKeyData.organizationId,
        apiKeyData.permissions,
      );

      // Attach API key data to request
      request.apiKey = {
        ...apiKeyData,
        ability,
      };

      return true;
    } catch (error) {
      throw new UnauthorizedException('Invalid API key');
    }
  }

  private extractApiKey(request: any): string | null {
    // Check Authorization header: Bearer sk_...
    const authHeader = request.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer sk_')) {
      return authHeader.substring(7); // Remove 'Bearer '
    }

    // Check X-API-Key header
    const apiKeyHeader = request.headers['x-api-key'];
    if (apiKeyHeader && apiKeyHeader.startsWith('sk_')) {
      return apiKeyHeader;
    }

    // Check query parameter
    const queryApiKey = request.query.api_key;
    if (queryApiKey && queryApiKey.startsWith('sk_')) {
      return queryApiKey;
    }

    return null;
  }
}
