{"name": "@synapseai/shared", "version": "1.0.0", "description": "Shared TypeScript types and utilities for SynapseAI platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "type-check": "tsc --noEmit", "lint": "eslint \"src/**/*.ts\" --fix", "test": "jest"}, "files": ["dist/**/*", "src/**/*"], "devDependencies": {"@types/node": "^20.8.10", "@typescript-eslint/eslint-plugin": "^6.9.1", "@typescript-eslint/parser": "^6.9.1", "eslint": "^8.52.0", "jest": "^29.7.0", "rimraf": "^5.0.5", "typescript": "^5.2.2"}, "peerDependencies": {"typescript": ">=4.5.0"}}