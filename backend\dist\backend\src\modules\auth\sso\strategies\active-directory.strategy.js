"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActiveDirectoryStrategy = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const passport_ldapauth_1 = require("passport-ldapauth");
const config_1 = require("@nestjs/config");
let ActiveDirectoryStrategy = class ActiveDirectoryStrategy extends (0, passport_1.PassportStrategy)(passport_ldapauth_1.Strategy, 'ldap') {
    constructor(configService) {
        super({
            server: {
                url: configService.get('LDAP_URL'),
                bindDN: configService.get('LDAP_BIND_DN'),
                bindCredentials: configService.get('LDAP_BIND_PASSWORD'),
                searchBase: configService.get('LDAP_SEARCH_BASE'),
                searchFilter: '(sAMAccountName={{username}})',
                searchAttributes: [
                    'displayName',
                    'mail',
                    'givenName',
                    'sn',
                    'memberOf',
                    'department',
                    'title',
                ],
            },
            usernameField: 'username',
            passwordField: 'password',
        });
        this.configService = configService;
    }
    async validate(user) {
        const groups = user.memberOf || [];
        return {
            email: user.mail,
            firstName: user.givenName,
            lastName: user.sn,
            displayName: user.displayName,
            groups: Array.isArray(groups) ? groups : [groups],
            attributes: {
                department: user.department,
                title: user.title,
                ...user,
            },
        };
    }
};
exports.ActiveDirectoryStrategy = ActiveDirectoryStrategy;
exports.ActiveDirectoryStrategy = ActiveDirectoryStrategy = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], ActiveDirectoryStrategy);
//# sourceMappingURL=active-directory.strategy.js.map