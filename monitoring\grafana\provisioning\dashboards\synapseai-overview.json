{"dashboard": {"id": null, "title": "SynapseAI Platform Overview", "tags": ["synapseai", "overview", "production"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "Platform Health Status", "type": "stat", "targets": [{"expr": "up{job=\"synapseai-backend\"}", "legendFormat": "Backend Services"}, {"expr": "up{job=\"postgresql\"}", "legendFormat": "Database"}, {"expr": "up{job=\"redis\"}", "legendFormat": "<PERSON><PERSON>"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Request Rate", "type": "graph", "targets": [{"expr": "rate(http_requests_total{job=\"synapseai-backend\"}[5m])", "legendFormat": "{{method}} {{route}}"}], "yAxes": [{"label": "Requests/sec", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Response Time", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job=\"synapseai-backend\"}[5m]))", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket{job=\"synapseai-backend\"}[5m]))", "legendFormat": "50th percentile"}], "yAxes": [{"label": "Seconds", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "Error Rate", "type": "graph", "targets": [{"expr": "rate(http_requests_total{job=\"synapseai-backend\",status=~\"5..\"}[5m]) / rate(http_requests_total{job=\"synapseai-backend\"}[5m])", "legendFormat": "5xx Error Rate"}, {"expr": "rate(http_requests_total{job=\"synapseai-backend\",status=~\"4..\"}[5m]) / rate(http_requests_total{job=\"synapseai-backend\"}[5m])", "legendFormat": "4xx Error Rate"}], "yAxes": [{"label": "Error Rate", "min": 0, "max": 1}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 5, "title": "AI Agent Executions", "type": "graph", "targets": [{"expr": "rate(synapseai_agent_executions_total[5m])", "legendFormat": "Agent Executions/sec"}, {"expr": "rate(synapseai_agent_executions_total{status=\"success\"}[5m])", "legendFormat": "Successful Executions/sec"}, {"expr": "rate(synapseai_agent_executions_total{status=\"failed\"}[5m])", "legendFormat": "Failed Executions/sec"}], "yAxes": [{"label": "Executions/sec", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 6, "title": "Tool Executions", "type": "graph", "targets": [{"expr": "rate(synapseai_tool_executions_total[5m])", "legendFormat": "Tool Executions/sec"}, {"expr": "rate(synapseai_tool_executions_total{status=\"success\"}[5m])", "legendFormat": "Successful Executions/sec"}], "yAxes": [{"label": "Executions/sec", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 7, "title": "Database Performance", "type": "graph", "targets": [{"expr": "rate(pg_stat_database_tup_fetched{datname=\"synapseai\"}[5m])", "legendFormat": "Rows Fetched/sec"}, {"expr": "rate(pg_stat_database_tup_inserted{datname=\"synapseai\"}[5m])", "legendFormat": "Rows Inserted/sec"}, {"expr": "rate(pg_stat_database_tup_updated{datname=\"synapseai\"}[5m])", "legendFormat": "Rows Updated/sec"}], "yAxes": [{"label": "Operations/sec", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}}, {"id": 8, "title": "Redis Performance", "type": "graph", "targets": [{"expr": "rate(redis_commands_processed_total[5m])", "legendFormat": "Commands/sec"}, {"expr": "redis_connected_clients", "legendFormat": "Connected Clients"}, {"expr": "redis_memory_used_bytes / 1024 / 1024", "legendFormat": "Memory Used (MB)"}], "yAxes": [{"label": "Operations/sec", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}}, {"id": 9, "title": "Queue Status", "type": "graph", "targets": [{"expr": "synapseai_queue_jobs_waiting", "legendFormat": "{{queue}} - Waiting"}, {"expr": "synapseai_queue_jobs_active", "legendFormat": "{{queue}} - Active"}, {"expr": "synapseai_queue_jobs_completed", "legendFormat": "{{queue}} - Completed"}, {"expr": "synapseai_queue_jobs_failed", "legendFormat": "{{queue}} - Failed"}], "yAxes": [{"label": "Job Count", "min": 0}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 32}}, {"id": 10, "title": "AI Provider Performance", "type": "graph", "targets": [{"expr": "rate(synapseai_ai_provider_requests_total[5m])", "legendFormat": "{{provider}} - Requests/sec"}, {"expr": "histogram_quantile(0.95, rate(synapseai_ai_provider_duration_seconds_bucket[5m]))", "legendFormat": "{{provider}} - 95th percentile latency"}], "yAxes": [{"label": "Requests/sec | Latency (s)", "min": 0}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 40}}]}}