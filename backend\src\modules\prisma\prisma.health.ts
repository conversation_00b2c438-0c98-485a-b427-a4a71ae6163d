import { Injectable } from '@nestjs/common';
import { HealthIndicator, HealthIndicatorResult, HealthCheckError } from '@nestjs/terminus';
import { PrismaService } from './prisma.service';

@Injectable()
export class PrismaHealthIndicator extends HealthIndicator {
  constructor(private prismaService: PrismaService) {
    super();
  }

  async isHealthy(key: string): Promise<HealthIndicatorResult> {
    try {
      const startTime = Date.now();

      // Test basic connectivity
      await this.prismaService.$queryRaw`SELECT 1`;

      // Test schema integrity by checking key tables exist
      await this.prismaService.$queryRaw`SELECT COUNT(*) FROM organizations LIMIT 1`;
      await this.prismaService.$queryRaw`SELECT COUNT(*) FROM users LIMIT 1`;

      const responseTime = Date.now() - startTime;

      const result = this.getStatus(key, true, {
        responseTime: `${responseTime}ms`,
        schemaVersion: 'v1.0.0',
        tablesChecked: ['organizations', 'users'],
      });

      return result;
    } catch (error) {
      throw new HealthCheckError(
        'Prisma health check failed',
        this.getStatus(key, false, {
          error: error.message,
        }),
      );
    }
  }
}
