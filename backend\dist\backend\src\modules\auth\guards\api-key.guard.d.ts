import { CanActivate, ExecutionContext } from '@nestjs/common';
import { ApiKeyService } from '../api-key.service';
import { CaslAbilityFactory } from '../casl/casl-ability.factory';
export declare class ApiKeyGuard implements CanActivate {
    private apiKeyService;
    private caslAbilityFactory;
    constructor(apiKeyService: ApiKeyService, caslAbilityFactory: CaslAbilityFactory);
    canActivate(context: ExecutionContext): Promise<boolean>;
    private extractApiKey;
}
