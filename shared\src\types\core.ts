/**
 * Core types shared across SynapseAI platform
 * These types define the fundamental data structures used throughout the system
 */

// User and Organization Types
export type UserRole = 'SUPER_ADMIN' | 'ORG_ADMIN' | 'DEVELOPER' | 'VIEWER';

export interface User {
  id: string;
  email: string;
  firstName: string | null;
  lastName: string | null;
  role: UserRole;
  organizationId: string;
  isActive: boolean;
  lastLoginAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface Organization {
  id: string;
  name: string;
  slug: string;
  settings: OrganizationSettings;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface OrganizationSettings {
  allowedDomains?: string[];
  ssoEnabled?: boolean;
  maxUsers?: number;
  features: {
    agents: boolean;
    tools: boolean;
    workflows: boolean;
    analytics: boolean;
    widgets: boolean;
  };
}

// Authentication Types
export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface JWTPayload {
  userId: string;
  organizationId: string;
  role: UserRole;
  iat: number;
  exp: number;
}

// Session Types
export interface SessionContext {
  sessionId: string;
  organizationId: string;
  userId?: string;
  agentId?: string;
  toolId?: string;
  workflowId?: string;
  context: Record<string, any>;
  createdAt: Date;
  expiresAt: Date;
  updatedAt?: Date;
}

// Execution Types
export type ExecutionStatus = 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';

export interface ExecutionResult {
  success: boolean;
  result?: any;
  error?: string;
  duration: number;
  timestamp: Date;
  metadata?: Record<string, any>;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    pagination?: PaginationMeta;
    timestamp: string;
    requestId: string;
  };
}

export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  search?: string;
}

// Error Types
export interface ErrorDetails {
  code: string;
  message: string;
  field?: string;
  value?: any;
  context?: Record<string, any>;
}

export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

// Utility Types
export type Timestamp = string; // ISO 8601 timestamp
export type UUID = string;
export type JSONValue = string | number | boolean | null | JSONObject | JSONArray;
export type JSONObject = { [key: string]: JSONValue };
export type JSONArray = JSONValue[];

// Configuration Types
export interface DatabaseConfig {
  url: string;
  ssl?: boolean;
  poolSize?: number;
  connectionTimeout?: number;
}

export interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db?: number;
  cluster?: boolean;
  nodes?: Array<{ host: string; port: number }>;
}

export interface MonitoringConfig {
  sentry?: {
    dsn: string;
    environment: string;
    tracesSampleRate: number;
  };
  prometheus?: {
    enabled: boolean;
    port: number;
    path: string;
  };
}

// Health Check Types
export interface HealthStatus {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  services: {
    database: ServiceHealth;
    redis: ServiceHealth;
    queue: ServiceHealth;
    external?: Record<string, ServiceHealth>;
  };
}

export interface ServiceHealth {
  status: 'up' | 'down' | 'degraded';
  responseTime?: number;
  error?: string;
  metadata?: Record<string, any>;
}

// Audit Types
export interface AuditLog {
  id: string;
  action: string;
  resource: string;
  resourceId?: string;
  userId: string;
  organizationId: string;
  result: 'success' | 'failure';
  details?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
}

// Notification Types
export type NotificationChannel = 'EMAIL' | 'SMS' | 'PUSH' | 'IN_APP' | 'WEBHOOK';

export interface NotificationPreferences {
  userId: string;
  channels: {
    [key in NotificationChannel]: boolean;
  };
  settings: Record<string, any>;
}

// Billing Types
export interface UsageMetrics {
  organizationId: string;
  period: {
    start: Date;
    end: Date;
  };
  metrics: {
    agentExecutions: number;
    toolCalls: number;
    workflowSteps: number;
    knowledgeQueries: number;
    widgetViews: number;
    storageUsed: number; // in bytes
    bandwidthUsed: number; // in bytes
  };
  costs: {
    total: number;
    breakdown: Record<string, number>;
  };
}

// Feature Flag Types
export interface FeatureFlag {
  key: string;
  enabled: boolean;
  rolloutPercentage?: number;
  conditions?: {
    organizationIds?: string[];
    userRoles?: UserRole[];
    environment?: string;
  };
}

// Rate Limiting Types
export interface RateLimit {
  windowMs: number;
  maxRequests: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (req: any) => string;
}

// APIX Protocol Types (Real-time WebSocket communication)
export interface APXMessage {
  id: string;
  type: APXMessageType;
  sessionId: string;
  organizationId: string;
  userId?: string;
  timestamp: string;
  payload: any;
}

export type APXMessageType =
  | 'user_message'
  | 'thinking_status'
  | 'text_chunk'
  | 'tool_call_start'
  | 'tool_call_result'
  | 'tool_call_error'
  | 'request_user_input'
  | 'user_response'
  | 'state_update'
  | 'error'
  | 'control_signal';

export interface APXResponse {
  stream?: boolean;
  chunks?: string[];
  final?: string;
  tool_call?: {
    toolId: string;
    params: Record<string, any>;
  };
  error?: string;
  state_update?: Record<string, any>;
}

// Database Entity Types
export interface Agent {
  id: string;
  name: string;
  description?: string;
  templateId?: string;
  configuration: Record<string, any>;
  isActive: boolean;
  organizationId: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Tool {
  id: string;
  name: string;
  description?: string;
  type: string;
  configuration: Record<string, any>;
  schema: Record<string, any>;
  isActive: boolean;
  organizationId: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Workflow {
  id: string;
  name: string;
  description?: string;
  definition: Record<string, any>;
  agentIds: string[];
  toolIds: string[];
  isActive: boolean;
  organizationId: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Template {
  id: string;
  name: string;
  description?: string;
  category: string;
  content: string;
  variables: Array<{
    name: string;
    type: string;
    required: boolean;
  }>;
  metadata: Record<string, any>;
  isPublic: boolean;
  organizationId: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Provider {
  id: string;
  name: string;
  type: string;
  configuration: Record<string, any>;
  credentials: Record<string, any>;
  isActive: boolean;
  organizationId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Widget {
  id: string;
  name: string;
  type: string;
  targetId: string;
  configuration: Record<string, any>;
  styling: Record<string, any>;
  embedCode?: string;
  isActive: boolean;
  organizationId: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

// Execution Types
export interface AgentExecution {
  id: string;
  agentId: string;
  sessionId?: string;
  input: Record<string, any>;
  output?: Record<string, any>;
  status: ExecutionStatus;
  startedAt: Date;
  completedAt?: Date;
  duration?: number;
  errorMessage?: string;
  metadata: Record<string, any>;
  organizationId: string;
}

export interface ToolExecution {
  id: string;
  toolId: string;
  sessionId?: string;
  input: Record<string, any>;
  output?: Record<string, any>;
  status: ExecutionStatus;
  startedAt: Date;
  completedAt?: Date;
  duration?: number;
  errorMessage?: string;
  metadata: Record<string, any>;
  organizationId: string;
}

// Multi-tenant Types
export interface TenantContext {
  organizationId: string;
  userId?: string;
  userRole?: UserRole;
  permissions?: string[];
}

export interface TenantRequest {
  organizationId?: string;
  userId?: string;
  userRole?: string;
  tenant?: TenantContext;
}
