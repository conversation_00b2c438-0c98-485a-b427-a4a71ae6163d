"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const terminus_1 = require("@nestjs/terminus");
const bull_1 = require("@nestjs/bull");
const jwt_1 = require("@nestjs/jwt");
const prisma_module_1 = require("./modules/prisma/prisma.module");
const redis_module_1 = require("./modules/redis/redis.module");
const logger_module_1 = require("./services/logger/logger.module");
const monitoring_module_1 = require("./services/monitoring/monitoring.module");
const health_module_1 = require("./modules/health/health.module");
const tenant_middleware_1 = require("@middleware/tenant.middleware");
const tenant_service_1 = require("./services/tenant/tenant.service");
const config_validation_1 = require("./utils/config/config.validation");
const auth_module_1 = require("./modules/auth/auth.module");
let AppModule = class AppModule {
    configure(consumer) {
        consumer
            .apply(tenant_middleware_1.TenantMiddleware)
            .exclude('/health', '/health/ready', '/health/live', '/api/v1/auth/login', '/api/v1/auth/register', '/api/v1/auth/refresh', '/api/docs', '/metrics')
            .forRoutes('*');
    }
    constructor() {
        console.log('SynapseAI AppModule initialized with multi-tenant microservices architecture');
    }
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: ['.env.local', '.env'],
                validationSchema: config_validation_1.configValidationSchema,
                validationOptions: {
                    allowUnknown: true,
                    abortEarly: true,
                },
            }),
            terminus_1.TerminusModule,
            health_module_1.HealthModule,
            jwt_1.JwtModule.registerAsync({
                useFactory: () => ({
                    secret: process.env.JWT_SECRET,
                    signOptions: {
                        expiresIn: process.env.JWT_EXPIRES_IN || '1h',
                    },
                }),
            }),
            logger_module_1.LoggerModule,
            monitoring_module_1.MonitoringModule,
            prisma_module_1.PrismaModule,
            redis_module_1.RedisModule,
            bull_1.BullModule.forRootAsync({
                useFactory: () => ({
                    redis: {
                        host: process.env.REDIS_HOST || 'localhost',
                        port: parseInt(process.env.REDIS_PORT || '6379'),
                        password: process.env.REDIS_PASSWORD,
                        maxRetriesPerRequest: 3,
                        retryDelayOnFailover: 100,
                        enableReadyCheck: true,
                        lazyConnect: true,
                    },
                    defaultJobOptions: {
                        removeOnComplete: 100,
                        removeOnFail: 50,
                        attempts: 3,
                        backoff: {
                            type: 'exponential',
                            delay: 2000,
                        },
                    },
                }),
            }),
            auth_module_1.AuthModule,
        ],
        controllers: [],
        providers: [tenant_service_1.TenantService],
    }),
    __metadata("design:paramtypes", [])
], AppModule);
//# sourceMappingURL=app.module.js.map