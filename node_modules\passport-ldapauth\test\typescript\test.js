"use strict";
exports.__esModule = true;
var Passport = require("passport");
var LdapStrategy = require("../../");
var Express = require("express");
var Logger = require("bunyan");
var BasicAuth = require("basic-auth");
var app = Express();
var user = { dn: 'test' };
var log = new Logger({
    name: 'ldap',
    component: 'client',
    stream: process.stderr,
    level: 'trace'
});
var options = {
    server: {
        url: 'ldap://ldap.forumsys.com:389',
        bindDN: 'cn=read-only-admin,dc=example,dc=com',
        bindCredentials: 'password',
        searchBase: 'dc=example,dc=com',
        searchFilter: '(uid={{username}})',
        log: log,
        cache: true,
        includeRaw: true,
        groupSearchFilter: '(member={{dn}})',
        groupSearchBase: 'dc=example,dc=com'
    },
    credentialsLookup: BasicAuth
};
var optionsAsFunction = function (req, callback) {
    callback(null, options);
};
var regularCallback = function (user, callback) {
    if (user.foo) {
        callback(new Error('Foo user is an error'), null, { message: 'Foo user' });
    }
    else if (!user) {
        callback(null, false, { message: 'No user' });
    }
    else {
        callback(null, user);
    }
};
var reqCallback = function (req, user, callback) {
    if (user.foo) {
        callback(new Error('Foo user is an error'), null, { message: 'Foo user' });
    }
    else if (!user) {
        callback(null, false, { message: 'No user' });
    }
    else {
        callback(null, user);
    }
};
var credentialsLookup = function (req) { return ({
    user: 'username',
    pass: 'password'
}); };
Passport.serializeUser(function (user, done) { return done(null, user.dn); });
Passport.deserializeUser(function (dn, done) { return done(null, user); });
Passport.use(new LdapStrategy(options, regularCallback));
Passport.use('withreq', new LdapStrategy(options, reqCallback));
Passport.use('dynopts', new LdapStrategy(optionsAsFunction));
var authOpts = {
    badRequestMessage: 'Bad request you did there'
};
app.post('/login', Passport.authenticate('ldapauth', authOpts));
app.post('/login', function (req, res, next) {
    Passport.authenticate('ldapauth', function (err, user) {
        req.logIn(user, function (err) {
            res.send({ ok: 1 });
        });
    })(req, res, next);
});
//# sourceMappingURL=test.js.map