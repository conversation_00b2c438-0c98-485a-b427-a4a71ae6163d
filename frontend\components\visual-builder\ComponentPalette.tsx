'use client';

import { useState, useEffect, useMemo } from 'react';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { 
  componentPalette, 
  ComponentTemplate, 
  PaletteSuggestion, 
  PaletteContext 
} from '@/lib/visual-builder/component-palette';
import { CanvasNode } from '@/lib/visual-builder/canvas';
import { useAppStore } from '@/lib/store';
import {
  MagnifyingGlassIcon,
  SparklesIcon,
  StarIcon,
  ClockIcon,
  FunnelIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/outline';

interface ComponentPaletteProps {
  context: PaletteContext;
  onComponentSelect: (template: ComponentTemplate, position?: { x: number; y: number }) => void;
  onComponentDrag?: (template: ComponentTemplate, event: React.DragEvent) => void;
  className?: string;
}

export function ComponentPalette({
  context,
  onComponentSelect,
  onComponentDrag,
  className,
}: ComponentPaletteProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedDifficulty, setSelectedDifficulty] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(true);
  const [suggestions, setSuggestions] = useState<PaletteSuggestion[]>([]);
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<ComponentTemplate | null>(null);

  const { addNotification } = useAppStore();

  // Get categories
  const categories = useMemo(() => componentPalette.getCategories(), []);

  // Get filtered templates
  const filteredTemplates = useMemo(() => {
    return componentPalette.getTemplates({
      category: selectedCategory === 'all' ? undefined : selectedCategory,
      difficulty: selectedDifficulty.length > 0 ? selectedDifficulty : undefined,
      search: searchQuery,
    });
  }, [searchQuery, selectedCategory, selectedDifficulty]);

  // Load suggestions when context changes
  useEffect(() => {
    if (showSuggestions) {
      loadSuggestions();
    }
  }, [context, showSuggestions]);

  const loadSuggestions = async () => {
    setIsLoadingSuggestions(true);
    try {
      const newSuggestions = await componentPalette.getSuggestions({
        ...context,
        searchQuery,
      });
      setSuggestions(newSuggestions);
    } catch (error) {
      console.error('Failed to load suggestions:', error);
      addNotification({
        type: 'error',
        title: 'Suggestions Failed',
        message: 'Could not load component suggestions',
      });
    } finally {
      setIsLoadingSuggestions(false);
    }
  };

  const handleComponentClick = (template: ComponentTemplate) => {
    onComponentSelect(template);
    
    // Update recent components
    const recent = context.recentComponents || [];
    const updated = [template.id, ...recent.filter(id => id !== template.id)].slice(0, 10);
    componentPalette.updateUserPreferences({ recentComponents: updated });
    
    addNotification({
      type: 'success',
      title: 'Component Added',
      message: `${template.name} added to canvas`,
    });
  };

  const handleComponentDragStart = (template: ComponentTemplate, event: React.DragEvent) => {
    event.dataTransfer.setData('application/json', JSON.stringify({
      type: 'component-template',
      templateId: template.id,
    }));
    
    onComponentDrag?.(template, event);
  };

  const handleDifficultyToggle = (difficulty: string) => {
    setSelectedDifficulty(prev => 
      prev.includes(difficulty)
        ? prev.filter(d => d !== difficulty)
        : [...prev, difficulty]
    );
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'success';
      case 'intermediate': return 'warning';
      case 'advanced': return 'error';
      default: return 'secondary';
    }
  };

  const renderComponentCard = (template: ComponentTemplate, suggestion?: PaletteSuggestion) => (
    <div
      key={template.id}
      className={`group relative border border-gray-200 rounded-lg p-3 cursor-pointer hover:border-primary-300 hover:shadow-md transition-all duration-200 ${
        selectedTemplate?.id === template.id ? 'border-primary-500 bg-primary-50' : 'bg-white'
      }`}
      draggable
      onDragStart={(e) => handleComponentDragStart(template, e)}
      onClick={() => {
        setSelectedTemplate(template);
        handleComponentClick(template);
      }}
    >
      {/* Suggestion Badge */}
      {suggestion && (
        <div className="absolute -top-2 -right-2 z-10">
          <Badge variant="info" size="sm">
            <SparklesIcon className="h-3 w-3 mr-1" />
            {Math.round(suggestion.confidence * 100)}%
          </Badge>
        </div>
      )}

      <div className="flex items-start space-x-3">
        <div className="text-2xl">{template.icon}</div>
        <div className="flex-1 min-w-0">
          <h4 className="font-medium text-sm text-gray-900 truncate">
            {template.name}
          </h4>
          <p className="text-xs text-gray-600 mt-1 line-clamp-2">
            {template.description}
          </p>
          
          <div className="flex items-center justify-between mt-2">
            <Badge variant={getDifficultyColor(template.difficulty)} size="sm">
              {template.difficulty}
            </Badge>
            
            <div className="flex items-center space-x-1 text-xs text-gray-500">
              <StarIcon className="h-3 w-3" />
              <span>{template.popularity}</span>
            </div>
          </div>

          {/* Tags */}
          {template.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {template.tags.slice(0, 3).map((tag, index) => (
                <Badge key={index} variant="outline" size="sm">
                  {tag}
                </Badge>
              ))}
              {template.tags.length > 3 && (
                <Badge variant="outline" size="sm">
                  +{template.tags.length - 3}
                </Badge>
              )}
            </div>
          )}

          {/* Suggestion Reasoning */}
          {suggestion && (
            <div className="mt-2 text-xs text-blue-600 italic">
              {suggestion.reasoning}
            </div>
          )}
        </div>
      </div>

      {/* Hover Actions */}
      <div className="absolute inset-0 bg-primary-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg flex items-center justify-center">
        <Button size="sm" variant="outline" className="bg-white">
          Add to Canvas
        </Button>
      </div>
    </div>
  );

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <span>Component Palette</span>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => setShowSuggestions(!showSuggestions)}
            leftIcon={<SparklesIcon className="h-4 w-4" />}
          >
            {showSuggestions ? 'Hide' : 'Show'} AI
          </Button>
        </CardTitle>

        {/* Search */}
        <Input
          placeholder="Search components..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          leftIcon={<MagnifyingGlassIcon className="h-4 w-4" />}
          className="mb-3"
        />

        {/* Filters */}
        <div className="space-y-3">
          {/* Category Filter */}
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Category
            </label>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="w-full text-sm border border-gray-300 rounded px-2 py-1"
            >
              <option value="all">All Categories</option>
              {categories.map((category) => (
                <option key={category.name} value={category.name.toLowerCase()}>
                  {category.icon} {category.name} ({category.count})
                </option>
              ))}
            </select>
          </div>

          {/* Difficulty Filter */}
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Difficulty
            </label>
            <div className="flex space-x-2">
              {['beginner', 'intermediate', 'advanced'].map((difficulty) => (
                <Button
                  key={difficulty}
                  size="sm"
                  variant={selectedDifficulty.includes(difficulty) ? 'default' : 'outline'}
                  onClick={() => handleDifficultyToggle(difficulty)}
                  className="capitalize"
                >
                  {difficulty}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* AI Suggestions */}
        {showSuggestions && (
          <div>
            <div className="flex items-center space-x-2 mb-3">
              <SparklesIcon className="h-4 w-4 text-primary-600" />
              <h4 className="font-medium text-sm">AI Suggestions</h4>
              {isLoadingSuggestions && (
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-primary-600" />
              )}
            </div>

            {suggestions.length > 0 ? (
              <div className="space-y-2 mb-4">
                {suggestions.slice(0, 3).map((suggestion) => 
                  renderComponentCard(suggestion.template, suggestion)
                )}
              </div>
            ) : (
              <div className="text-center py-4 text-gray-500">
                <SparklesIcon className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-xs">No suggestions available</p>
              </div>
            )}
          </div>
        )}

        {/* Popular Components */}
        {!searchQuery && selectedCategory === 'all' && (
          <div>
            <div className="flex items-center space-x-2 mb-3">
              <StarIcon className="h-4 w-4 text-yellow-500" />
              <h4 className="font-medium text-sm">Popular</h4>
            </div>
            <div className="space-y-2 mb-4">
              {componentPalette.getPopularTemplates(3).map((template) => 
                renderComponentCard(template)
              )}
            </div>
          </div>
        )}

        {/* Recent Components */}
        {context.recentComponents && context.recentComponents.length > 0 && (
          <div>
            <div className="flex items-center space-x-2 mb-3">
              <ClockIcon className="h-4 w-4 text-gray-500" />
              <h4 className="font-medium text-sm">Recent</h4>
            </div>
            <div className="space-y-2 mb-4">
              {componentPalette.getRecentTemplates(context.recentComponents.slice(0, 3)).map((template) => 
                renderComponentCard(template)
              )}
            </div>
          </div>
        )}

        {/* All Components */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <h4 className="font-medium text-sm">
              All Components ({filteredTemplates.length})
            </h4>
            <Button
              size="sm"
              variant="ghost"
              leftIcon={<FunnelIcon className="h-4 w-4" />}
            >
              Filter
            </Button>
          </div>

          {filteredTemplates.length > 0 ? (
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {filteredTemplates.map((template) => renderComponentCard(template))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <MagnifyingGlassIcon className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p className="text-sm font-medium">No components found</p>
              <p className="text-xs">Try adjusting your search or filters</p>
            </div>
          )}
        </div>

        {/* Component Details */}
        {selectedTemplate && (
          <div className="border-t pt-4">
            <div className="flex items-center space-x-2 mb-2">
              <InformationCircleIcon className="h-4 w-4 text-blue-500" />
              <h4 className="font-medium text-sm">Component Details</h4>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-3 text-xs">
              <div className="font-medium mb-1">{selectedTemplate.name}</div>
              <div className="text-gray-600 mb-2">{selectedTemplate.description}</div>
              
              <div className="space-y-1">
                <div><strong>Category:</strong> {selectedTemplate.category}</div>
                <div><strong>Difficulty:</strong> {selectedTemplate.difficulty}</div>
                <div><strong>Inputs:</strong> {selectedTemplate.template.inputs.length}</div>
                <div><strong>Outputs:</strong> {selectedTemplate.template.outputs.length}</div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
