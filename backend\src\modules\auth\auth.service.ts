import { Injectable, UnauthorizedException, ConflictException, BadRequestException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcryptjs';
import { PrismaService } from '@modules/prisma/prisma.service';
import { RedisService } from '@modules/redis/redis.service';
import { LoggerService } from '@services/logger/logger.service';
import { UserRole } from '@prisma/client';

export interface JwtPayload {
  userId: string;
  organizationId: string;
  role: UserRole;
  email: string;
  iat?: number;
  exp?: number;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface LoginDto {
  email: string;
  password: string;
  organizationId: string;
}

export interface RegisterDto {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  organizationId: string;
  role?: UserRole;
}

@Injectable()
export class AuthService {
  private readonly saltRounds = 12;
  private readonly refreshTokenExpiry = 7 * 24 * 60 * 60; // 7 days in seconds

  constructor(
    private jwtService: JwtService,
    private prismaService: PrismaService,
    private redisService: RedisService,
    private configService: ConfigService,
    private logger: LoggerService,
  ) {}

  /**
   * Validate user credentials for login
   */
  async validateUser(email: string, password: string, organizationId: string): Promise<any> {
    try {
      const user = await this.prismaService.user.findFirst({
        where: {
          email,
          organizationId,
          isActive: true,
        },
        include: {
          organization: true,
        },
      });

      if (!user || !user.passwordHash) {
        throw new UnauthorizedException('Invalid credentials');
      }

      const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
      if (!isPasswordValid) {
        throw new UnauthorizedException('Invalid credentials');
      }

      // Update last login
      await this.prismaService.user.update({
        where: { id: user.id },
        data: { lastLoginAt: new Date() },
      });

      const { passwordHash, ...result } = user;
      return result;
    } catch (error) {
      this.logger.error('User validation failed:', error);
      throw new UnauthorizedException('Invalid credentials');
    }
  }

  /**
   * Login user and generate tokens
   */
  async login(loginDto: LoginDto): Promise<AuthTokens> {
    const user = await this.validateUser(loginDto.email, loginDto.password, loginDto.organizationId);

    const payload: JwtPayload = {
      userId: user.id,
      organizationId: user.organizationId,
      role: user.role,
      email: user.email,
    };

    const accessToken = this.jwtService.sign(payload);
    const refreshToken = await this.generateRefreshToken(user.id, user.organizationId);

    // Log successful login
    this.logger.audit('USER_LOGIN', 'success', {
      userId: user.id,
      organizationId: user.organizationId,
      resource: 'user',
      details: {
        email: user.email,
        role: user.role,
      },
    });

    return {
      accessToken,
      refreshToken,
      expiresIn: parseInt(this.configService.get('JWT_EXPIRES_IN', '3600')),
    };
  }

  /**
   * Register new user
   */
  async register(registerDto: RegisterDto): Promise<AuthTokens> {
    // Validate organization exists
    const organization = await this.prismaService.organization.findFirst({
      where: {
        id: registerDto.organizationId,
        isActive: true,
      },
    });

    if (!organization) {
      throw new BadRequestException('Invalid organization');
    }

    // Check if user already exists
    const existingUser = await this.prismaService.user.findFirst({
      where: {
        email: registerDto.email,
        organizationId: registerDto.organizationId,
      },
    });

    if (existingUser) {
      throw new ConflictException('User already exists');
    }

    // Validate password strength
    this.validatePasswordStrength(registerDto.password);

    // Hash password
    const passwordHash = await bcrypt.hash(registerDto.password, this.saltRounds);

    // Create user
    const user = await this.prismaService.user.create({
      data: {
        email: registerDto.email,
        passwordHash,
        firstName: registerDto.firstName,
        lastName: registerDto.lastName,
        organizationId: registerDto.organizationId,
        role: registerDto.role || UserRole.VIEWER,
        isActive: true,
      },
      include: {
        organization: true,
      },
    });

    // Generate tokens
    const payload: JwtPayload = {
      userId: user.id,
      organizationId: user.organizationId,
      role: user.role,
      email: user.email,
    };

    const accessToken = this.jwtService.sign(payload);
    const refreshToken = await this.generateRefreshToken(user.id, user.organizationId);

    // Log successful registration
    this.logger.audit('USER_REGISTER', 'success', {
      userId: user.id,
      organizationId: user.organizationId,
      resource: 'user',
      details: {
        email: user.email,
        role: user.role,
      },
    });

    return {
      accessToken,
      refreshToken,
      expiresIn: parseInt(this.configService.get('JWT_EXPIRES_IN', '3600')),
    };
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshToken(refreshToken: string): Promise<AuthTokens> {
    try {
      // Verify refresh token exists in Redis
      const tokenData = await this.redisService.get(`refresh_token:${refreshToken}`);
      if (!tokenData) {
        throw new UnauthorizedException('Invalid refresh token');
      }

      const { userId, organizationId } = JSON.parse(tokenData);

      // Get user data
      const user = await this.prismaService.user.findFirst({
        where: {
          id: userId,
          organizationId,
          isActive: true,
        },
      });

      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      // Generate new tokens
      const payload: JwtPayload = {
        userId: user.id,
        organizationId: user.organizationId,
        role: user.role,
        email: user.email,
      };

      const newAccessToken = this.jwtService.sign(payload);
      const newRefreshToken = await this.generateRefreshToken(user.id, user.organizationId);

      // Invalidate old refresh token
      await this.redisService.del(`refresh_token:${refreshToken}`);

      return {
        accessToken: newAccessToken,
        refreshToken: newRefreshToken,
        expiresIn: parseInt(this.configService.get('JWT_EXPIRES_IN', '3600')),
      };
    } catch (error) {
      this.logger.error('Token refresh failed:', error);
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  /**
   * Logout user and invalidate tokens
   */
  async logout(userId: string, refreshToken?: string): Promise<void> {
    try {
      // Add access token to blacklist (if provided)
      // Note: In production, you might want to implement JWT blacklisting

      // Remove refresh token from Redis
      if (refreshToken) {
        await this.redisService.del(`refresh_token:${refreshToken}`);
      }

      // Remove all refresh tokens for user (optional - for logout from all devices)
      const pattern = `refresh_token:*:${userId}:*`;
      const keys = await this.redisService.keys(pattern);
      if (keys.length > 0) {
        await this.redisService.del(...keys);
      }

      this.logger.audit('USER_LOGOUT', 'success', {
        userId,
        resource: 'user',
      });
    } catch (error) {
      this.logger.error('Logout failed:', error);
    }
  }

  /**
   * Generate refresh token and store in Redis
   */
  private async generateRefreshToken(userId: string, organizationId: string): Promise<string> {
    const tokenId = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const refreshToken = `${userId}_${organizationId}_${tokenId}`;

    const tokenData = {
      userId,
      organizationId,
      createdAt: new Date().toISOString(),
    };

    await this.redisService.setex(
      `refresh_token:${refreshToken}`,
      this.refreshTokenExpiry,
      JSON.stringify(tokenData),
    );

    return refreshToken;
  }

  /**
   * Validate password strength
   */
  private validatePasswordStrength(password: string): void {
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    if (password.length < minLength) {
      throw new BadRequestException('Password must be at least 8 characters long');
    }

    if (!hasUpperCase) {
      throw new BadRequestException('Password must contain at least one uppercase letter');
    }

    if (!hasLowerCase) {
      throw new BadRequestException('Password must contain at least one lowercase letter');
    }

    if (!hasNumbers) {
      throw new BadRequestException('Password must contain at least one number');
    }

    if (!hasSpecialChar) {
      throw new BadRequestException('Password must contain at least one special character');
    }
  }

  /**
   * Verify JWT token
   */
  async verifyToken(token: string): Promise<JwtPayload> {
    try {
      return this.jwtService.verify(token);
    } catch (error) {
      throw new UnauthorizedException('Invalid token');
    }
  }
}
