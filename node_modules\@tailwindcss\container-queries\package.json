{"name": "@tailwindcss/container-queries", "version": "0.1.1", "main": "dist/index.js", "types": "dist/index.d.ts", "license": "MIT", "repository": "https://github.com/tailwindlabs/tailwindcss-container-queries", "publishConfig": {"access": "public"}, "scripts": {"test": "jest", "swcify": "swc ./src/index.ts --out-dir ./dist", "build": "npm run swcify", "dev": "npm run swcify -- --watch", "postbuild": "tsc --emitDeclarationOnly", "prepublishOnly": "npm run build", "release-channel": "node ./scripts/release-channel.js", "release-notes": "node ./scripts/release-notes.js"}, "prettier": {"printWidth": 100, "semi": false, "singleQuote": true, "trailingComma": "es5"}, "jest": {"setupFilesAfterEnv": ["<rootDir>/jest/custom-matchers.js"], "transform": {"^.+\\.(t|j)sx?$": "@swc/jest"}, "moduleNameMapper": {"^~/(.*)": "<rootDir>/$1"}}, "files": ["dist"], "peerDependencies": {"tailwindcss": ">=3.2.0"}, "devDependencies": {"@swc/cli": "^0.1.57", "@swc/core": "^1.3.7", "@swc/jest": "^0.2.23", "@types/jest": "^29.1.2", "jest": "^29.1.2", "postcss": "^8.4.17", "prettier": "^2.7.1", "tailwindcss": "0.0.0-insiders.3011f46", "typescript": "^4.8.4"}}