import { ConfigService } from '@nestjs/config';
import * as Sen<PERSON> from '@sentry/node';
export declare class SentryService {
    private configService;
    private readonly logger;
    private initialized;
    constructor(configService: ConfigService);
    init(): void;
    captureException(error: Error, context?: {
        user?: {
            id: string;
            email?: string;
            organizationId?: string;
        };
        tags?: Record<string, string>;
        extra?: Record<string, any>;
        level?: Sentry.SeverityLevel;
    }): string | undefined;
    captureMessage(message: string, level?: Sentry.SeverityLevel, context?: {
        user?: {
            id: string;
            email?: string;
            organizationId?: string;
        };
        tags?: Record<string, string>;
        extra?: Record<string, any>;
    }): string | undefined;
    startTransaction(name: string, op: string, description?: string): Sentry.Transaction | undefined;
    addBreadcrumb(message: string, category: string, level?: Sentry.SeverityLevel, data?: Record<string, any>): void;
    setUser(user: {
        id: string;
        email?: string;
        organizationId?: string;
        role?: string;
    }): void;
    setTags(tags: Record<string, string>): void;
    setContext(key: string, context: Record<string, any>): void;
    flush(timeout?: number): Promise<boolean>;
    close(timeout?: number): Promise<boolean>;
    isInitialized(): boolean;
}
