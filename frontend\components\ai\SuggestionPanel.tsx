'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { suggestionEngine, ConfigSuggestion, SuggestionContext, SuggestionFilter } from '@/lib/ai/suggestion-engine';
import { useAppStore } from '@/lib/store';
import {
  LightBulbIcon,
  CheckIcon,
  XMarkIcon,
  AdjustmentsHorizontalIcon,
  ChartBarIcon,
  ShieldCheckIcon,
  SparklesIcon,
} from '@heroicons/react/24/outline';

interface SuggestionPanelProps {
  context: SuggestionContext;
  onApplySuggestion: (suggestion: ConfigSuggestion, newConfig: Record<string, any>) => void;
  className?: string;
  autoRefresh?: boolean;
  showFilters?: boolean;
}

export function SuggestionPanel({
  context,
  onApplySuggestion,
  className,
  autoRefresh = true,
  showFilters = true,
}: SuggestionPanelProps) {
  const [suggestions, setSuggestions] = useState<ConfigSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [filter, setFilter] = useState<SuggestionFilter>({
    maxSuggestions: 10,
    excludeApplied: true,
  });
  const [showFilterPanel, setShowFilterPanel] = useState(false);
  const [stats, setStats] = useState(suggestionEngine.getStats());

  const { addNotification } = useAppStore();

  useEffect(() => {
    if (autoRefresh) {
      loadSuggestions();
    }
  }, [context.currentConfig, filter, autoRefresh]);

  const loadSuggestions = async () => {
    setIsLoading(true);
    try {
      const newSuggestions = await suggestionEngine.getSuggestions(context, filter);
      setSuggestions(newSuggestions);
      setStats(suggestionEngine.getStats());
    } catch (error) {
      console.error('Failed to load suggestions:', error);
      addNotification({
        type: 'error',
        title: 'Suggestion Loading Failed',
        message: 'Could not load configuration suggestions',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleApplySuggestion = async (suggestion: ConfigSuggestion) => {
    try {
      const newConfig = suggestionEngine.applySuggestion(suggestion, context.currentConfig);
      onApplySuggestion(suggestion, newConfig);
      
      // Remove applied suggestion from list
      setSuggestions(prev => prev.filter(s => s.id !== suggestion.id));
      setStats(suggestionEngine.getStats());
      
      addNotification({
        type: 'success',
        title: 'Suggestion Applied',
        message: suggestion.title,
      });
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Application Failed',
        message: 'Could not apply the suggestion',
      });
    }
  };

  const handleDismissSuggestion = (suggestion: ConfigSuggestion) => {
    suggestionEngine.dismissSuggestion(suggestion.id);
    setSuggestions(prev => prev.filter(s => s.id !== suggestion.id));
    setStats(suggestionEngine.getStats());
    
    addNotification({
      type: 'info',
      title: 'Suggestion Dismissed',
      message: suggestion.title,
    });
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'optimization': return <ChartBarIcon className="h-4 w-4" />;
      case 'security': return <ShieldCheckIcon className="h-4 w-4" />;
      case 'completion': return <LightBulbIcon className="h-4 w-4" />;
      case 'best_practice': return <SparklesIcon className="h-4 w-4" />;
      default: return <AdjustmentsHorizontalIcon className="h-4 w-4" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'secondary';
      default: return 'secondary';
    }
  };

  const getImpactDisplay = (impact: ConfigSuggestion['impact']) => {
    const impacts = [];
    if (impact.performance) impacts.push(`Performance: +${Math.round(impact.performance * 100)}%`);
    if (impact.security) impacts.push(`Security: +${Math.round(impact.security * 100)}%`);
    if (impact.usability) impacts.push(`Usability: +${Math.round(impact.usability * 100)}%`);
    if (impact.maintainability) impacts.push(`Maintainability: +${Math.round(impact.maintainability * 100)}%`);
    return impacts.join(', ');
  };

  const groupedSuggestions = suggestions.reduce((groups, suggestion) => {
    const key = suggestion.priority;
    if (!groups[key]) groups[key] = [];
    groups[key].push(suggestion);
    return groups;
  }, {} as Record<string, ConfigSuggestion[]>);

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <SparklesIcon className="h-5 w-5 text-primary-600" />
            <span>AI Suggestions</span>
            {suggestions.length > 0 && (
              <Badge variant="info" size="sm">
                {suggestions.length}
              </Badge>
            )}
          </CardTitle>
          
          <div className="flex items-center space-x-2">
            {showFilters && (
              <Button
                size="sm"
                variant="outline"
                onClick={() => setShowFilterPanel(!showFilterPanel)}
                leftIcon={<AdjustmentsHorizontalIcon className="h-4 w-4" />}
              >
                Filters
              </Button>
            )}
            <Button
              size="sm"
              onClick={loadSuggestions}
              loading={isLoading}
            >
              Refresh
            </Button>
          </div>
        </div>

        {/* Stats */}
        {stats.totalApplied > 0 && (
          <div className="flex items-center space-x-4 text-sm text-gray-600">
            <span>Applied: {stats.totalApplied}</span>
            <span>Success Rate: {Math.round(stats.successRate * 100)}%</span>
          </div>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Filter Panel */}
        {showFilterPanel && (
          <div className="p-4 bg-gray-50 rounded-lg space-y-3">
            <h4 className="font-medium text-sm">Filter Suggestions</h4>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Types
                </label>
                <div className="space-y-1">
                  {['optimization', 'completion', 'validation', 'best_practice', 'security'].map(type => (
                    <label key={type} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={filter.types?.includes(type) ?? true}
                        onChange={(e) => {
                          const types = filter.types || [];
                          if (e.target.checked) {
                            setFilter(prev => ({ ...prev, types: [...types, type] }));
                          } else {
                            setFilter(prev => ({ ...prev, types: types.filter(t => t !== type) }));
                          }
                        }}
                        className="rounded"
                      />
                      <span className="text-xs capitalize">{type.replace('_', ' ')}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Priorities
                </label>
                <div className="space-y-1">
                  {['critical', 'high', 'medium', 'low'].map(priority => (
                    <label key={priority} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={filter.priorities?.includes(priority) ?? true}
                        onChange={(e) => {
                          const priorities = filter.priorities || [];
                          if (e.target.checked) {
                            setFilter(prev => ({ ...prev, priorities: [...priorities, priority] }));
                          } else {
                            setFilter(prev => ({ ...prev, priorities: priorities.filter(p => p !== priority) }));
                          }
                        }}
                        className="rounded"
                      />
                      <span className="text-xs capitalize">{priority}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <label className="flex items-center space-x-2">
                <span className="text-xs font-medium text-gray-700">Min Confidence:</span>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={filter.minConfidence || 0}
                  onChange={(e) => setFilter(prev => ({ ...prev, minConfidence: parseFloat(e.target.value) }))}
                  className="w-20"
                />
                <span className="text-xs text-gray-600">
                  {Math.round((filter.minConfidence || 0) * 100)}%
                </span>
              </label>
            </div>
          </div>
        )}

        {/* Loading State */}
        {isLoading && (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600" />
            <span className="ml-2 text-sm text-gray-600">Loading suggestions...</span>
          </div>
        )}

        {/* No Suggestions */}
        {!isLoading && suggestions.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <LightBulbIcon className="h-12 w-12 mx-auto mb-3 opacity-50" />
            <p className="text-sm font-medium">No suggestions available</p>
            <p className="text-xs">Your configuration looks good!</p>
          </div>
        )}

        {/* Suggestions by Priority */}
        {Object.entries(groupedSuggestions).map(([priority, prioritySuggestions]) => (
          <div key={priority} className="space-y-3">
            <h4 className="font-medium text-sm flex items-center space-x-2">
              <Badge variant={getPriorityColor(priority)} size="sm">
                {priority}
              </Badge>
              <span>{prioritySuggestions.length} suggestion{prioritySuggestions.length !== 1 ? 's' : ''}</span>
            </h4>

            {prioritySuggestions.map((suggestion) => (
              <div
                key={suggestion.id}
                className="border border-gray-200 rounded-lg p-4 space-y-3"
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3 flex-1">
                    <div className="mt-0.5">
                      {getTypeIcon(suggestion.type)}
                    </div>
                    <div className="flex-1">
                      <h5 className="font-medium text-sm">{suggestion.title}</h5>
                      <p className="text-xs text-gray-600 mt-1">{suggestion.description}</p>
                      
                      {suggestion.reasoning && (
                        <p className="text-xs text-gray-500 italic mt-2">
                          {suggestion.reasoning}
                        </p>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary" size="sm">
                      {Math.round(suggestion.confidence * 100)}%
                    </Badge>
                  </div>
                </div>

                {/* Impact Display */}
                {Object.keys(suggestion.impact).length > 0 && (
                  <div className="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
                    Expected impact: {getImpactDisplay(suggestion.impact)}
                  </div>
                )}

                {/* Tags */}
                {suggestion.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {suggestion.tags.map((tag, index) => (
                      <Badge key={index} variant="outline" size="sm">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                )}

                {/* Examples */}
                {suggestion.examples && suggestion.examples.length > 0 && (
                  <div className="text-xs">
                    <details className="cursor-pointer">
                      <summary className="font-medium text-gray-700">View examples</summary>
                      <div className="mt-2 space-y-2">
                        {suggestion.examples.map((example, index) => (
                          <div key={index} className="bg-gray-50 p-2 rounded">
                            <div className="font-medium">Before:</div>
                            <pre className="text-xs text-gray-600">{JSON.stringify(example.before, null, 2)}</pre>
                            <div className="font-medium mt-1">After:</div>
                            <pre className="text-xs text-gray-600">{JSON.stringify(example.after, null, 2)}</pre>
                            <div className="text-xs text-gray-500 mt-1">{example.explanation}</div>
                          </div>
                        ))}
                      </div>
                    </details>
                  </div>
                )}

                {/* Actions */}
                <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      onClick={() => handleApplySuggestion(suggestion)}
                      leftIcon={<CheckIcon className="h-3 w-3" />}
                    >
                      Apply
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleDismissSuggestion(suggestion)}
                      leftIcon={<XMarkIcon className="h-3 w-3" />}
                    >
                      Dismiss
                    </Button>
                  </div>
                  
                  <Badge variant="outline" size="sm">
                    {suggestion.type.replace('_', ' ')}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
