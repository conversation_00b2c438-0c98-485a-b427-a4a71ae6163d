{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "noImplicitAny": true, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@/*": ["src/*"], "@modules/*": ["src/modules/*"], "@services/*": ["src/services/*"], "@utils/*": ["src/utils/*"], "@schemas/*": ["src/schemas/*"], "@shared/*": ["../shared/*"]}}, "include": ["src/**/*", "../shared/**/*"], "exclude": ["node_modules", "dist", "test", "**/*spec.ts"]}