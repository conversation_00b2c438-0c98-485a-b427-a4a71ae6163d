{"name": "ldapauth-fork", "version": "5.0.5", "main": "./lib/ldapauth.js", "types": "./lib/ldapauth.d.ts", "description": "Authenticate against an LDAP server", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "keywords": ["authenticate", "ldap", "authentication", "auth"], "repository": {"type": "git", "url": "git://github.com/vesse/node-ldapauth-fork.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"prepublishOnly": "npm run lint", "lint": "eslint ./lib", "lint:watch": "watch 'npm run lint' ./lib --wait 0.5", "prettier:fix": "prettier --write '**/*.{js,json,md,ts}'", "prettier": "prettier --list-different '**/*.{js,json,md,ts}'", "pretest": "cd test && tsc", "test": "cd test && node test.js"}, "dependencies": {"@types/ldapjs": "^2.2.2", "bcryptjs": "^2.4.0", "ldapjs": "^2.2.1", "lru-cache": "^7.10.1"}, "devDependencies": {"@types/bunyan": "^1.8.6", "@types/node": "^14.14.7", "bunyan": "^1.8.14", "eslint": "^8.5.0", "prettier": "^2.7.1", "typescript": "^4.0.5", "watch": "^1.0.2"}}