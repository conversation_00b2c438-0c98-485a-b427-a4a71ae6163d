"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DEFAULT_CONFIG = exports.CACHE_KEYS = exports.QUEUE_NAMES = exports.WEBSOCKET_EVENTS = exports.HTTP_STATUS = exports.ERROR_CODES = exports.VALIDATION_RULES = exports.ALLOWED_FILE_TYPES = exports.MAX_FILE_SIZE = exports.AUTH_RATE_LIMIT = exports.DEFAULT_RATE_LIMIT = exports.SESSION_CLEANUP_INTERVAL = exports.SESSION_TIMEOUT = exports.MAX_PAGE_SIZE = exports.DEFAULT_PAGE_SIZE = exports.API_PREFIX = exports.API_VERSION = void 0;
exports.API_VERSION = 'v1';
exports.API_PREFIX = `/api/${exports.API_VERSION}`;
exports.DEFAULT_PAGE_SIZE = 20;
exports.MAX_PAGE_SIZE = 100;
exports.SESSION_TIMEOUT = 24 * 60 * 60 * 1000;
exports.SESSION_CLEANUP_INTERVAL = 60 * 60 * 1000;
exports.DEFAULT_RATE_LIMIT = {
    windowMs: 15 * 60 * 1000,
    maxRequests: 100,
};
exports.AUTH_RATE_LIMIT = {
    windowMs: 15 * 60 * 1000,
    maxRequests: 5,
};
exports.MAX_FILE_SIZE = 100 * 1024 * 1024;
exports.ALLOWED_FILE_TYPES = [
    'application/pdf',
    'text/plain',
    'text/csv',
    'application/json',
    'image/jpeg',
    'image/png',
    'image/webp',
];
exports.VALIDATION_RULES = {
    email: {
        maxLength: 255,
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    },
    password: {
        minLength: 8,
        maxLength: 128,
        pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    },
    organizationSlug: {
        minLength: 3,
        maxLength: 50,
        pattern: /^[a-z0-9-]+$/,
    },
    name: {
        minLength: 1,
        maxLength: 100,
    },
};
exports.ERROR_CODES = {
    INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
    TOKEN_EXPIRED: 'TOKEN_EXPIRED',
    TOKEN_INVALID: 'TOKEN_INVALID',
    UNAUTHORIZED: 'UNAUTHORIZED',
    FORBIDDEN: 'FORBIDDEN',
    VALIDATION_ERROR: 'VALIDATION_ERROR',
    INVALID_INPUT: 'INVALID_INPUT',
    MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
    RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
    RESOURCE_ALREADY_EXISTS: 'RESOURCE_ALREADY_EXISTS',
    RESOURCE_CONFLICT: 'RESOURCE_CONFLICT',
    INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',
    SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
    DATABASE_ERROR: 'DATABASE_ERROR',
    EXTERNAL_SERVICE_ERROR: 'EXTERNAL_SERVICE_ERROR',
    RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
    ORGANIZATION_LIMIT_EXCEEDED: 'ORGANIZATION_LIMIT_EXCEEDED',
    FEATURE_NOT_AVAILABLE: 'FEATURE_NOT_AVAILABLE',
};
exports.HTTP_STATUS = {
    OK: 200,
    CREATED: 201,
    NO_CONTENT: 204,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    CONFLICT: 409,
    UNPROCESSABLE_ENTITY: 422,
    TOO_MANY_REQUESTS: 429,
    INTERNAL_SERVER_ERROR: 500,
    SERVICE_UNAVAILABLE: 503,
};
exports.WEBSOCKET_EVENTS = {
    CONNECTION: 'connection',
    DISCONNECT: 'disconnect',
    ERROR: 'error',
    USER_MESSAGE: 'user_message',
    THINKING_STATUS: 'thinking_status',
    TEXT_CHUNK: 'text_chunk',
    TOOL_CALL_START: 'tool_call_start',
    TOOL_CALL_RESULT: 'tool_call_result',
    TOOL_CALL_ERROR: 'tool_call_error',
    REQUEST_USER_INPUT: 'request_user_input',
    USER_RESPONSE: 'user_response',
    STATE_UPDATE: 'state_update',
    CONTROL_SIGNAL: 'control_signal',
};
exports.QUEUE_NAMES = {
    AGENT_EXECUTION: 'agent-execution',
    TOOL_EXECUTION: 'tool-execution',
    WORKFLOW_EXECUTION: 'workflow-execution',
    NOTIFICATION: 'notification',
    ANALYTICS: 'analytics',
    BILLING: 'billing',
};
exports.CACHE_KEYS = {
    USER_SESSION: (sessionId) => `session:${sessionId}`,
    USER_PERMISSIONS: (userId) => `permissions:${userId}`,
    ORGANIZATION_SETTINGS: (orgId) => `org:${orgId}:settings`,
    RATE_LIMIT: (key) => `rate_limit:${key}`,
    FEATURE_FLAGS: (orgId) => `feature_flags:${orgId}`,
};
exports.DEFAULT_CONFIG = {
    jwt: {
        expiresIn: '1h',
        refreshExpiresIn: '7d',
    },
    pagination: {
        defaultPage: 1,
        defaultLimit: exports.DEFAULT_PAGE_SIZE,
        maxLimit: exports.MAX_PAGE_SIZE,
    },
    upload: {
        maxFileSize: exports.MAX_FILE_SIZE,
        allowedTypes: exports.ALLOWED_FILE_TYPES,
    },
};
//# sourceMappingURL=constants.js.map