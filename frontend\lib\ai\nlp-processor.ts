import { apiClient } from '@/lib/api';

export interface IntentEntity {
  type: 'action' | 'object' | 'property' | 'value' | 'modifier';
  value: string;
  confidence: number;
  startIndex: number;
  endIndex: number;
  metadata?: Record<string, any>;
}

export interface IntentAnalysis {
  intent: string;
  confidence: number;
  entities: IntentEntity[];
  context: {
    domain: string;
    action: string;
    target?: string;
    parameters?: Record<string, any>;
  };
  suggestedActions: Array<{
    type: 'create' | 'modify' | 'delete' | 'navigate' | 'help';
    description: string;
    confidence: number;
    payload?: Record<string, any>;
  }>;
  alternatives: Array<{
    intent: string;
    confidence: number;
    reasoning: string;
  }>;
}

export interface NLPContext {
  currentPage?: string;
  userRole?: string;
  recentActions?: string[];
  availableActions?: string[];
  domainContext?: Record<string, any>;
}

class NLPProcessor {
  private intentPatterns: Map<string, RegExp[]> = new Map();
  private entityPatterns: Map<string, RegExp> = new Map();
  private actionKeywords: Map<string, string[]> = new Map();

  constructor() {
    this.initializePatterns();
  }

  /**
   * Analyze user input to extract intent and entities
   */
  async analyzeIntent(
    input: string,
    context: NLPContext = {}
  ): Promise<IntentAnalysis> {
    try {
      // Try AI-powered analysis first
      const aiAnalysis = await this.getAIAnalysis(input, context);
      if (aiAnalysis) {
        return aiAnalysis;
      }
    } catch (error) {
      console.warn('AI analysis failed, falling back to rule-based analysis:', error);
    }

    // Fallback to rule-based analysis
    return this.getRuleBasedAnalysis(input, context);
  }

  /**
   * Extract entities from text
   */
  extractEntities(text: string): IntentEntity[] {
    const entities: IntentEntity[] = [];
    const normalizedText = text.toLowerCase();

    // Extract different types of entities
    this.entityPatterns.forEach((pattern, type) => {
      let match;
      while ((match = pattern.exec(normalizedText)) !== null) {
        entities.push({
          type: type as any,
          value: match[0],
          confidence: 0.8,
          startIndex: match.index,
          endIndex: match.index + match[0].length,
        });
      }
    });

    // Sort by position and remove overlaps
    return this.deduplicateEntities(entities);
  }

  /**
   * Get intent suggestions based on partial input
   */
  async getSuggestions(
    partialInput: string,
    context: NLPContext = {}
  ): Promise<Array<{
    completion: string;
    intent: string;
    confidence: number;
    description: string;
  }>> {
    try {
      const response = await apiClient.post('/api/v1/ai/intent-suggestions', {
        input: partialInput,
        context,
      });
      return response.data.suggestions;
    } catch (error) {
      return this.getRuleBasedSuggestions(partialInput, context);
    }
  }

  /**
   * Validate if an intent is actionable in current context
   */
  validateIntent(analysis: IntentAnalysis, context: NLPContext): {
    isValid: boolean;
    issues: string[];
    suggestions: string[];
  } {
    const issues: string[] = [];
    const suggestions: string[] = [];

    // Check if action is available in current context
    if (context.availableActions && !context.availableActions.includes(analysis.context.action)) {
      issues.push(`Action "${analysis.context.action}" is not available in current context`);
      suggestions.push('Try a different action or navigate to the appropriate page');
    }

    // Check confidence threshold
    if (analysis.confidence < 0.6) {
      issues.push('Intent confidence is low');
      suggestions.push('Try being more specific or use different wording');
    }

    // Check for required entities
    const requiredEntities = this.getRequiredEntities(analysis.intent);
    const missingEntities = requiredEntities.filter(
      required => !analysis.entities.some(entity => entity.type === required)
    );

    if (missingEntities.length > 0) {
      issues.push(`Missing required information: ${missingEntities.join(', ')}`);
      suggestions.push('Please provide more details about what you want to do');
    }

    return {
      isValid: issues.length === 0,
      issues,
      suggestions,
    };
  }

  /**
   * Convert intent analysis to actionable commands
   */
  intentToActions(analysis: IntentAnalysis): Array<{
    type: string;
    payload: Record<string, any>;
    description: string;
    confidence: number;
  }> {
    const actions: Array<{
      type: string;
      payload: Record<string, any>;
      description: string;
      confidence: number;
    }> = [];

    switch (analysis.intent) {
      case 'create_agent':
        actions.push({
          type: 'navigate',
          payload: { path: '/agents/new' },
          description: 'Navigate to agent creation page',
          confidence: 0.9,
        });
        
        if (analysis.entities.length > 0) {
          const agentName = analysis.entities.find(e => e.type === 'object')?.value;
          if (agentName) {
            actions.push({
              type: 'prefill_form',
              payload: { field: 'name', value: agentName },
              description: `Set agent name to "${agentName}"`,
              confidence: 0.8,
            });
          }
        }
        break;

      case 'modify_config':
        const property = analysis.entities.find(e => e.type === 'property')?.value;
        const value = analysis.entities.find(e => e.type === 'value')?.value;
        
        if (property && value) {
          actions.push({
            type: 'update_config',
            payload: { [property]: value },
            description: `Update ${property} to ${value}`,
            confidence: 0.85,
          });
        }
        break;

      case 'get_help':
        const topic = analysis.entities.find(e => e.type === 'object')?.value;
        actions.push({
          type: 'show_help',
          payload: { topic: topic || 'general' },
          description: `Show help for ${topic || 'general topics'}`,
          confidence: 0.9,
        });
        break;

      case 'navigate':
        const target = analysis.entities.find(e => e.type === 'object')?.value;
        if (target) {
          const path = this.getNavigationPath(target);
          if (path) {
            actions.push({
              type: 'navigate',
              payload: { path },
              description: `Navigate to ${target}`,
              confidence: 0.8,
            });
          }
        }
        break;
    }

    return actions;
  }

  /**
   * Initialize pattern matching rules
   */
  private initializePatterns(): void {
    // Intent patterns
    this.intentPatterns.set('create_agent', [
      /create.*agent/i,
      /build.*agent/i,
      /new.*agent/i,
      /make.*agent/i,
    ]);

    this.intentPatterns.set('create_tool', [
      /create.*tool/i,
      /build.*tool/i,
      /new.*tool/i,
      /make.*tool/i,
    ]);

    this.intentPatterns.set('modify_config', [
      /change.*setting/i,
      /update.*config/i,
      /modify.*parameter/i,
      /set.*to/i,
    ]);

    this.intentPatterns.set('get_help', [
      /help.*with/i,
      /how.*to/i,
      /what.*is/i,
      /explain/i,
    ]);

    this.intentPatterns.set('navigate', [
      /go.*to/i,
      /show.*me/i,
      /open/i,
      /navigate.*to/i,
    ]);

    // Entity patterns
    this.entityPatterns.set('action', /\b(create|build|make|update|change|set|delete|remove|show|open|navigate)\b/gi);
    this.entityPatterns.set('object', /\b(agent|tool|workflow|template|dashboard|settings|config)\b/gi);
    this.entityPatterns.set('property', /\b(name|description|model|temperature|timeout|retries)\b/gi);
    this.entityPatterns.set('value', /"([^"]+)"|'([^']+)'|\b(\d+(?:\.\d+)?)\b/gi);

    // Action keywords
    this.actionKeywords.set('create', ['create', 'build', 'make', 'new', 'add']);
    this.actionKeywords.set('modify', ['change', 'update', 'edit', 'modify', 'set']);
    this.actionKeywords.set('delete', ['delete', 'remove', 'clear']);
    this.actionKeywords.set('navigate', ['go', 'show', 'open', 'navigate']);
    this.actionKeywords.set('help', ['help', 'explain', 'how', 'what']);
  }

  /**
   * AI-powered analysis using backend service
   */
  private async getAIAnalysis(
    input: string,
    context: NLPContext
  ): Promise<IntentAnalysis | null> {
    try {
      const response = await apiClient.post('/api/v1/ai/analyze-intent', {
        input,
        context,
      });
      return response.data;
    } catch (error) {
      return null;
    }
  }

  /**
   * Rule-based analysis fallback
   */
  private getRuleBasedAnalysis(
    input: string,
    context: NLPContext
  ): IntentAnalysis {
    const normalizedInput = input.toLowerCase();
    let bestIntent = 'unknown';
    let bestConfidence = 0;

    // Find best matching intent
    this.intentPatterns.forEach((patterns, intent) => {
      for (const pattern of patterns) {
        if (pattern.test(normalizedInput)) {
          const confidence = this.calculatePatternConfidence(normalizedInput, pattern);
          if (confidence > bestConfidence) {
            bestIntent = intent;
            bestConfidence = confidence;
          }
        }
      }
    });

    const entities = this.extractEntities(input);
    const actionEntity = entities.find(e => e.type === 'action');
    const objectEntity = entities.find(e => e.type === 'object');

    return {
      intent: bestIntent,
      confidence: bestConfidence,
      entities,
      context: {
        domain: context.currentPage || 'general',
        action: actionEntity?.value || 'unknown',
        target: objectEntity?.value,
        parameters: this.extractParameters(entities),
      },
      suggestedActions: this.generateSuggestedActions(bestIntent, entities),
      alternatives: this.generateAlternatives(normalizedInput),
    };
  }

  /**
   * Generate rule-based suggestions
   */
  private getRuleBasedSuggestions(
    partialInput: string,
    context: NLPContext
  ): Array<{
    completion: string;
    intent: string;
    confidence: number;
    description: string;
  }> {
    const suggestions = [];
    const normalizedInput = partialInput.toLowerCase();

    // Common completions based on partial input
    if (normalizedInput.includes('create')) {
      suggestions.push({
        completion: 'create a new agent',
        intent: 'create_agent',
        confidence: 0.8,
        description: 'Create a new AI agent',
      });
    }

    if (normalizedInput.includes('help')) {
      suggestions.push({
        completion: 'help with agents',
        intent: 'get_help',
        confidence: 0.9,
        description: 'Get help with AI agents',
      });
    }

    return suggestions;
  }

  /**
   * Calculate confidence for pattern matches
   */
  private calculatePatternConfidence(text: string, pattern: RegExp): number {
    const matches = text.match(pattern);
    if (!matches) return 0;
    
    // Base confidence on match length relative to input length
    const matchLength = matches[0].length;
    const textLength = text.length;
    return Math.min(0.9, (matchLength / textLength) * 2);
  }

  /**
   * Remove overlapping entities
   */
  private deduplicateEntities(entities: IntentEntity[]): IntentEntity[] {
    const sorted = entities.sort((a, b) => a.startIndex - b.startIndex);
    const deduplicated: IntentEntity[] = [];

    for (const entity of sorted) {
      const hasOverlap = deduplicated.some(
        existing => 
          (entity.startIndex >= existing.startIndex && entity.startIndex < existing.endIndex) ||
          (entity.endIndex > existing.startIndex && entity.endIndex <= existing.endIndex)
      );

      if (!hasOverlap) {
        deduplicated.push(entity);
      }
    }

    return deduplicated;
  }

  /**
   * Extract parameters from entities
   */
  private extractParameters(entities: IntentEntity[]): Record<string, any> {
    const parameters: Record<string, any> = {};
    
    entities.forEach(entity => {
      if (entity.type === 'property' || entity.type === 'value') {
        parameters[entity.type] = entity.value;
      }
    });

    return parameters;
  }

  /**
   * Generate suggested actions based on intent
   */
  private generateSuggestedActions(
    intent: string,
    entities: IntentEntity[]
  ): IntentAnalysis['suggestedActions'] {
    const actions: IntentAnalysis['suggestedActions'] = [];

    switch (intent) {
      case 'create_agent':
        actions.push({
          type: 'create',
          description: 'Create a new AI agent',
          confidence: 0.9,
          payload: { type: 'agent' },
        });
        break;
      case 'get_help':
        actions.push({
          type: 'help',
          description: 'Show help documentation',
          confidence: 0.9,
        });
        break;
    }

    return actions;
  }

  /**
   * Generate alternative interpretations
   */
  private generateAlternatives(input: string): IntentAnalysis['alternatives'] {
    // Simple alternative generation based on common patterns
    return [
      {
        intent: 'get_help',
        confidence: 0.3,
        reasoning: 'Could be asking for help',
      },
    ];
  }

  /**
   * Get required entities for an intent
   */
  private getRequiredEntities(intent: string): string[] {
    const requirements: Record<string, string[]> = {
      create_agent: ['object'],
      modify_config: ['property', 'value'],
      navigate: ['object'],
    };

    return requirements[intent] || [];
  }

  /**
   * Get navigation path for target
   */
  private getNavigationPath(target: string): string | null {
    const paths: Record<string, string> = {
      agents: '/agents',
      tools: '/tools',
      workflows: '/workflows',
      dashboard: '/dashboard',
      settings: '/settings',
    };

    return paths[target.toLowerCase()] || null;
  }
}

export const nlpProcessor = new NLPProcessor();
