import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TerminusModule } from '@nestjs/terminus';
import { BullModule } from '@nestjs/bull';
import { PrismaModule } from '@modules/prisma/prisma.module';
import { RedisModule } from '@modules/redis/redis.module';
import { LoggerModule } from '@services/logger/logger.module';
import { MonitoringModule } from '@services/monitoring/monitoring.module';
import { HealthModule } from '@modules/health/health.module';
import { configValidationSchema } from '@utils/config/config.validation';

@Module({
  imports: [
    // Global configuration with validation
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
      validationSchema: configValidationSchema,
      validationOptions: {
        allowUnknown: true,
        abortEarly: true,
      },
    }),

    // Health check module
    TerminusModule,
    HealthModule,

    // Core infrastructure modules
    LoggerModule,
    MonitoringModule,
    PrismaModule,
    RedisModule,

    // Message queue configuration
    BullModule.forRootAsync({
      useFactory: () => ({
        redis: {
          host: process.env.REDIS_HOST || 'localhost',
          port: parseInt(process.env.REDIS_PORT || '6379'),
          password: process.env.REDIS_PASSWORD,
          maxRetriesPerRequest: 3,
          retryDelayOnFailover: 100,
          enableReadyCheck: true,
          lazyConnect: true,
        },
        defaultJobOptions: {
          removeOnComplete: 100,
          removeOnFail: 50,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
      }),
    }),

    // Feature modules will be added in subsequent tasks
    // AuthModule,
    // AgentModule,
    // ToolModule,
    // WorkflowModule,
    // etc.
  ],
  controllers: [],
  providers: [],
})
export class AppModule {
  constructor() {
    console.log('SynapseAI AppModule initialized with microservices architecture');
  }
}
