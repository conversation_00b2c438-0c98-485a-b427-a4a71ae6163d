import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TerminusModule } from '@nestjs/terminus';
import { BullModule } from '@nestjs/bull';
import { JwtModule } from '@nestjs/jwt';
import { PrismaModule } from '@modules/prisma/prisma.module';
import { RedisModule } from '@modules/redis/redis.module';
import { LoggerModule } from '@services/logger/logger.module';
import { MonitoringModule } from '@services/monitoring/monitoring.module';
import { HealthModule } from '@modules/health/health.module';
import { TenantMiddleware } from '@middleware/tenant.middleware';
import { TenantService } from '@services/tenant/tenant.service';
import { configValidationSchema } from '@utils/config/config.validation';

@Module({
  imports: [
    // Global configuration with validation
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
      validationSchema: configValidationSchema,
      validationOptions: {
        allowUnknown: true,
        abortEarly: true,
      },
    }),

    // Health check module
    TerminusModule,
    HealthModule,

    // Authentication module
    JwtModule.registerAsync({
      useFactory: () => ({
        secret: process.env.JWT_SECRET,
        signOptions: {
          expiresIn: process.env.JWT_EXPIRES_IN || '1h',
        },
      }),
    }),

    // Core infrastructure modules
    LoggerModule,
    MonitoringModule,
    PrismaModule,
    RedisModule,

    // Message queue configuration
    BullModule.forRootAsync({
      useFactory: () => ({
        redis: {
          host: process.env.REDIS_HOST || 'localhost',
          port: parseInt(process.env.REDIS_PORT || '6379'),
          password: process.env.REDIS_PASSWORD,
          maxRetriesPerRequest: 3,
          retryDelayOnFailover: 100,
          enableReadyCheck: true,
          lazyConnect: true,
        },
        defaultJobOptions: {
          removeOnComplete: 100,
          removeOnFail: 50,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
      }),
    }),

    // Feature modules will be added in subsequent tasks
    // AuthModule,
    // AgentModule,
    // ToolModule,
    // WorkflowModule,
    // etc.
  ],
  controllers: [],
  providers: [TenantService],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(TenantMiddleware)
      .exclude(
        '/health',
        '/health/ready',
        '/health/live',
        '/api/v1/auth/login',
        '/api/v1/auth/register',
        '/api/v1/auth/refresh',
        '/api/docs',
        '/metrics',
      )
      .forRoutes('*');
  }

  constructor() {
    console.log('SynapseAI AppModule initialized with multi-tenant microservices architecture');
  }
}
