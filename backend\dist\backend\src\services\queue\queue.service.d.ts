import { OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Queue, Job, JobOptions } from 'bull';
import { RedisService } from '@modules/redis/redis.service';
export interface QueueJobData {
    type: string;
    payload: any;
    organizationId: string;
    userId?: string;
    priority?: number;
    metadata?: Record<string, any>;
}
export interface QueueJobResult {
    success: boolean;
    result?: any;
    error?: string;
    duration: number;
    timestamp: string;
}
export declare class QueueService implements OnModuleInit, OnModuleDestroy {
    private configService;
    private redisService;
    private agentQueue;
    private toolQueue;
    private workflowQueue;
    private notificationQueue;
    private analyticsQueue;
    private billingQueue;
    private readonly logger;
    constructor(configService: ConfigService, redisService: RedisService, agentQueue: Queue, toolQueue: Queue, workflowQueue: Queue, notificationQueue: Queue, analyticsQueue: Queue, billingQueue: Queue);
    onModuleInit(): Promise<void>;
    onModuleDestroy(): Promise<void>;
    addAgentJob(jobData: QueueJobData, options?: JobOptions): Promise<Job<QueueJobData>>;
    addToolJob(jobData: QueueJobData, options?: JobOptions): Promise<Job<QueueJobData>>;
    addWorkflowJob(jobData: QueueJobData, options?: JobOptions): Promise<Job<QueueJobData>>;
    addNotificationJob(jobData: QueueJobData, options?: JobOptions): Promise<Job<QueueJobData>>;
    addAnalyticsJob(jobData: QueueJobData, options?: JobOptions): Promise<Job<QueueJobData>>;
    addBillingJob(jobData: QueueJobData, options?: JobOptions): Promise<Job<QueueJobData>>;
    getQueueStats(): Promise<{
        name: string;
        waiting: number;
        active: number;
        completed: number;
        failed: number;
        delayed: number;
    }[]>;
    pauseQueue(queueName: string): Promise<void>;
    resumeQueue(queueName: string): Promise<void>;
    cleanQueue(queueName: string, grace?: number): Promise<void>;
    private getQueueByName;
    private setupQueueProcessors;
    private setupQueueEvents;
}
