export interface CanvasNode {
  id: string;
  type: 'agent' | 'tool' | 'condition' | 'trigger' | 'action' | 'output';
  position: { x: number; y: number };
  size: { width: number; height: number };
  data: Record<string, any>;
  inputs: Array<{
    id: string;
    name: string;
    type: string;
    required: boolean;
    connected?: boolean;
  }>;
  outputs: Array<{
    id: string;
    name: string;
    type: string;
    connected?: boolean;
  }>;
  style?: {
    backgroundColor?: string;
    borderColor?: string;
    textColor?: string;
    borderRadius?: number;
  };
  metadata?: {
    label?: string;
    description?: string;
    icon?: string;
    category?: string;
    version?: string;
  };
}

export interface CanvasConnection {
  id: string;
  sourceNodeId: string;
  sourceOutputId: string;
  targetNodeId: string;
  targetInputId: string;
  style?: {
    color?: string;
    width?: number;
    dashArray?: string;
  };
  metadata?: {
    label?: string;
    dataType?: string;
  };
}

export interface CanvasState {
  nodes: CanvasNode[];
  connections: CanvasConnection[];
  selectedNodes: string[];
  selectedConnections: string[];
  viewport: {
    x: number;
    y: number;
    zoom: number;
  };
  grid: {
    enabled: boolean;
    size: number;
    snapToGrid: boolean;
  };
  settings: {
    showMinimap: boolean;
    showGrid: boolean;
    autoLayout: boolean;
    theme: 'light' | 'dark';
  };
}

export interface DragState {
  isDragging: boolean;
  dragType: 'node' | 'connection' | 'selection' | 'viewport';
  startPosition: { x: number; y: number };
  currentPosition: { x: number; y: number };
  draggedNodes: string[];
  tempConnection?: {
  sourceNodeId: string;
  sourceOutputId: string;
  targetPosition: { x: number; y: number; };
} | undefined;
}

export interface CanvasEvents {
  onNodeSelect: (nodeIds: string[]) => void;
  onNodeDeselect: () => void;
  onNodeMove: (nodeId: string, position: { x: number; y: number }) => void;
  onNodeDelete: (nodeIds: string[]) => void;
  onNodeAdd: (node: Omit<CanvasNode, 'id'>) => void;
  onConnectionCreate: (connection: Omit<CanvasConnection, 'id'>) => void;
  onConnectionDelete: (connectionIds: string[]) => void;
  onCanvasClick: (position: { x: number; y: number }) => void;
  onViewportChange: (viewport: CanvasState['viewport']) => void;
}

class CanvasEngine {
  private state: CanvasState;
  private dragState: DragState;
  private events: Partial<CanvasEvents>;
  private canvas: HTMLCanvasElement | null = null;
  private ctx: CanvasRenderingContext2D | null = null;

  constructor(initialState?: Partial<CanvasState>) {
    this.state = {
      nodes: [],
      connections: [],
      selectedNodes: [],
      selectedConnections: [],
      viewport: { x: 0, y: 0, zoom: 1 },
      grid: { enabled: true, size: 20, snapToGrid: true },
      settings: { showMinimap: true, showGrid: true, autoLayout: false, theme: 'light' },
      ...initialState,
    };

    this.dragState = {
      isDragging: false,
      dragType: 'node',
      startPosition: { x: 0, y: 0 },
      currentPosition: { x: 0, y: 0 },
      draggedNodes: [],
    };

    this.events = {};
  }

  /**
   * Initialize canvas with DOM element
   */
  initialize(canvas: HTMLCanvasElement, events: Partial<CanvasEvents>): void {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d');
    this.events = events;

    this.setupEventListeners();
    this.render();
  }

  /**
   * Add a node to the canvas
   */
  addNode(node: Omit<CanvasNode, 'id'>): string {
    const id = this.generateId();
    const newNode: CanvasNode = {
      ...node,
      id,
    };

    // Snap to grid if enabled
    if (this.state.grid.snapToGrid) {
      newNode.position = this.snapToGrid(newNode.position);
    }

    this.state.nodes.push(newNode);
    this.events.onNodeAdd?.(node);
    this.render();
    
    return id;
  }

  /**
   * Remove nodes from canvas
   */
  removeNodes(nodeIds: string[]): void {
    // Remove connections first
    const connectionsToRemove = this.state.connections.filter(
      conn => nodeIds.includes(conn.sourceNodeId) || nodeIds.includes(conn.targetNodeId)
    );
    this.removeConnections(connectionsToRemove.map(c => c.id));

    // Remove nodes
    this.state.nodes = this.state.nodes.filter(node => !nodeIds.includes(node.id));
    this.state.selectedNodes = this.state.selectedNodes.filter(id => !nodeIds.includes(id));
    
    this.events.onNodeDelete?.(nodeIds);
    this.render();
  }

  /**
   * Create connection between nodes
   */
  createConnection(connection: Omit<CanvasConnection, 'id'>): string {
    // Validate connection
    if (!this.isValidConnection(connection)) {
      throw new Error('Invalid connection');
    }

    const id = this.generateId();
    const newConnection: CanvasConnection = {
      ...connection,
      id,
    };

    this.state.connections.push(newConnection);
    this.updateNodeConnectionStatus();
    this.events.onConnectionCreate?.(connection);
    this.render();
    
    return id;
  }

  /**
   * Remove connections from canvas
   */
  removeConnections(connectionIds: string[]): void {
    this.state.connections = this.state.connections.filter(
      conn => !connectionIds.includes(conn.id)
    );
    this.state.selectedConnections = this.state.selectedConnections.filter(
      id => !connectionIds.includes(id)
    );
    
    this.updateNodeConnectionStatus();
    this.events.onConnectionDelete?.(connectionIds);
    this.render();
  }

  /**
   * Select nodes
   */
  selectNodes(nodeIds: string[], addToSelection = false): void {
    if (!addToSelection) {
      this.state.selectedNodes = [];
      this.state.selectedConnections = [];
    }

    nodeIds.forEach(id => {
      if (!this.state.selectedNodes.includes(id)) {
        this.state.selectedNodes.push(id);
      }
    });

    this.events.onNodeSelect?.(this.state.selectedNodes);
    this.render();
  }

  /**
   * Move selected nodes
   */
  moveNodes(nodeIds: string[], delta: { x: number; y: number }): void {
    nodeIds.forEach(nodeId => {
      const node = this.state.nodes.find(n => n.id === nodeId);
      if (node) {
        const newPosition = {
          x: node.position.x + delta.x,
          y: node.position.y + delta.y,
        };

        if (this.state.grid.snapToGrid) {
          node.position = this.snapToGrid(newPosition);
        } else {
          node.position = newPosition;
        }

        this.events.onNodeMove?.(nodeId, node.position);
      }
    });

    this.render();
  }

  /**
   * Update viewport (pan and zoom)
   */
  updateViewport(viewport: Partial<CanvasState['viewport']>): void {
    this.state.viewport = { ...this.state.viewport, ...viewport };
    this.events.onViewportChange?.(this.state.viewport);
    this.render();
  }

  /**
   * Auto-layout nodes
   */
  autoLayout(): void {
    if (this.state.nodes.length === 0) return;

    // Simple hierarchical layout
    const levels = this.calculateNodeLevels();
    const levelWidth = 200;
    const levelHeight = 150;

    levels.forEach((nodeIds, level) => {
      nodeIds.forEach((nodeId, index) => {
        const node = this.state.nodes.find(n => n.id === nodeId);
        if (node) {
          node.position = {
            x: level * levelWidth,
            y: index * levelHeight,
          };
        }
      });
    });

    this.render();
  }

  /**
   * Export canvas state
   */
  export(): CanvasState {
    return JSON.parse(JSON.stringify(this.state));
  }

  /**
   * Import canvas state
   */
  import(state: CanvasState): void {
    this.state = state;
    this.render();
  }

  /**
   * Get canvas state
   */
  getState(): CanvasState {
    return this.state;
  }

  /**
   * Render the canvas
   */
  private render(): void {
    if (!this.canvas || !this.ctx) return;

    const { width, height } = this.canvas;
    this.ctx.clearRect(0, 0, width, height);

    // Apply viewport transformation
    this.ctx.save();
    this.ctx.translate(this.state.viewport.x, this.state.viewport.y);
    this.ctx.scale(this.state.viewport.zoom, this.state.viewport.zoom);

    // Draw grid
    if (this.state.settings.showGrid) {
      this.drawGrid();
    }

    // Draw connections
    this.state.connections.forEach(connection => {
      this.drawConnection(connection);
    });

    // Draw nodes
    this.state.nodes.forEach(node => {
      this.drawNode(node);
    });

    // Draw temporary connection
    if (this.dragState.tempConnection) {
      this.drawTempConnection();
    }

    this.ctx.restore();
  }

  /**
   * Draw grid
   */
  private drawGrid(): void {
    if (!this.ctx || !this.canvas) return;

    const { size } = this.state.grid;
    const { width, height } = this.canvas;
    const { x, y, zoom } = this.state.viewport;

    this.ctx.strokeStyle = this.state.settings.theme === 'dark' ? '#374151' : '#e5e7eb';
    this.ctx.lineWidth = 1 / zoom;

    // Calculate grid bounds
    const startX = Math.floor(-x / (size * zoom)) * size;
    const startY = Math.floor(-y / (size * zoom)) * size;
    const endX = startX + Math.ceil(width / (size * zoom)) * size;
    const endY = startY + Math.ceil(height / (size * zoom)) * size;

    // Draw vertical lines
    for (let x = startX; x <= endX; x += size) {
      this.ctx.beginPath();
      this.ctx.moveTo(x, startY);
      this.ctx.lineTo(x, endY);
      this.ctx.stroke();
    }

    // Draw horizontal lines
    for (let y = startY; y <= endY; y += size) {
      this.ctx.beginPath();
      this.ctx.moveTo(startX, y);
      this.ctx.lineTo(endX, y);
      this.ctx.stroke();
    }
  }

  /**
   * Draw a node
   */
  private drawNode(node: CanvasNode): void {
    if (!this.ctx) return;

    const isSelected = this.state.selectedNodes.includes(node.id);
    const { x, y } = node.position;
    const { width, height } = node.size;

    // Node background
    this.ctx.fillStyle = node.style?.backgroundColor || '#ffffff';
    this.ctx.strokeStyle = isSelected 
      ? '#3b82f6' 
      : node.style?.borderColor || '#d1d5db';
    this.ctx.lineWidth = isSelected ? 3 : 1;

    const radius = node.style?.borderRadius || 8;
    this.drawRoundedRect(x, y, width, height, radius);
    this.ctx.fill();
    this.ctx.stroke();

    // Node label
    if (node.metadata?.label) {
      this.ctx.fillStyle = node.style?.textColor || '#374151';
      this.ctx.font = '14px Inter, sans-serif';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';
      this.ctx.fillText(
        node.metadata.label,
        x + width / 2,
        y + height / 2
      );
    }

    // Input/output ports
    this.drawPorts(node);
  }

  /**
   * Draw connection ports
   */
  private drawPorts(node: CanvasNode): void {
    if (!this.ctx) return;

    const portRadius = 6;
    const { x, y } = node.position;
    const { width, height } = node.size;

    // Input ports (left side)
    node.inputs.forEach((input, index) => {
      const portY = y + (height / (node.inputs.length + 1)) * (index + 1);

      this.ctx!.fillStyle = input.connected ? '#10b981' : '#6b7280';
      this.ctx!.beginPath();
      this.ctx!.arc(x, portY, portRadius, 0, 2 * Math.PI);
      this.ctx!.fill();
    });

    // Output ports (right side)
    node.outputs.forEach((output, index) => {
      const portY = y + (height / (node.outputs.length + 1)) * (index + 1);

      this.ctx!.fillStyle = output.connected ? '#10b981' : '#6b7280';
      this.ctx!.beginPath();
      this.ctx!.arc(x + width, portY, portRadius, 0, 2 * Math.PI);
      this.ctx!.fill();
    });
  }

  /**
   * Draw a connection
   */
  private drawConnection(connection: CanvasConnection): void {
    if (!this.ctx) return;

    const sourceNode = this.state.nodes.find(n => n.id === connection.sourceNodeId);
    const targetNode = this.state.nodes.find(n => n.id === connection.targetNodeId);
    
    if (!sourceNode || !targetNode) return;

    const sourceOutput = sourceNode.outputs.find(o => o.id === connection.sourceOutputId);
    const targetInput = targetNode.inputs.find(i => i.id === connection.targetInputId);
    
    if (!sourceOutput || !targetInput) return;

    // Calculate port positions
    const sourceIndex = sourceNode.outputs.indexOf(sourceOutput);
    const targetIndex = targetNode.inputs.indexOf(targetInput);
    
    const sourceY = sourceNode.position.y + 
      (sourceNode.size.height / (sourceNode.outputs.length + 1)) * (sourceIndex + 1);
    const targetY = targetNode.position.y + 
      (targetNode.size.height / (targetNode.inputs.length + 1)) * (targetIndex + 1);

    const startX = sourceNode.position.x + sourceNode.size.width;
    const startY = sourceY;
    const endX = targetNode.position.x;
    const endY = targetY;

    // Draw bezier curve
    this.ctx!.strokeStyle = connection.style?.color || '#6b7280';
    this.ctx!.lineWidth = connection.style?.width || 2;

    if (connection.style?.dashArray) {
      this.ctx!.setLineDash(connection.style.dashArray.split(',').map(Number));
    }

    this.ctx!.beginPath();
    this.ctx!.moveTo(startX, startY);

    const controlPointOffset = Math.abs(endX - startX) * 0.5;
    this.ctx!.bezierCurveTo(
      startX + controlPointOffset, startY,
      endX - controlPointOffset, endY,
      endX, endY
    );

    this.ctx!.stroke();
    this.ctx!.setLineDash([]);
  }

  /**
   * Draw temporary connection during drag
   */
  private drawTempConnection(): void {
    if (!this.ctx || !this.dragState.tempConnection) return;

    const sourceNode = this.state.nodes.find(n => n.id === this.dragState.tempConnection!.sourceNodeId);
    if (!sourceNode) return;

    const sourceOutput = sourceNode.outputs.find(o => o.id === this.dragState.tempConnection!.sourceOutputId);
    if (!sourceOutput) return;

    const sourceIndex = sourceNode.outputs.indexOf(sourceOutput);
    const sourceY = sourceNode.position.y + 
      (sourceNode.size.height / (sourceNode.outputs.length + 1)) * (sourceIndex + 1);

    const startX = sourceNode.position.x + sourceNode.size.width;
    const startY = sourceY;
    const endX = this.dragState.tempConnection.targetPosition.x;
    const endY = this.dragState.tempConnection.targetPosition.y;

    this.ctx!.strokeStyle = '#3b82f6';
    this.ctx!.lineWidth = 2;
    this.ctx!.setLineDash([5, 5]);

    this.ctx!.beginPath();
    this.ctx!.moveTo(startX, startY);
    this.ctx!.lineTo(endX, endY);
    this.ctx!.stroke();
    this.ctx!.setLineDash([]);
  }

  /**
   * Setup event listeners
   */
  private setupEventListeners(): void {
    if (!this.canvas) return;

    this.canvas.addEventListener('mousedown', this.handleMouseDown.bind(this));
    this.canvas.addEventListener('mousemove', this.handleMouseMove.bind(this));
    this.canvas.addEventListener('mouseup', this.handleMouseUp.bind(this));
    this.canvas.addEventListener('wheel', this.handleWheel.bind(this));
    this.canvas.addEventListener('click', this.handleClick.bind(this));
  }

  /**
   * Handle mouse events
   */
  private handleMouseDown(event: MouseEvent): void {
    if (!this.canvas) return;

    const rect = this.canvas.getBoundingClientRect();
    const x = (event.clientX - rect.left - this.state.viewport.x) / this.state.viewport.zoom;
    const y = (event.clientY - rect.top - this.state.viewport.y) / this.state.viewport.zoom;

    this.dragState.startPosition = { x, y };
    this.dragState.currentPosition = { x, y };

    // Check if clicking on a node
    const clickedNode = this.getNodeAtPosition({ x, y });
    if (clickedNode) {
      this.dragState.isDragging = true;
      this.dragState.dragType = 'node';
      this.dragState.draggedNodes = this.state.selectedNodes.includes(clickedNode.id)
        ? this.state.selectedNodes
        : [clickedNode.id];

      if (!this.state.selectedNodes.includes(clickedNode.id)) {
        this.selectNodes([clickedNode.id], event.ctrlKey || event.metaKey);
      }
    } else {
      // Start viewport drag
      this.dragState.isDragging = true;
      this.dragState.dragType = 'viewport';
    }
  }

  private handleMouseMove(event: MouseEvent): void {
    if (!this.dragState.isDragging || !this.canvas) return;

    const rect = this.canvas.getBoundingClientRect();
    const x = (event.clientX - rect.left - this.state.viewport.x) / this.state.viewport.zoom;
    const y = (event.clientY - rect.top - this.state.viewport.y) / this.state.viewport.zoom;

    const delta = {
      x: x - this.dragState.currentPosition.x,
      y: y - this.dragState.currentPosition.y,
    };

    if (this.dragState.dragType === 'node') {
      this.moveNodes(this.dragState.draggedNodes, delta);
    } else if (this.dragState.dragType === 'viewport') {
      this.updateViewport({
        x: this.state.viewport.x + delta.x * this.state.viewport.zoom,
        y: this.state.viewport.y + delta.y * this.state.viewport.zoom,
      });
    }

    this.dragState.currentPosition = { x, y };
  }

  private handleMouseUp(): void {
    this.dragState.isDragging = false;
    this.dragState.draggedNodes = [];
    this.dragState.tempConnection = undefined;
  }

  private handleWheel(event: WheelEvent): void {
    event.preventDefault();
    
    const zoomFactor = event.deltaY > 0 ? 0.9 : 1.1;
    const newZoom = Math.max(0.1, Math.min(3, this.state.viewport.zoom * zoomFactor));
    
    this.updateViewport({ zoom: newZoom });
  }

  private handleClick(event: MouseEvent): void {
    if (!this.canvas) return;

    const rect = this.canvas.getBoundingClientRect();
    const x = (event.clientX - rect.left - this.state.viewport.x) / this.state.viewport.zoom;
    const y = (event.clientY - rect.top - this.state.viewport.y) / this.state.viewport.zoom;

    this.events.onCanvasClick?.({ x, y });
  }

  /**
   * Utility methods
   */
  private generateId(): string {
    return `node_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private snapToGrid(position: { x: number; y: number }): { x: number; y: number } {
    const { size } = this.state.grid;
    return {
      x: Math.round(position.x / size) * size,
      y: Math.round(position.y / size) * size,
    };
  }

  private getNodeAtPosition(position: { x: number; y: number }): CanvasNode | null {
    return this.state.nodes.find(node => 
      position.x >= node.position.x &&
      position.x <= node.position.x + node.size.width &&
      position.y >= node.position.y &&
      position.y <= node.position.y + node.size.height
    ) || null;
  }

  private isValidConnection(connection: Omit<CanvasConnection, 'id'>): boolean {
    // Check if nodes exist
    const sourceNode = this.state.nodes.find(n => n.id === connection.sourceNodeId);
    const targetNode = this.state.nodes.find(n => n.id === connection.targetNodeId);
    
    if (!sourceNode || !targetNode) return false;

    // Check if ports exist
    const sourceOutput = sourceNode.outputs.find(o => o.id === connection.sourceOutputId);
    const targetInput = targetNode.inputs.find(i => i.id === connection.targetInputId);
    
    if (!sourceOutput || !targetInput) return false;

    // Check for existing connection
    const existingConnection = this.state.connections.find(conn =>
      conn.targetNodeId === connection.targetNodeId &&
      conn.targetInputId === connection.targetInputId
    );
    
    return !existingConnection;
  }

  private updateNodeConnectionStatus(): void {
    // Reset all connection statuses
    this.state.nodes.forEach(node => {
      node.inputs.forEach(input => input.connected = false);
      node.outputs.forEach(output => output.connected = false);
    });

    // Update based on existing connections
    this.state.connections.forEach(connection => {
      const sourceNode = this.state.nodes.find(n => n.id === connection.sourceNodeId);
      const targetNode = this.state.nodes.find(n => n.id === connection.targetNodeId);
      
      if (sourceNode && targetNode) {
        const sourceOutput = sourceNode.outputs.find(o => o.id === connection.sourceOutputId);
        const targetInput = targetNode.inputs.find(i => i.id === connection.targetInputId);
        
        if (sourceOutput) sourceOutput.connected = true;
        if (targetInput) targetInput.connected = true;
      }
    });
  }

  private calculateNodeLevels(): Map<number, string[]> {
    const levels = new Map<number, string[]>();
    const visited = new Set<string>();
    
    // Find root nodes (no incoming connections)
    const rootNodes = this.state.nodes.filter(node =>
      !this.state.connections.some(conn => conn.targetNodeId === node.id)
    );

    // BFS to assign levels
    const queue: Array<{ nodeId: string; level: number }> = 
      rootNodes.map(node => ({ nodeId: node.id, level: 0 }));

    while (queue.length > 0) {
      const { nodeId, level } = queue.shift()!;
      
      if (visited.has(nodeId)) continue;
      visited.add(nodeId);

      if (!levels.has(level)) {
        levels.set(level, []);
      }
      levels.get(level)!.push(nodeId);

      // Add connected nodes to next level
      const outgoingConnections = this.state.connections.filter(
        conn => conn.sourceNodeId === nodeId
      );
      
      outgoingConnections.forEach(conn => {
        if (!visited.has(conn.targetNodeId)) {
          queue.push({ nodeId: conn.targetNodeId, level: level + 1 });
        }
      });
    }

    return levels;
  }

  private drawRoundedRect(x: number, y: number, width: number, height: number, radius: number): void {
    if (!this.ctx) return;

    this.ctx.beginPath();
    this.ctx.moveTo(x + radius, y);
    this.ctx.lineTo(x + width - radius, y);
    this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    this.ctx.lineTo(x + width, y + height - radius);
    this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    this.ctx.lineTo(x + radius, y + height);
    this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    this.ctx.lineTo(x, y + radius);
    this.ctx.quadraticCurveTo(x, y, x + radius, y);
    this.ctx.closePath();
  }
}

export { CanvasEngine };
