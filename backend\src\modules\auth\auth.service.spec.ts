import { Test, TestingModule } from '@nestjs/testing';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { UnauthorizedException, ConflictException, BadRequestException } from '@nestjs/common';
import { AuthService } from './auth.service';
import { PrismaService } from '@modules/prisma/prisma.service';
import { RedisService } from '@modules/redis/redis.service';
import { LoggerService } from '@services/logger/logger.service';
import { UserRole } from '@prisma/client';
import * as bcrypt from 'bcryptjs';

describe('AuthService', () => {
  let service: AuthService;
  let prismaService: PrismaService;
  let redisService: RedisService;
  let jwtService: JwtService;
  let loggerService: LoggerService;

  const mockUser = {
    id: 'user-id-123',
    email: '<EMAIL>',
    passwordHash: '$2a$12$hashedpassword',
    firstName: 'John',
    lastName: 'Doe',
    role: UserRole.DEVELOPER,
    organizationId: 'org-id-123',
    isActive: true,
    organization: {
      id: 'org-id-123',
      name: 'Test Organization',
      isActive: true,
    },
  };

  const mockPrismaService = {
    user: {
      findFirst: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
    organization: {
      findFirst: jest.fn(),
    },
  };

  const mockRedisService = {
    setex: jest.fn(),
    get: jest.fn(),
    del: jest.fn(),
    keys: jest.fn(),
  };

  const mockJwtService = {
    sign: jest.fn(),
    verify: jest.fn(),
  };

  const mockLoggerService = {
    audit: jest.fn(),
    error: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        { provide: PrismaService, useValue: mockPrismaService },
        { provide: RedisService, useValue: mockRedisService },
        { provide: JwtService, useValue: mockJwtService },
        { provide: LoggerService, useValue: mockLoggerService },
        { provide: ConfigService, useValue: mockConfigService },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    prismaService = module.get<PrismaService>(PrismaService);
    redisService = module.get<RedisService>(RedisService);
    jwtService = module.get<JwtService>(JwtService);
    loggerService = module.get<LoggerService>(LoggerService);

    // Reset mocks
    jest.clearAllMocks();
    mockConfigService.get.mockReturnValue('3600');
  });

  describe('validateUser', () => {
    it('should validate user with correct credentials', async () => {
      mockPrismaService.user.findFirst.mockResolvedValue(mockUser);
      jest.spyOn(bcrypt, 'compare').mockResolvedValue(true as never);

      const result = await service.validateUser('<EMAIL>', 'password', 'org-id-123');

      expect(result).toBeDefined();
      expect(result.email).toBe('<EMAIL>');
      expect(result.passwordHash).toBeUndefined();
      expect(mockPrismaService.user.update).toHaveBeenCalledWith({
        where: { id: 'user-id-123' },
        data: { lastLoginAt: expect.any(Date) },
      });
    });

    it('should throw UnauthorizedException for invalid credentials', async () => {
      mockPrismaService.user.findFirst.mockResolvedValue(null);

      await expect(
        service.validateUser('<EMAIL>', 'wrongpassword', 'org-id-123')
      ).rejects.toThrow(UnauthorizedException);
    });

    it('should throw UnauthorizedException for wrong password', async () => {
      mockPrismaService.user.findFirst.mockResolvedValue(mockUser);
      jest.spyOn(bcrypt, 'compare').mockResolvedValue(false as never);

      await expect(
        service.validateUser('<EMAIL>', 'wrongpassword', 'org-id-123')
      ).rejects.toThrow(UnauthorizedException);
    });
  });

  describe('login', () => {
    it('should login user and return tokens', async () => {
      const loginDto = {
        email: '<EMAIL>',
        password: 'password',
        organizationId: 'org-id-123',
      };

      mockPrismaService.user.findFirst.mockResolvedValue(mockUser);
      jest.spyOn(bcrypt, 'compare').mockResolvedValue(true as never);
      mockJwtService.sign.mockReturnValue('access-token');
      mockRedisService.setex.mockResolvedValue(undefined);

      const result = await service.login(loginDto);

      expect(result).toEqual({
        accessToken: 'access-token',
        refreshToken: expect.any(String),
        expiresIn: 3600,
      });
      expect(mockLoggerService.audit).toHaveBeenCalledWith('USER_LOGIN', 'success', expect.any(Object));
    });
  });

  describe('register', () => {
    it('should register new user and return tokens', async () => {
      const registerDto = {
        email: '<EMAIL>',
        password: 'StrongPassword123!',
        firstName: 'Jane',
        lastName: 'Doe',
        organizationId: 'org-id-123',
        role: UserRole.DEVELOPER,
      };

      mockPrismaService.organization.findFirst.mockResolvedValue({
        id: 'org-id-123',
        isActive: true,
      });
      mockPrismaService.user.findFirst.mockResolvedValue(null); // User doesn't exist
      mockPrismaService.user.create.mockResolvedValue({
        ...mockUser,
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Doe',
      });
      jest.spyOn(bcrypt, 'hash').mockResolvedValue('hashed-password' as never);
      mockJwtService.sign.mockReturnValue('access-token');
      mockRedisService.setex.mockResolvedValue(undefined);

      const result = await service.register(registerDto);

      expect(result).toEqual({
        accessToken: 'access-token',
        refreshToken: expect.any(String),
        expiresIn: 3600,
      });
      expect(mockLoggerService.audit).toHaveBeenCalledWith('USER_REGISTER', 'success', expect.any(Object));
    });

    it('should throw ConflictException if user already exists', async () => {
      const registerDto = {
        email: '<EMAIL>',
        password: 'StrongPassword123!',
        firstName: 'Jane',
        lastName: 'Doe',
        organizationId: 'org-id-123',
      };

      mockPrismaService.organization.findFirst.mockResolvedValue({
        id: 'org-id-123',
        isActive: true,
      });
      mockPrismaService.user.findFirst.mockResolvedValue(mockUser); // User exists

      await expect(service.register(registerDto)).rejects.toThrow(ConflictException);
    });

    it('should throw BadRequestException for weak password', async () => {
      const registerDto = {
        email: '<EMAIL>',
        password: 'weak',
        firstName: 'Jane',
        lastName: 'Doe',
        organizationId: 'org-id-123',
      };

      mockPrismaService.organization.findFirst.mockResolvedValue({
        id: 'org-id-123',
        isActive: true,
      });
      mockPrismaService.user.findFirst.mockResolvedValue(null);

      await expect(service.register(registerDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe('refreshToken', () => {
    it('should refresh token successfully', async () => {
      const refreshToken = 'valid-refresh-token';
      const tokenData = {
        userId: 'user-id-123',
        organizationId: 'org-id-123',
      };

      mockRedisService.get.mockResolvedValue(JSON.stringify(tokenData));
      mockPrismaService.user.findFirst.mockResolvedValue(mockUser);
      mockJwtService.sign.mockReturnValue('new-access-token');
      mockRedisService.del.mockResolvedValue(1);
      mockRedisService.setex.mockResolvedValue(undefined);

      const result = await service.refreshToken(refreshToken);

      expect(result).toEqual({
        accessToken: 'new-access-token',
        refreshToken: expect.any(String),
        expiresIn: 3600,
      });
      expect(mockRedisService.del).toHaveBeenCalledWith(`refresh_token:${refreshToken}`);
    });

    it('should throw UnauthorizedException for invalid refresh token', async () => {
      mockRedisService.get.mockResolvedValue(null);

      await expect(service.refreshToken('invalid-token')).rejects.toThrow(UnauthorizedException);
    });
  });

  describe('logout', () => {
    it('should logout user successfully', async () => {
      const userId = 'user-id-123';
      const refreshToken = 'refresh-token';

      mockRedisService.del.mockResolvedValue(1);
      mockRedisService.keys.mockResolvedValue(['refresh_token:token1', 'refresh_token:token2']);

      await service.logout(userId, refreshToken);

      expect(mockRedisService.del).toHaveBeenCalledWith(`refresh_token:${refreshToken}`);
      expect(mockLoggerService.audit).toHaveBeenCalledWith('USER_LOGOUT', 'success', { userId });
    });
  });

  describe('verifyToken', () => {
    it('should verify valid token', async () => {
      const token = 'valid-token';
      const payload = { userId: 'user-id-123', organizationId: 'org-id-123' };

      mockJwtService.verify.mockReturnValue(payload);

      const result = await service.verifyToken(token);

      expect(result).toEqual(payload);
    });

    it('should throw UnauthorizedException for invalid token', async () => {
      mockJwtService.verify.mockImplementation(() => {
        throw new Error('Invalid token');
      });

      await expect(service.verifyToken('invalid-token')).rejects.toThrow(UnauthorizedException);
    });
  });
});
