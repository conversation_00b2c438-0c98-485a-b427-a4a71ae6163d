import { OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaClient } from '@prisma/client';
export declare class PrismaService extends PrismaClient implements OnModuleInit, OnModuleDestroy {
    private configService;
    private readonly logger;
    constructor(configService: ConfigService);
    onModuleInit(): Promise<void>;
    onModuleDestroy(): Promise<void>;
    executeTransaction<T>(fn: (prisma: PrismaClient) => Promise<T>, maxRetries?: number): Promise<T>;
    isHealthy(): Promise<boolean>;
    forOrganization(organizationId: string): {
        users: any;
        roles: any;
        sessions: any;
        templates: any;
        templateVersions: (templateId: string) => any;
        agents: any;
        agentExecutions: any;
        tools: any;
        toolExecutions: any;
        hybrids: any;
        hybridExecutions: any;
        workflows: any;
        workflowExecutions: any;
        providers: any;
        providerUsage: any;
        hitlRequests: any;
        documents: any;
        knowledgeSearches: any;
        widgets: any;
        widgetExecutions: any;
        analytics: any;
        metrics: any;
        billing: any;
        usageMeters: any;
        quotas: any;
        notifications: any;
        notificationPreferences: any;
        sandboxes: any;
        testResults: any;
    };
    validateOrganizationAccess(userId: string, organizationId: string): Promise<boolean>;
}
