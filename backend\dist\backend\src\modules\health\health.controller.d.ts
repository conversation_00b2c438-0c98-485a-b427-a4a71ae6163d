import { HealthCheckService, HealthCheckResult } from '@nestjs/terminus';
import { PrismaHealthIndicator } from '@modules/prisma/prisma.health';
import { RedisHealthIndicator } from '@modules/redis/redis.health';
export declare class HealthController {
    private health;
    private prismaHealth;
    private redisHealth;
    constructor(health: HealthCheckService, prismaHealth: PrismaHealthIndicator, redisHealth: RedisHealthIndicator);
    check(): Promise<HealthCheckResult>;
    readiness(): Promise<HealthCheckResult>;
    liveness(): {
        status: string;
        timestamp: string;
    };
}
