apiVersion: v1
kind: Namespace
metadata:
  name: synapseai
  labels:
    name: synapseai
    environment: production
    app: synapseai-platform
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: synapseai-quota
  namespace: synapseai
spec:
  hard:
    requests.cpu: "20"
    requests.memory: 40Gi
    limits.cpu: "40"
    limits.memory: 80Gi
    persistentvolumeclaims: "10"
    services: "20"
    secrets: "20"
    configmaps: "20"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: synapseai-limits
  namespace: synapseai
spec:
  limits:
  - default:
      cpu: "1"
      memory: "2Gi"
    defaultRequest:
      cpu: "100m"
      memory: "256Mi"
    type: Container
  - default:
      storage: "10Gi"
    type: PersistentVolumeClaim
