# SynapseAI Environment Configuration
# Copy this file to .env and fill in your values

# Application
NODE_ENV=development
PORT=3001
HOST=0.0.0.0
APP_VERSION=1.0.0

# Database
DATABASE_URL=postgresql://synapseai:synapseai_dev_password@localhost:5432/synapseai_dev
DATABASE_SSL=false
DATABASE_POOL_SIZE=10

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=synapseai_redis_password
REDIS_DB=0
# REDIS_CLUSTER_NODES=redis1:6379,redis2:6379,redis3:6379

# Authentication
JWT_SECRET=your-super-secure-jwt-secret-key-here-minimum-32-characters
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d

# Security
CORS_ORIGINS=http://localhost:3000,http://localhost:3001
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Monitoring
SENTRY_DSN=https://<EMAIL>/project-id
NEW_RELIC_LICENSE_KEY=your-new-relic-license-key
LOG_LEVEL=info
LOG_DIR=./logs

# AI Providers
OPENAI_API_KEY=sk-your-openai-api-key
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key
GOOGLE_AI_API_KEY=your-google-ai-api-key
MISTRAL_API_KEY=your-mistral-api-key
GROQ_API_KEY=gsk_your-groq-api-key

# File Storage (AWS S3)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
S3_BUCKET=synapseai-uploads

# Email (SendGrid)
SENDGRID_API_KEY=SG.your-sendgrid-api-key
# Alternative: Resend
# RESEND_API_KEY=re_your-resend-api-key
# Alternative: SMTP
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-app-password

# SMS (Twilio)
TWILIO_ACCOUNT_SID=ACyour-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# Billing (Stripe)
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-stripe-webhook-secret

# Vector Database
# Pinecone
PINECONE_API_KEY=your-pinecone-api-key
PINECONE_ENVIRONMENT=your-pinecone-environment
# Alternative: Weaviate
# WEAVIATE_URL=http://localhost:8080
# WEAVIATE_API_KEY=your-weaviate-api-key

# CDN (CloudFlare)
CLOUDFLARE_API_TOKEN=your-cloudflare-api-token
CLOUDFLARE_ZONE_ID=your-cloudflare-zone-id

# Webhooks
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/slack/webhook
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/your/discord/webhook
