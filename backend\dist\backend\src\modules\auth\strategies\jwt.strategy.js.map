{"version": 3, "file": "jwt.strategy.js", "sourceRoot": "", "sources": ["../../../../../../src/modules/auth/strategies/jwt.strategy.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAAmE;AACnE,+CAAoD;AACpD,+CAAoD;AACpD,2CAA+C;AAC/C,gEAA+D;AAIxD,IAAM,WAAW,GAAjB,MAAM,WAAY,SAAQ,IAAA,2BAAgB,EAAC,uBAAQ,CAAC;IACzD,YACU,aAA4B,EAC5B,aAA4B;QAEpC,KAAK,CAAC;YACJ,cAAc,EAAE,yBAAU,CAAC,2BAA2B,EAAE;YACxD,gBAAgB,EAAE,KAAK;YACvB,WAAW,EAAE,aAAa,CAAC,GAAG,CAAS,YAAY,CAAC;SACrD,CAAC,CAAC;QAPK,kBAAa,GAAb,aAAa,CAAe;QAC5B,kBAAa,GAAb,aAAa,CAAe;IAOtC,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,OAAmB;QAEhC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC;YACnD,KAAK,EAAE;gBACL,EAAE,EAAE,OAAO,CAAC,MAAM;gBAClB,cAAc,EAAE,OAAO,CAAC,cAAc;gBACtC,QAAQ,EAAE,IAAI;aACf;YACD,OAAO,EAAE;gBACP,YAAY,EAAE;oBACZ,MAAM,EAAE;wBACN,EAAE,EAAE,IAAI;wBACR,IAAI,EAAE,IAAI;wBACV,IAAI,EAAE,IAAI;wBACV,QAAQ,EAAE,IAAI;qBACf;iBACF;aACF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;YACzC,MAAM,IAAI,8BAAqB,CAAC,4CAA4C,CAAC,CAAC;QAChF,CAAC;QAGD,OAAO;YACL,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,YAAY,EAAE,IAAI,CAAC,YAAY;SAChC,CAAC;IACJ,CAAC;CACF,CAAA;AA/CY,kCAAW;sBAAX,WAAW;IADvB,IAAA,mBAAU,GAAE;qCAGc,sBAAa;QACb,8BAAa;GAH3B,WAAW,CA+CvB"}