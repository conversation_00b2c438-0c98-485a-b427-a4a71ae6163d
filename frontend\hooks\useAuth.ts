'use client';

import { useSession, signIn, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

interface LoginCredentials {
  email: string;
  password: string;
  organizationId?: string;
}

interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  organizationId?: string;
  role?: string;
}

export function useAuth() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const login = async (credentials: LoginCredentials) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await signIn('credentials', {
        email: credentials.email,
        password: credentials.password,
        organizationId: credentials.organizationId,
        redirect: false,
      });

      if (result?.error) {
        setError('Invalid credentials');
        return false;
      }

      router.push('/dashboard');
      return true;
    } catch (err) {
      setError('Login failed');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (data: RegisterData) => {
    setIsLoading(true);
    setError(null);

    try {
      // For demo purposes, simulate registration validation
      if (!data.email || !data.password || !data.firstName || !data.lastName) {
        setError('All fields are required');
        return false;
      }

      if (data.password.length < 8) {
        setError('Password must be at least 8 characters');
        return false;
      }

      // Simulate successful registration by storing user data locally
      // In a real app, this would be sent to your backend
      const userData = {
        email: data.email,
        firstName: data.firstName,
        lastName: data.lastName,
        organizationId: data.organizationId,
        role: data.role || 'user',
        registeredAt: new Date().toISOString()
      };

      // Store in localStorage for demo (in production, this would be in your database)
      localStorage.setItem(`user_${data.email}`, JSON.stringify(userData));

      // Auto-login after successful registration
      const result = await signIn('credentials', {
        email: data.email,
        password: data.password,
        redirect: false,
      });

      if (result?.error) {
        setError('Registration successful, but login failed. Please try signing in.');
        return false;
      }

      if (result?.ok) {
        router.push('/dashboard');
        return true;
      }

      setError('Registration failed');
      return false;
    } catch (err) {
      setError('Registration failed');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    setIsLoading(true);
    try {
      await signOut({ redirect: false });
      router.push('/auth/signin');
    } finally {
      setIsLoading(false);
    }
  };

  const clearError = () => setError(null);

  return {
    user: session?.user,
    accessToken: session?.accessToken,
    isAuthenticated: !!session,
    isLoading: status === 'loading' || isLoading,
    error,
    login,
    register,
    logout,
    clearError,
  };
}
