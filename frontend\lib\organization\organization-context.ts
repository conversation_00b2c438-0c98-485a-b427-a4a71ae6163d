import { createContext, useContext } from 'react';
import { Organization, User } from '@/lib/auth';

export interface OrganizationMember {
  id: string;
  userId: string;
  organizationId: string;
  role: 'owner' | 'admin' | 'member';
  permissions: string[];
  joinedAt: string;
  lastActiveAt: string;
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    avatar?: string;
  };
}

export interface OrganizationInvite {
  id: string;
  organizationId: string;
  email: string;
  role: 'admin' | 'member';
  permissions: string[];
  invitedBy: string;
  invitedAt: string;
  expiresAt: string;
  status: 'pending' | 'accepted' | 'expired' | 'revoked';
}

export interface OrganizationUsage {
  period: 'current' | 'previous';
  users: number;
  agents: number;
  workflows: number;
  apiRequests: number;
  storage: number;
  bandwidth: number;
}

export interface OrganizationBilling {
  plan: 'free' | 'pro' | 'enterprise';
  status: 'active' | 'past_due' | 'canceled' | 'trialing';
  currentPeriodStart: string;
  currentPeriodEnd: string;
  trialEnd?: string;
  subscriptionId?: string;
  customerId?: string;
  paymentMethod?: {
    type: 'card' | 'bank_account';
    last4: string;
    brand?: string;
    expiryMonth?: number;
    expiryYear?: number;
  };
  upcomingInvoice?: {
    amount: number;
    currency: string;
    date: string;
  };
}

export interface OrganizationContextValue {
  // Current organization
  organization: Organization | null;
  
  // Organization management
  switchOrganization: (organizationId: string) => Promise<void>;
  updateOrganization: (updates: Partial<Organization>) => Promise<void>;
  deleteOrganization: () => Promise<void>;
  
  // Members management
  members: OrganizationMember[];
  invites: OrganizationInvite[];
  addMember: (email: string, role: string, permissions: string[]) => Promise<void>;
  updateMemberRole: (memberId: string, role: string, permissions: string[]) => Promise<void>;
  removeMember: (memberId: string) => Promise<void>;
  inviteMember: (email: string, role: string, permissions: string[]) => Promise<void>;
  revokeInvite: (inviteId: string) => Promise<void>;
  resendInvite: (inviteId: string) => Promise<void>;
  
  // Usage and billing
  usage: OrganizationUsage | null;
  billing: OrganizationBilling | null;
  updateBilling: (planId: string) => Promise<void>;
  
  // Permissions
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  canManageMembers: () => boolean;
  canManageBilling: () => boolean;
  canManageSettings: () => boolean;
  
  // Loading states
  isLoading: boolean;
  isUpdating: boolean;
  
  // Error handling
  error: string | null;
  clearError: () => void;
}

export const OrganizationContext = createContext<OrganizationContextValue | undefined>(undefined);

export function useOrganization(): OrganizationContextValue {
  const context = useContext(OrganizationContext);
  if (context === undefined) {
    throw new Error('useOrganization must be used within an OrganizationProvider');
  }
  return context;
}

/**
 * Organization management class
 */
export class OrganizationManager {
  private apiUrl: string;
  private accessToken: string | null = null;

  constructor(apiUrl: string) {
    this.apiUrl = apiUrl;
  }

  setAccessToken(token: string | null): void {
    this.accessToken = token;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.apiUrl}${endpoint}`;
    const headers = {
      'Content-Type': 'application/json',
      ...(this.accessToken && { Authorization: `Bearer ${this.accessToken}` }),
      ...options.headers,
    };

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: 'Request failed' }));
      throw new Error(error.message || `HTTP ${response.status}`);
    }

    return response.json();
  }

  // Organization operations
  async getOrganization(organizationId: string): Promise<Organization> {
    return this.request<Organization>(`/api/v1/organizations/${organizationId}`);
  }

  async updateOrganization(
    organizationId: string,
    updates: Partial<Organization>
  ): Promise<Organization> {
    return this.request<Organization>(`/api/v1/organizations/${organizationId}`, {
      method: 'PATCH',
      body: JSON.stringify(updates),
    });
  }

  async deleteOrganization(organizationId: string): Promise<void> {
    await this.request(`/api/v1/organizations/${organizationId}`, {
      method: 'DELETE',
    });
  }

  async switchOrganization(organizationId: string): Promise<{ accessToken: string; user: User }> {
    return this.request<{ accessToken: string; user: User }>('/api/v1/auth/switch-organization', {
      method: 'POST',
      body: JSON.stringify({ organizationId }),
    });
  }

  // Members operations
  async getMembers(organizationId: string): Promise<OrganizationMember[]> {
    return this.request<OrganizationMember[]>(`/api/v1/organizations/${organizationId}/members`);
  }

  async addMember(
    organizationId: string,
    data: { email: string; role: string; permissions: string[] }
  ): Promise<OrganizationMember> {
    return this.request<OrganizationMember>(`/api/v1/organizations/${organizationId}/members`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateMember(
    organizationId: string,
    memberId: string,
    updates: { role?: string; permissions?: string[] }
  ): Promise<OrganizationMember> {
    return this.request<OrganizationMember>(
      `/api/v1/organizations/${organizationId}/members/${memberId}`,
      {
        method: 'PATCH',
        body: JSON.stringify(updates),
      }
    );
  }

  async removeMember(organizationId: string, memberId: string): Promise<void> {
    await this.request(`/api/v1/organizations/${organizationId}/members/${memberId}`, {
      method: 'DELETE',
    });
  }

  // Invites operations
  async getInvites(organizationId: string): Promise<OrganizationInvite[]> {
    return this.request<OrganizationInvite[]>(`/api/v1/organizations/${organizationId}/invites`);
  }

  async inviteMember(
    organizationId: string,
    data: { email: string; role: string; permissions: string[] }
  ): Promise<OrganizationInvite> {
    return this.request<OrganizationInvite>(`/api/v1/organizations/${organizationId}/invites`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async revokeInvite(organizationId: string, inviteId: string): Promise<void> {
    await this.request(`/api/v1/organizations/${organizationId}/invites/${inviteId}`, {
      method: 'DELETE',
    });
  }

  async resendInvite(organizationId: string, inviteId: string): Promise<OrganizationInvite> {
    return this.request<OrganizationInvite>(
      `/api/v1/organizations/${organizationId}/invites/${inviteId}/resend`,
      {
        method: 'POST',
      }
    );
  }

  // Usage operations
  async getUsage(organizationId: string): Promise<OrganizationUsage> {
    return this.request<OrganizationUsage>(`/api/v1/organizations/${organizationId}/usage`);
  }

  // Billing operations
  async getBilling(organizationId: string): Promise<OrganizationBilling> {
    return this.request<OrganizationBilling>(`/api/v1/organizations/${organizationId}/billing`);
  }

  async updateBilling(
    organizationId: string,
    data: { planId: string; paymentMethodId?: string }
  ): Promise<OrganizationBilling> {
    return this.request<OrganizationBilling>(`/api/v1/organizations/${organizationId}/billing`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  async createCheckoutSession(
    organizationId: string,
    data: { planId: string; successUrl: string; cancelUrl: string }
  ): Promise<{ url: string }> {
    return this.request<{ url: string }>(
      `/api/v1/organizations/${organizationId}/billing/checkout`,
      {
        method: 'POST',
        body: JSON.stringify(data),
      }
    );
  }

  async createPortalSession(
    organizationId: string,
    data: { returnUrl: string }
  ): Promise<{ url: string }> {
    return this.request<{ url: string }>(
      `/api/v1/organizations/${organizationId}/billing/portal`,
      {
        method: 'POST',
        body: JSON.stringify(data),
      }
    );
  }
}

/**
 * Permission utilities
 */
export const ORGANIZATION_PERMISSIONS = {
  // Organization management
  'organization:read': 'View organization details',
  'organization:update': 'Update organization settings',
  'organization:delete': 'Delete organization',
  
  // Member management
  'members:read': 'View organization members',
  'members:invite': 'Invite new members',
  'members:update': 'Update member roles and permissions',
  'members:remove': 'Remove members',
  
  // Billing management
  'billing:read': 'View billing information',
  'billing:update': 'Update billing and subscription',
  
  // Agent management
  'agents:create': 'Create new agents',
  'agents:read': 'View agents',
  'agents:update': 'Update agents',
  'agents:delete': 'Delete agents',
  'agents:deploy': 'Deploy agents',
  
  // Workflow management
  'workflows:create': 'Create new workflows',
  'workflows:read': 'View workflows',
  'workflows:update': 'Update workflows',
  'workflows:delete': 'Delete workflows',
  'workflows:execute': 'Execute workflows',
  
  // Tool management
  'tools:create': 'Create new tools',
  'tools:read': 'View tools',
  'tools:update': 'Update tools',
  'tools:delete': 'Delete tools',
  
  // Analytics
  'analytics:read': 'View analytics and reports',
} as const;

export type OrganizationPermission = keyof typeof ORGANIZATION_PERMISSIONS;

export const ORGANIZATION_ROLES = {
  owner: {
    name: 'Owner',
    description: 'Full access to all organization features',
    permissions: Object.keys(ORGANIZATION_PERMISSIONS) as OrganizationPermission[],
  },
  admin: {
    name: 'Admin',
    description: 'Manage organization, members, and all resources',
    permissions: [
      'organization:read',
      'organization:update',
      'members:read',
      'members:invite',
      'members:update',
      'members:remove',
      'billing:read',
      'agents:create',
      'agents:read',
      'agents:update',
      'agents:delete',
      'agents:deploy',
      'workflows:create',
      'workflows:read',
      'workflows:update',
      'workflows:delete',
      'workflows:execute',
      'tools:create',
      'tools:read',
      'tools:update',
      'tools:delete',
      'analytics:read',
    ] as OrganizationPermission[],
  },
  member: {
    name: 'Member',
    description: 'Create and manage own resources',
    permissions: [
      'organization:read',
      'members:read',
      'agents:create',
      'agents:read',
      'agents:update',
      'agents:delete',
      'agents:deploy',
      'workflows:create',
      'workflows:read',
      'workflows:update',
      'workflows:delete',
      'workflows:execute',
      'tools:create',
      'tools:read',
      'tools:update',
      'tools:delete',
    ] as OrganizationPermission[],
  },
} as const;

export type OrganizationRole = keyof typeof ORGANIZATION_ROLES;
