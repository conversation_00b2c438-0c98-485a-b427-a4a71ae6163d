(()=>{var e={};e.id=47,e.ids=[47],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},22641:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},31882:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c}),r(99118),r(69319),r(77406),r(12874);var s=r(27105),a=r(15265),i=r(90157),n=r.n(i),o=r(44665),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c=["",{children:["auth",{children:["forgot-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,99118)),"C:\\laragon\\www\\max\\trae\\kilo-teset\\augsyanapseAI\\frontend\\app\\auth\\forgot-password\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,69319)),"C:\\laragon\\www\\max\\trae\\kilo-teset\\augsyanapseAI\\frontend\\app\\auth\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,77406)),"C:\\laragon\\www\\max\\trae\\kilo-teset\\augsyanapseAI\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,12874,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\laragon\\www\\max\\trae\\kilo-teset\\augsyanapseAI\\frontend\\app\\auth\\forgot-password\\page.tsx"],u="/auth/forgot-password/page",m={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/auth/forgot-password/page",pathname:"/auth/forgot-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},33196:(e,t,r)=>{Promise.resolve().then(r.bind(r,21020))},74868:()=>{},21020:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var s=r(19899),a=r(5507),i=r(54175),n=r(15950),o=r(95650),l=r(37513),c=r(20382),d=r(80046),u=r(21842),m=r(27843);let x=l.Ry({email:l.Z_().email("Please enter a valid email address")});function p(){let[e,t]=(0,a.useState)(!1),[r,l]=(0,a.useState)(!1),[p,h]=(0,a.useState)(null),{register:f,handleSubmit:g,formState:{errors:v},getValues:y}=(0,n.cI)({resolver:(0,o.F)(x),defaultValues:{email:""}}),b=async e=>{t(!0),h(null);try{await u.x.post("/api/v1/auth/forgot-password",e),l(!0)}catch(e){h(e.message||"An error occurred. Please try again.")}finally{t(!1)}},w=async()=>{let e=y("email");if(e){t(!0);try{await u.x.post("/api/v1/auth/forgot-password",{email:e})}catch(e){}finally{t(!1)}}};return r?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"text-center",children:[s.jsx(m.Z,{className:"mx-auto h-12 w-12 text-success-500"}),s.jsx("h2",{className:"mt-4 text-3xl font-bold tracking-tight text-gray-900",children:"Check your email"}),(0,s.jsxs)("p",{className:"mt-2 text-sm text-gray-600",children:["We've sent a password reset link to"," ",s.jsx("span",{className:"font-medium",children:y("email")})]})]}),s.jsx("div",{className:"rounded-md bg-blue-50 p-4",children:(0,s.jsxs)("div",{className:"text-sm text-blue-700",children:[s.jsx("p",{className:"font-medium mb-2",children:"Didn't receive the email?"}),(0,s.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-blue-600",children:[s.jsx("li",{children:"Check your spam or junk folder"}),s.jsx("li",{children:"Make sure you entered the correct email address"}),s.jsx("li",{children:"Wait a few minutes for the email to arrive"})]})]})}),(0,s.jsxs)("div",{className:"flex flex-col space-y-3",children:[s.jsx(c.z,{variant:"outline",onClick:w,loading:e,disabled:e,children:"Resend email"}),s.jsx(i.default,{href:"/auth/signin",children:s.jsx(c.z,{variant:"ghost",className:"w-full",children:"Back to sign in"})})]})]}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[s.jsx("h2",{className:"text-3xl font-bold tracking-tight text-gray-900",children:"Forgot your password?"}),s.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"Enter your email address and we'll send you a link to reset your password."})]}),p&&s.jsx("div",{className:"rounded-md bg-error-50 p-4",children:s.jsx("div",{className:"text-sm text-error-700",children:p})}),(0,s.jsxs)("form",{className:"space-y-6",onSubmit:g(b),children:[s.jsx(d.I,{label:"Email address",type:"email",autoComplete:"email",placeholder:"Enter your email",error:v.email?.message,...f("email")}),s.jsx(c.z,{type:"submit",className:"w-full",loading:e,disabled:e,children:"Send reset link"})]}),s.jsx("div",{className:"text-center",children:s.jsx(i.default,{href:"/auth/signin",className:"text-sm font-medium text-primary-600 hover:text-primary-500",children:"Back to sign in"})})]})}},20382:(e,t,r)=>{"use strict";r.d(t,{z:()=>l});var s=r(19899),a=r(5507);!function(){var e=Error("Cannot find module '@radix-ui/react-slot'");throw e.code="MODULE_NOT_FOUND",e}();var i=r(51138),n=r(66409);let o=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",gradient:"bg-gradient-to-r from-primary-500 to-primary-600 text-white hover:from-primary-600 hover:to-primary-700",success:"bg-success-500 text-white hover:bg-success-600",warning:"bg-warning-500 text-white hover:bg-warning-600",error:"bg-error-500 text-white hover:bg-error-600"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",xl:"h-12 rounded-lg px-10 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=a.forwardRef(({className:e,variant:t,size:r,asChild:a=!1,loading:i=!1,leftIcon:l,rightIcon:c,children:d,disabled:u,...m},x)=>{let p=a?Object(function(){var e=Error("Cannot find module '@radix-ui/react-slot'");throw e.code="MODULE_NOT_FOUND",e}()):"button";return(0,s.jsxs)(p,{className:(0,n.cn)(o({variant:t,size:r,className:e})),ref:x,disabled:u||i,...m,children:[i&&(0,s.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[s.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),s.jsx("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!i&&l&&s.jsx("span",{className:"mr-2",children:l}),d,!i&&c&&s.jsx("span",{className:"ml-2",children:c})]})});l.displayName="Button"},80046:(e,t,r)=>{"use strict";r.d(t,{I:()=>l});var s=r(19899),a=r(5507),i=r(51138),n=r(66409);let o=(0,i.j)("flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",{variants:{variant:{default:"border-input",error:"border-error-500 focus-visible:ring-error-500",success:"border-success-500 focus-visible:ring-success-500",warning:"border-warning-500 focus-visible:ring-warning-500"},size:{default:"h-10",sm:"h-9 px-2 text-xs",lg:"h-11 px-4",xl:"h-12 px-4 text-base"}},defaultVariants:{variant:"default",size:"default"}}),l=a.forwardRef(({className:e,variant:t,size:r,type:i,leftIcon:l,rightIcon:c,error:d,helperText:u,label:m,id:x,...p},h)=>{let f=x||a.useId(),g=!!d;return(0,s.jsxs)("div",{className:"w-full",children:[m&&s.jsx("label",{htmlFor:f,className:"block text-sm font-medium text-foreground mb-1",children:m}),(0,s.jsxs)("div",{className:"relative",children:[l&&s.jsx("div",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground",children:l}),s.jsx("input",{type:i,id:f,className:(0,n.cn)(o({variant:g?"error":t,size:r,className:e}),l&&"pl-10",c&&"pr-10"),ref:h,...p}),c&&s.jsx("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground",children:c})]}),(d||u)&&s.jsx("p",{className:(0,n.cn)("text-xs mt-1",g?"text-error-600":"text-muted-foreground"),children:d||u})]})});l.displayName="Input"},21842:(e,t,r)=>{"use strict";r.d(t,{x:()=>n});var s=r(46294),a=r(60878);class i{constructor(){this.baseURL="http://localhost:3000",this.client=s.Z.create({baseURL:this.baseURL,timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors()}setupInterceptors(){this.client.interceptors.request.use(async e=>{let t=await (0,a.getSession)();return t?.accessToken&&(e.headers.Authorization=`Bearer ${t.accessToken}`),t?.user?.organizationId&&(e.headers["X-Organization-ID"]=t.user.organizationId),e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,async e=>{let t=e.config;if(e.response?.status===401&&!t._retry){t._retry=!0;try{let e=await (0,a.getSession)();if(e?.refreshToken){let r=await this.post("/api/v1/auth/refresh",{refreshToken:e.refreshToken});if(r.data.accessToken)return t.headers.Authorization=`Bearer ${r.data.accessToken}`,this.client(t)}}catch(e){return Promise.reject(e)}}return Promise.reject({message:e.response?.data?.message||e.message||"An unexpected error occurred",statusCode:e.response?.status||500,timestamp:new Date().toISOString(),path:e.config?.url})})}async get(e,t){return this.client.get(e,t)}async post(e,t,r){return this.client.post(e,t,r)}async put(e,t,r){return this.client.put(e,t,r)}async patch(e,t,r){return this.client.patch(e,t,r)}async delete(e,t){return this.client.delete(e,t)}async upload(e,t,r){let s=new FormData;return s.append("file",t),this.client.post(e,s,{headers:{"Content-Type":"multipart/form-data"},onUploadProgress:e=>{r&&e.total&&r(Math.round(100*e.loaded/e.total))}})}async healthCheck(){return this.get("/api/v1/health")}getBaseURL(){return this.baseURL}}let n=new i},66409:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a});var s=r(23332);function a(...e){return Object(function(){var e=Error("Cannot find module 'tailwind-merge'");throw e.code="MODULE_NOT_FOUND",e}())((0,s.W)(e))}!function(){var e=Error("Cannot find module 'tailwind-merge'");throw e.code="MODULE_NOT_FOUND",e}()},99118:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2772).createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\app\auth\forgot-password\page.tsx#default`)},69319:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,metadata:()=>a});var s=r(35023);let a={title:"Authentication",description:"Sign in to your SynapseAI account"};function i({children:e}){return(0,s.jsxs)("div",{className:"min-h-screen flex",children:[(0,s.jsxs)("div",{className:"hidden lg:flex lg:w-1/2 bg-gradient-to-br from-primary-600 to-primary-800 relative overflow-hidden",children:[s.jsx("div",{className:"absolute inset-0 bg-black/20"}),s.jsx("div",{className:"relative z-10 flex flex-col justify-center px-12 text-white",children:(0,s.jsxs)("div",{className:"max-w-md",children:[s.jsx("h1",{className:"text-4xl font-bold mb-6",children:"Welcome to SynapseAI"}),s.jsx("p",{className:"text-xl text-primary-100 mb-8",children:"The universal AI orchestration platform that empowers you to build, deploy, and manage intelligent agents and workflows with ease."}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-2 h-2 bg-primary-300 rounded-full"}),s.jsx("span",{className:"text-primary-100",children:"No-code AI agent builder"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-2 h-2 bg-primary-300 rounded-full"}),s.jsx("span",{className:"text-primary-100",children:"Enterprise-grade security"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-2 h-2 bg-primary-300 rounded-full"}),s.jsx("span",{className:"text-primary-100",children:"Real-time collaboration"})]})]})]})}),s.jsx("div",{className:"absolute top-0 right-0 w-64 h-64 bg-primary-400/20 rounded-full -translate-y-32 translate-x-32"}),s.jsx("div",{className:"absolute bottom-0 left-0 w-96 h-96 bg-primary-400/10 rounded-full translate-y-48 -translate-x-48"})]}),s.jsx("div",{className:"flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:px-20 xl:px-24",children:(0,s.jsxs)("div",{className:"mx-auto w-full max-w-sm lg:w-96",children:[s.jsx("div",{className:"mb-8",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[s.jsx("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:s.jsx("span",{className:"text-white font-bold text-sm",children:"S"})}),s.jsx("span",{className:"text-2xl font-bold text-gray-900",children:"SynapseAI"})]})}),e]})})]})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[942,544,695,137,188],()=>r(31882));module.exports=s})();