'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useSession } from 'next-auth/react';
import {
  Organization,
  OrganizationContextValue,
  OrganizationManager,
  OrganizationMember,
  OrganizationInvite,
  OrganizationUsage,
  OrganizationBilling,
  ORGANIZATION_ROLES,
  OrganizationContext,
} from '@/lib/organization/organization-context';
import { User } from '@/lib/auth';
import { useA11y } from '@/components/accessibility/A11yProvider';

interface OrganizationProviderProps {
  children: ReactNode;
}

export function OrganizationProvider({ children }: OrganizationProviderProps) {
  const { data: session, update: updateSession } = useSession();
  const { announceStatus } = useA11y();

  // State
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [members, setMembers] = useState<OrganizationMember[]>([]);
  const [invites, setInvites] = useState<OrganizationInvite[]>([]);
  const [usage, setUsage] = useState<OrganizationUsage | null>(null);
  const [billing, setBilling] = useState<OrganizationBilling | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Organization manager
  const [orgManager] = useState(() => 
    new OrganizationManager(process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000')
  );

  const user = session?.user as User;
  const currentOrganizationId = user?.organizationId;

  // Update access token when session changes
  useEffect(() => {
    if (session?.accessToken) {
      orgManager.setAccessToken(session.accessToken);
    }
  }, [session?.accessToken, orgManager]);

  // Load organization data when organization ID changes
  useEffect(() => {
    if (currentOrganizationId) {
      loadOrganizationData();
    } else {
      // Clear data if no organization
      setOrganization(null);
      setMembers([]);
      setInvites([]);
      setUsage(null);
      setBilling(null);
    }
  }, [currentOrganizationId]);

  const loadOrganizationData = async () => {
    if (!currentOrganizationId) return;

    setIsLoading(true);
    setError(null);

    try {
      // Load organization details
      const orgData = await orgManager.getOrganization(currentOrganizationId);
      setOrganization(orgData);

      // Load members and invites in parallel
      const [membersData, invitesData, usageData, billingData] = await Promise.allSettled([
        orgManager.getMembers(currentOrganizationId),
        orgManager.getInvites(currentOrganizationId),
        orgManager.getUsage(currentOrganizationId),
        orgManager.getBilling(currentOrganizationId),
      ]);

      if (membersData.status === 'fulfilled') {
        setMembers(membersData.value);
      }

      if (invitesData.status === 'fulfilled') {
        setInvites(invitesData.value);
      }

      if (usageData.status === 'fulfilled') {
        setUsage(usageData.value);
      }

      if (billingData.status === 'fulfilled') {
        setBilling(billingData.value);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load organization data';
      setError(errorMessage);
      announceStatus(`Error: ${errorMessage}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const switchOrganization = async (organizationId: string) => {
    if (organizationId === currentOrganizationId) return;

    setIsUpdating(true);
    setError(null);

    try {
      const { accessToken, user: updatedUser } = await orgManager.switchOrganization(organizationId);
      
      // Update session with new access token and user data
      await updateSession({
        accessToken,
        user: updatedUser,
      });

      announceStatus('Organization switched successfully', 'success');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to switch organization';
      setError(errorMessage);
      announceStatus(`Error: ${errorMessage}`, 'error');
      throw error;
    } finally {
      setIsUpdating(false);
    }
  };

  const updateOrganization = async (updates: Partial<Organization>) => {
    if (!currentOrganizationId) throw new Error('No organization selected');

    setIsUpdating(true);
    setError(null);

    try {
      const updatedOrg = await orgManager.updateOrganization(currentOrganizationId, updates);
      setOrganization(updatedOrg);
      announceStatus('Organization updated successfully', 'success');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update organization';
      setError(errorMessage);
      announceStatus(`Error: ${errorMessage}`, 'error');
      throw error;
    } finally {
      setIsUpdating(false);
    }
  };

  const deleteOrganization = async () => {
    if (!currentOrganizationId) throw new Error('No organization selected');

    setIsUpdating(true);
    setError(null);

    try {
      await orgManager.deleteOrganization(currentOrganizationId);
      
      // Clear organization data
      setOrganization(null);
      setMembers([]);
      setInvites([]);
      setUsage(null);
      setBilling(null);

      announceStatus('Organization deleted successfully', 'success');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete organization';
      setError(errorMessage);
      announceStatus(`Error: ${errorMessage}`, 'error');
      throw error;
    } finally {
      setIsUpdating(false);
    }
  };

  const addMember = async (email: string, role: string, permissions: string[]) => {
    if (!currentOrganizationId) throw new Error('No organization selected');

    setIsUpdating(true);
    setError(null);

    try {
      const newMember = await orgManager.addMember(currentOrganizationId, {
        email,
        role,
        permissions,
      });
      
      setMembers(prev => [...prev, newMember]);
      announceStatus('Member added successfully', 'success');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to add member';
      setError(errorMessage);
      announceStatus(`Error: ${errorMessage}`, 'error');
      throw error;
    } finally {
      setIsUpdating(false);
    }
  };

  const updateMemberRole = async (memberId: string, role: string, permissions: string[]) => {
    if (!currentOrganizationId) throw new Error('No organization selected');

    setIsUpdating(true);
    setError(null);

    try {
      const updatedMember = await orgManager.updateMember(currentOrganizationId, memberId, {
        role,
        permissions,
      });
      
      setMembers(prev => prev.map(member => 
        member.id === memberId ? updatedMember : member
      ));
      
      announceStatus('Member role updated successfully', 'success');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update member role';
      setError(errorMessage);
      announceStatus(`Error: ${errorMessage}`, 'error');
      throw error;
    } finally {
      setIsUpdating(false);
    }
  };

  const removeMember = async (memberId: string) => {
    if (!currentOrganizationId) throw new Error('No organization selected');

    setIsUpdating(true);
    setError(null);

    try {
      await orgManager.removeMember(currentOrganizationId, memberId);
      setMembers(prev => prev.filter(member => member.id !== memberId));
      announceStatus('Member removed successfully', 'success');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to remove member';
      setError(errorMessage);
      announceStatus(`Error: ${errorMessage}`, 'error');
      throw error;
    } finally {
      setIsUpdating(false);
    }
  };

  const inviteMember = async (email: string, role: string, permissions: string[]) => {
    if (!currentOrganizationId) throw new Error('No organization selected');

    setIsUpdating(true);
    setError(null);

    try {
      const newInvite = await orgManager.inviteMember(currentOrganizationId, {
        email,
        role,
        permissions,
      });
      
      setInvites(prev => [...prev, newInvite]);
      announceStatus('Invitation sent successfully', 'success');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to send invitation';
      setError(errorMessage);
      announceStatus(`Error: ${errorMessage}`, 'error');
      throw error;
    } finally {
      setIsUpdating(false);
    }
  };

  const revokeInvite = async (inviteId: string) => {
    if (!currentOrganizationId) throw new Error('No organization selected');

    setIsUpdating(true);
    setError(null);

    try {
      await orgManager.revokeInvite(currentOrganizationId, inviteId);
      setInvites(prev => prev.filter(invite => invite.id !== inviteId));
      announceStatus('Invitation revoked successfully', 'success');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to revoke invitation';
      setError(errorMessage);
      announceStatus(`Error: ${errorMessage}`, 'error');
      throw error;
    } finally {
      setIsUpdating(false);
    }
  };

  const resendInvite = async (inviteId: string) => {
    if (!currentOrganizationId) throw new Error('No organization selected');

    setIsUpdating(true);
    setError(null);

    try {
      const updatedInvite = await orgManager.resendInvite(currentOrganizationId, inviteId);
      setInvites(prev => prev.map(invite => 
        invite.id === inviteId ? updatedInvite : invite
      ));
      announceStatus('Invitation resent successfully', 'success');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to resend invitation';
      setError(errorMessage);
      announceStatus(`Error: ${errorMessage}`, 'error');
      throw error;
    } finally {
      setIsUpdating(false);
    }
  };

  const updateBilling = async (planId: string) => {
    if (!currentOrganizationId) throw new Error('No organization selected');

    setIsUpdating(true);
    setError(null);

    try {
      const updatedBilling = await orgManager.updateBilling(currentOrganizationId, { planId });
      setBilling(updatedBilling);
      announceStatus('Billing updated successfully', 'success');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update billing';
      setError(errorMessage);
      announceStatus(`Error: ${errorMessage}`, 'error');
      throw error;
    } finally {
      setIsUpdating(false);
    }
  };

  // Permission helpers
  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    return user.permissions?.includes(permission) || false;
  };

  const hasRole = (role: string): boolean => {
    if (!user) return false;
    return user.organizationRole === role;
  };

  const canManageMembers = (): boolean => {
    return hasPermission('members:invite') || hasPermission('members:update') || hasPermission('members:remove');
  };

  const canManageBilling = (): boolean => {
    return hasPermission('billing:update') || hasRole('owner');
  };

  const canManageSettings = (): boolean => {
    return hasPermission('organization:update') || hasRole('owner') || hasRole('admin');
  };

  const clearError = () => {
    setError(null);
  };

  const contextValue: OrganizationContextValue = {
    organization,
    switchOrganization,
    updateOrganization,
    deleteOrganization,
    members,
    invites,
    addMember,
    updateMemberRole,
    removeMember,
    inviteMember,
    revokeInvite,
    resendInvite,
    usage,
    billing,
    updateBilling,
    hasPermission,
    hasRole,
    canManageMembers,
    canManageBilling,
    canManageSettings,
    isLoading,
    isUpdating,
    error,
    clearError,
  };

  return (
    <OrganizationContext.Provider value={contextValue}>
      {children}
    </OrganizationContext.Provider>
  );
}

// Hook for organization switching
export function useOrganizationSwitcher() {
  const { switchOrganization, isUpdating } = useOrganization();
  const { data: session } = useSession();

  const availableOrganizations = React.useMemo(() => {
    // This would typically come from the user's session or a separate API call
    // For now, we'll return an empty array
    return [];
  }, [session]);

  return {
    availableOrganizations,
    switchOrganization,
    isUpdating,
  };
}

// Hook for organization permissions
export function useOrganizationPermissions() {
  const { hasPermission, hasRole, canManageMembers, canManageBilling, canManageSettings } = useOrganization();
  const { data: session } = useSession();
  const user = session?.user as User;

  return {
    user,
    hasPermission,
    hasRole,
    canManageMembers,
    canManageBilling,
    canManageSettings,
    isOwner: hasRole('owner'),
    isAdmin: hasRole('admin') || hasRole('owner'),
    isMember: hasRole('member'),
  };
}
