'use client';

import { useState } from 'react';
import { signIn, getSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Alert } from '@/components/ui/Alert';
import { useA11y } from '@/components/accessibility/A11yProvider';
import {
  EyeIcon,
  EyeSlashIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';

interface SignInFormProps {
  callbackUrl?: string;
  showSocialLogin?: boolean;
  showOrganizationField?: boolean;
}

export function SignInForm({
  callbackUrl = '/dashboard',
  showSocialLogin = true,
  showOrganizationField = false,
}: SignInFormProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { announceFormErrors, announceStatus } = useA11y();

  const [formData, setFormData] = useState({
    email: '',
    password: '',
    organizationId: '',
    twoFactorCode: '',
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showTwoFactor, setShowTwoFactor] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});

  const errorParam = searchParams.get('error');
  const emailParam = searchParams.get('email');

  // Pre-fill email if provided in URL
  useState(() => {
    if (emailParam) {
      setFormData(prev => ({ ...prev, email: emailParam }));
    }
  });

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.email) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (!formData.password) {
      errors.password = 'Password is required';
    } else if (formData.password.length < 8) {
      errors.password = 'Password must be at least 8 characters';
    }

    if (showTwoFactor && !formData.twoFactorCode) {
      errors.twoFactorCode = '2FA code is required';
    }

    setFieldErrors(errors);

    if (Object.keys(errors).length > 0) {
      announceFormErrors(Object.values(errors));
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsLoading(true);
    setError('');

    try {
      const result = await signIn('credentials', {
        email: formData.email,
        password: formData.password,
        organizationId: formData.organizationId || undefined,
        twoFactorCode: formData.twoFactorCode || undefined,
        redirect: false,
      });

      if (result?.error) {
        if (result.error === 'TwoFactorRequired') {
          setShowTwoFactor(true);
          announceStatus('Two-factor authentication code required', 'info');
          return;
        }

        setError(result.error);
        announceFormErrors([result.error]);
      } else if (result?.ok) {
        announceStatus('Sign in successful', 'success');
        
        // Get updated session to check user status
        const session = await getSession();
        const user = session?.user as any;

        if (user && !user.emailVerified) {
          router.push('/auth/verify-email');
        } else if (user && !user.organizationId && showOrganizationField) {
          router.push('/onboarding/organization');
        } else {
          router.push(callbackUrl);
        }
      }
    } catch (error) {
      const errorMessage = 'An unexpected error occurred. Please try again.';
      setError(errorMessage);
      announceFormErrors([errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSocialSignIn = async (provider: 'google' | 'github') => {
    setIsLoading(true);
    announceStatus(`Signing in with ${provider}`, 'info');

    try {
      await signIn(provider, {
        callbackUrl,
        redirect: true,
      });
    } catch (error) {
      const errorMessage = `Failed to sign in with ${provider}`;
      setError(errorMessage);
      announceFormErrors([errorMessage]);
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when user starts typing
    if (fieldErrors[field]) {
      setFieldErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl font-bold text-center">
          Sign in to SynapseAI
        </CardTitle>
        <p className="text-sm text-gray-600 text-center">
          Enter your credentials to access your account
        </p>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Error Messages */}
        {(error || errorParam) && (
          <Alert variant="error">
            <ExclamationTriangleIcon className="h-4 w-4" />
            <span>{error || getErrorMessage(errorParam)}</span>
          </Alert>
        )}

        {/* Social Login */}
        {showSocialLogin && (
          <div className="space-y-3">
            <Button
              type="button"
              variant="outline"
              className="w-full"
              onClick={() => handleSocialSignIn('google')}
              disabled={isLoading}
            >
              <svg className="w-4 h-4 mr-2" viewBox="0 0 24 24">
                <path
                  fill="currentColor"
                  d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                />
                <path
                  fill="currentColor"
                  d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                />
                <path
                  fill="currentColor"
                  d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                />
                <path
                  fill="currentColor"
                  d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                />
              </svg>
              Continue with Google
            </Button>

            <Button
              type="button"
              variant="outline"
              className="w-full"
              onClick={() => handleSocialSignIn('github')}
              disabled={isLoading}
            >
              <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
              </svg>
              Continue with GitHub
            </Button>

            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-white px-2 text-gray-500">Or continue with email</span>
              </div>
            </div>
          </div>
        )}

        {/* Sign In Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Email Field */}
          <div>
            <Input
              id="email"
              type="email"
              placeholder="Enter your email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              error={fieldErrors.email || undefined}
              disabled={isLoading}
              autoComplete="email"
              required
              aria-describedby={fieldErrors.email ? 'email-error' : undefined}
            />
          </div>

          {/* Password Field */}
          <div>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? 'text' : 'password'}
                placeholder="Enter your password"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                error={fieldErrors.password || undefined}
                disabled={isLoading}
                autoComplete="current-password"
                required
                aria-describedby={fieldErrors.password ? 'password-error' : undefined}
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                onClick={() => setShowPassword(!showPassword)}
                aria-label={showPassword ? 'Hide password' : 'Show password'}
              >
                {showPassword ? (
                  <EyeSlashIcon className="h-4 w-4 text-gray-400" />
                ) : (
                  <EyeIcon className="h-4 w-4 text-gray-400" />
                )}
              </button>
            </div>
          </div>

          {/* Organization ID Field */}
          {showOrganizationField && (
            <div>
              <Input
                id="organizationId"
                type="text"
                placeholder="Organization ID (optional)"
                value={formData.organizationId}
                onChange={(e) => handleInputChange('organizationId', e.target.value)}
                disabled={isLoading}
                autoComplete="organization"
              />
            </div>
          )}

          {/* Two-Factor Code Field */}
          {showTwoFactor && (
            <div>
              <Input
                id="twoFactorCode"
                type="text"
                placeholder="Enter 2FA code"
                value={formData.twoFactorCode}
                onChange={(e) => handleInputChange('twoFactorCode', e.target.value)}
                error={fieldErrors.twoFactorCode || undefined}
                disabled={isLoading}
                autoComplete="one-time-code"
                maxLength={6}
                required
                aria-describedby={fieldErrors.twoFactorCode ? 'twoFactor-error' : undefined}
              />
              <p className="text-xs text-gray-600 mt-1">
                Enter the 6-digit code from your authenticator app
              </p>
            </div>
          )}

          {/* Submit Button */}
          <Button
            type="submit"
            className="w-full"
            loading={isLoading}
            disabled={isLoading}
          >
            {showTwoFactor ? 'Verify & Sign In' : 'Sign In'}
          </Button>
        </form>

        {/* Footer Links */}
        <div className="text-center space-y-2">
          <a
            href="/auth/forgot-password"
            className="text-sm text-primary-600 hover:text-primary-500"
          >
            Forgot your password?
          </a>
          <div className="text-sm text-gray-600">
            Don't have an account?{' '}
            <a
              href="/auth/signup"
              className="text-primary-600 hover:text-primary-500 font-medium"
            >
              Sign up
            </a>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function getErrorMessage(error: string | null): string {
  switch (error) {
    case 'CredentialsSignin':
      return 'Invalid email or password. Please try again.';
    case 'EmailNotVerified':
      return 'Please verify your email address before signing in.';
    case 'TwoFactorRequired':
      return 'Two-factor authentication is required for your account.';
    case 'AccountLocked':
      return 'Your account has been temporarily locked. Please try again later.';
    case 'OrganizationRequired':
      return 'Please select an organization to continue.';
    default:
      return 'An error occurred during sign in. Please try again.';
  }
}
