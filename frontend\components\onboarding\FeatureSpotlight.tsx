'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { FeatureDiscovery, Feature } from '@/lib/onboarding/feature-discovery';
import { useAppStore } from '@/lib/store';
import {
  XMarkIcon,
  SparklesIcon,
  LightBulbIcon,
  CheckIcon,
  EyeIcon,
  EyeSlashIcon,
  PlayIcon,
  BookOpenIcon,
  ChevronRightIcon,
} from '@heroicons/react/24/outline';

interface FeatureSpotlightProps {
  discovery: FeatureDiscovery;
  onFeatureActivate?: (featureId: string) => void;
  onSpotlightDismiss?: (featureId: string) => void;
  className?: string;
}

export function FeatureSpotlight({
  discovery,
  onFeatureActivate,
  onSpotlightDismiss,
  className,
}: FeatureSpotlightProps) {
  const [activeSpotlights, setActiveSpotlights] = useState<Array<{
    featureId: string;
    feature: Feature;
    position: { x: number; y: number };
  }>>([]);
  const [discoveryQueue, setDiscoveryQueue] = useState<Feature[]>([]);
  const [recommendedFeatures, setRecommendedFeatures] = useState<Feature[]>([]);
  const [showDiscoveryPanel, setShowDiscoveryPanel] = useState(false);
  const [userProgress, setUserProgress] = useState(discovery.getUserProgress());

  const spotlightRefs = useRef<Map<string, HTMLDivElement>>(new Map());
  const { addNotification } = useAppStore();

  useEffect(() => {
    // Set up discovery event listeners
    const handleSpotlightShown = (data: { featureId: string; spotlight: any }) => {
      const feature = discovery.getFeature(data.featureId);
      if (!feature) return;

      const targetElement = document.querySelector(data.spotlight.selector);
      if (!targetElement) return;

      const rect = targetElement.getBoundingClientRect();
      const position = calculateSpotlightPosition(rect, data.spotlight.position);

      setActiveSpotlights(prev => [
        ...prev.filter(s => s.featureId !== data.featureId),
        {
          featureId: data.featureId,
          feature,
          position,
        },
      ]);
    };

    const handleSpotlightHidden = (data: { featureId: string }) => {
      setActiveSpotlights(prev => prev.filter(s => s.featureId !== data.featureId));
    };

    const handleFeatureUnlocked = (data: { featureId: string }) => {
      const feature = discovery.getFeature(data.featureId);
      if (feature) {
        addNotification({
          type: 'success',
          title: 'New Feature Unlocked!',
          message: `${feature.name} is now available`,
        });
        
        updateDiscoveryData();
        setShowDiscoveryPanel(true);
      }
    };

    const handleFeatureActivated = (data: { featureId: string }) => {
      const feature = discovery.getFeature(data.featureId);
      if (feature) {
        addNotification({
          type: 'info',
          title: 'Feature Activated',
          message: `${feature.name} is now active`,
        });
        
        updateDiscoveryData();
      }
    };

    const handleAchievementEarned = (data: { achievementId: string }) => {
      addNotification({
        type: 'success',
        title: 'Achievement Unlocked!',
        message: `You earned: ${data.achievementId}`,
      });
    };

    discovery.on('spotlight_shown', handleSpotlightShown);
    discovery.on('spotlight_hidden', handleSpotlightHidden);
    discovery.on('feature_unlocked', handleFeatureUnlocked);
    discovery.on('feature_activated', handleFeatureActivated);
    discovery.on('achievement_earned', handleAchievementEarned);

    // Initial data load
    updateDiscoveryData();

    // Check for unlocks periodically
    const interval = setInterval(() => {
      discovery.checkUnlocks();
      updateDiscoveryData();
    }, 10000);

    return () => {
      discovery.off('spotlight_shown', handleSpotlightShown);
      discovery.off('spotlight_hidden', handleSpotlightHidden);
      discovery.off('feature_unlocked', handleFeatureUnlocked);
      discovery.off('feature_activated', handleFeatureActivated);
      discovery.off('achievement_earned', handleAchievementEarned);
      clearInterval(interval);
    };
  }, [discovery, addNotification]);

  const updateDiscoveryData = () => {
    setDiscoveryQueue(discovery.getDiscoveryQueue());
    setRecommendedFeatures(discovery.getRecommendedFeatures());
    setUserProgress(discovery.getUserProgress());
  };

  const calculateSpotlightPosition = (
    rect: DOMRect,
    position: 'top' | 'bottom' | 'left' | 'right' | 'center'
  ): { x: number; y: number } => {
    const offset = 20;
    
    switch (position) {
      case 'top':
        return {
          x: rect.left + rect.width / 2,
          y: rect.top - offset,
        };
      case 'bottom':
        return {
          x: rect.left + rect.width / 2,
          y: rect.bottom + offset,
        };
      case 'left':
        return {
          x: rect.left - offset,
          y: rect.top + rect.height / 2,
        };
      case 'right':
        return {
          x: rect.right + offset,
          y: rect.top + rect.height / 2,
        };
      case 'center':
        return {
          x: rect.left + rect.width / 2,
          y: rect.top + rect.height / 2,
        };
      default:
        return {
          x: rect.left + rect.width / 2,
          y: rect.bottom + offset,
        };
    }
  };

  const handleActivateFeature = (featureId: string) => {
    discovery.activateFeature(featureId);
    onFeatureActivate?.(featureId);
  };

  const handleDismissSpotlight = (featureId: string) => {
    discovery.hideSpotlight(featureId);
    onSpotlightDismiss?.(featureId);
  };

  const handleDismissFromQueue = (featureId: string) => {
    const queue = discovery.getDiscoveryQueue();
    const index = queue.findIndex(f => f.id === featureId);
    if (index > -1) {
      queue.splice(index, 1);
      setDiscoveryQueue([...queue]);
    }
  };

  const getComplexityColor = (complexity: Feature['complexity']) => {
    switch (complexity) {
      case 'beginner': return 'success';
      case 'intermediate': return 'warning';
      case 'advanced': return 'error';
      default: return 'secondary';
    }
  };

  const getCategoryIcon = (category: Feature['category']) => {
    switch (category) {
      case 'core': return '⚡';
      case 'advanced': return '🚀';
      case 'premium': return '💎';
      case 'experimental': return '🧪';
      default: return '✨';
    }
  };

  const renderFeatureCard = (feature: Feature, showActions: boolean = true) => (
    <div
      key={feature.id}
      className="border border-gray-200 rounded-lg p-4 space-y-3 bg-white"
    >
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3 flex-1">
          <div className="text-2xl">{feature.ui.icon}</div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-1">
              <h4 className="font-medium text-sm text-gray-900 truncate">
                {feature.name}
              </h4>
              <Badge variant={getComplexityColor(feature.complexity)} size="sm">
                {feature.complexity}
              </Badge>
              {feature.ui.badge && (
                <Badge variant="info" size="sm">
                  {feature.ui.badge}
                </Badge>
              )}
            </div>
            
            <p className="text-xs text-gray-600 mb-2">
              {feature.description}
            </p>

            {/* Benefits */}
            <div className="space-y-1">
              <div className="text-xs font-medium text-gray-700">Benefits:</div>
              <ul className="text-xs text-gray-600 space-y-0.5">
                {feature.benefits.slice(0, 3).map((benefit, index) => (
                  <li key={index} className="flex items-start space-x-1">
                    <CheckIcon className="h-3 w-3 text-success-500 mt-0.5 flex-shrink-0" />
                    <span>{benefit}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Risks */}
            {feature.risks && feature.risks.length > 0 && (
              <div className="space-y-1 mt-2">
                <div className="text-xs font-medium text-warning-700">Considerations:</div>
                <ul className="text-xs text-warning-600 space-y-0.5">
                  {feature.risks.slice(0, 2).map((risk, index) => (
                    <li key={index} className="flex items-start space-x-1">
                      <span>⚠️</span>
                      <span>{risk}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-1">
          <span className="text-xs text-gray-500">
            {Math.round(feature.analytics.adoptionRate * 100)}% adoption
          </span>
        </div>
      </div>

      {/* Actions */}
      {showActions && (
        <div className="flex items-center justify-between pt-2 border-t border-gray-100">
          <div className="flex space-x-2">
            <Button
              size="sm"
              onClick={() => handleActivateFeature(feature.id)}
              leftIcon={<CheckIcon className="h-3 w-3" />}
            >
              Activate
            </Button>
            
            <Button
              size="sm"
              variant="outline"
              leftIcon={<BookOpenIcon className="h-3 w-3" />}
            >
              Learn More
            </Button>
          </div>

          <Button
            size="sm"
            variant="ghost"
            onClick={() => handleDismissFromQueue(feature.id)}
            leftIcon={<XMarkIcon className="h-3 w-3" />}
          >
            Dismiss
          </Button>
        </div>
      )}
    </div>
  );

  return (
    <>
      {/* Active Spotlights */}
      {activeSpotlights.map(({ featureId, feature, position }) => (
        <div
          key={featureId}
          ref={(el) => {
            if (el) spotlightRefs.current.set(featureId, el);
          }}
          className="fixed z-50 pointer-events-none"
          style={{
            left: position.x - 150, // Center the 300px wide spotlight
            top: position.y,
          }}
        >
          <Card className="w-80 shadow-xl border-2 border-primary-300 pointer-events-auto">
            <CardContent className="p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <div className="text-lg">{feature.ui.icon}</div>
                  <div>
                    <h4 className="font-medium text-sm text-gray-900">
                      {feature.name}
                    </h4>
                    <Badge variant="info" size="sm">
                      New Feature!
                    </Badge>
                  </div>
                </div>
                
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => handleDismissSpotlight(featureId)}
                  leftIcon={<XMarkIcon className="h-3 w-3" />}
                />
              </div>

              <p className="text-sm text-gray-600 mb-3">
                {feature.ui.spotlight?.message || feature.description}
              </p>

              <div className="flex items-center space-x-2">
                <Button
                  size="sm"
                  onClick={() => handleActivateFeature(featureId)}
                  leftIcon={<SparklesIcon className="h-3 w-3" />}
                >
                  Try It Now
                </Button>
                
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleDismissSpotlight(featureId)}
                >
                  Later
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      ))}

      {/* Discovery Panel Toggle */}
      {(discoveryQueue.length > 0 || recommendedFeatures.length > 0) && (
        <div className="fixed bottom-6 left-6 z-40">
          <Button
            onClick={() => setShowDiscoveryPanel(!showDiscoveryPanel)}
            className="rounded-full shadow-lg"
            leftIcon={<LightBulbIcon className="h-5 w-5" />}
          >
            {discoveryQueue.length + recommendedFeatures.length} New Features
            {discoveryQueue.length > 0 && (
              <Badge variant="error" size="sm" className="ml-2">
                {discoveryQueue.length}
              </Badge>
            )}
          </Button>
        </div>
      )}

      {/* Discovery Panel */}
      {showDiscoveryPanel && (
        <div className="fixed bottom-20 left-6 z-40 w-96">
          <Card className="shadow-xl">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-lg flex items-center space-x-2">
                  <LightBulbIcon className="h-5 w-5 text-primary-600" />
                  <span>Feature Discovery</span>
                </h3>
                
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setShowDiscoveryPanel(false)}
                  leftIcon={<XMarkIcon className="h-4 w-4" />}
                />
              </div>

              <div className="space-y-4 max-h-96 overflow-y-auto">
                {/* Newly Unlocked */}
                {discoveryQueue.length > 0 && (
                  <div>
                    <h4 className="font-medium text-sm text-gray-900 mb-2 flex items-center space-x-1">
                      <SparklesIcon className="h-4 w-4 text-primary-600" />
                      <span>Just Unlocked</span>
                      <Badge variant="error" size="sm">
                        {discoveryQueue.length}
                      </Badge>
                    </h4>
                    <div className="space-y-3">
                      {discoveryQueue.map(feature => renderFeatureCard(feature))}
                    </div>
                  </div>
                )}

                {/* Recommended */}
                {recommendedFeatures.length > 0 && (
                  <div>
                    <h4 className="font-medium text-sm text-gray-900 mb-2 flex items-center space-x-1">
                      <ChevronRightIcon className="h-4 w-4 text-gray-600" />
                      <span>Recommended for You</span>
                    </h4>
                    <div className="space-y-3">
                      {recommendedFeatures.slice(0, 3).map(feature => renderFeatureCard(feature))}
                    </div>
                  </div>
                )}

                {/* Progress Summary */}
                <div className="pt-3 border-t border-gray-200">
                  <div className="text-xs text-gray-600 space-y-1">
                    <div>Active Features: {userProgress.activeFeatures.length}</div>
                    <div>Unlocked Features: {userProgress.unlockedFeatures.length}</div>
                    <div>Achievements: {userProgress.achievements.length}</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </>
  );
}
