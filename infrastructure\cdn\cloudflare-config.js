/**
 * CloudFlare CDN Configuration for SynapseAI
 * Global content delivery optimization for AI platform assets
 */

const cloudflareConfig = {
  // Zone Configuration
  zone: {
    name: 'synapseai.com',
    type: 'full',
    plan: 'enterprise',
  },

  // DNS Records
  dns: [
    {
      type: 'A',
      name: '@',
      content: '104.16.123.456', // Production server IP
      ttl: 300,
      proxied: true,
    },
    {
      type: 'A',
      name: 'api',
      content: '104.16.123.456',
      ttl: 300,
      proxied: true,
    },
    {
      type: 'A',
      name: 'cdn',
      content: '104.16.123.456',
      ttl: 300,
      proxied: true,
    },
    {
      type: 'CNAME',
      name: 'www',
      content: 'synapseai.com',
      ttl: 300,
      proxied: true,
    },
    {
      type: 'CNAME',
      name: 'widgets',
      content: 'synapseai.com',
      ttl: 300,
      proxied: true,
    },
  ],

  // Page Rules for Performance Optimization
  pageRules: [
    {
      targets: [
        {
          target: 'url',
          constraint: {
            operator: 'matches',
            value: 'synapseai.com/api/*',
          },
        },
      ],
      actions: [
        {
          id: 'cache_level',
          value: 'bypass',
        },
        {
          id: 'security_level',
          value: 'high',
        },
      ],
      priority: 1,
      status: 'active',
    },
    {
      targets: [
        {
          target: 'url',
          constraint: {
            operator: 'matches',
            value: 'synapseai.com/static/*',
          },
        },
      ],
      actions: [
        {
          id: 'cache_level',
          value: 'cache_everything',
        },
        {
          id: 'edge_cache_ttl',
          value: 2592000, // 30 days
        },
        {
          id: 'browser_cache_ttl',
          value: 86400, // 1 day
        },
      ],
      priority: 2,
      status: 'active',
    },
    {
      targets: [
        {
          target: 'url',
          constraint: {
            operator: 'matches',
            value: 'widgets.synapseai.com/*',
          },
        },
      ],
      actions: [
        {
          id: 'cache_level',
          value: 'cache_everything',
        },
        {
          id: 'edge_cache_ttl',
          value: 3600, // 1 hour
        },
        {
          id: 'security_level',
          value: 'medium',
        },
      ],
      priority: 3,
      status: 'active',
    },
  ],

  // Security Settings
  security: {
    // WAF Rules
    waf: {
      rules: [
        {
          description: 'Block malicious AI prompt injections',
          expression: '(http.request.body contains "ignore previous instructions") or (http.request.body contains "system prompt") or (http.request.body contains "jailbreak")',
          action: 'block',
        },
        {
          description: 'Rate limit API endpoints',
          expression: '(http.request.uri.path matches "^/api/") and (rate(1m) > 100)',
          action: 'challenge',
        },
        {
          description: 'Block suspicious user agents',
          expression: '(http.user_agent contains "bot") and not (http.user_agent contains "googlebot")',
          action: 'block',
        },
      ],
    },

    // DDoS Protection
    ddos: {
      enabled: true,
      sensitivity: 'high',
      action: 'challenge',
    },

    // Bot Management
    botManagement: {
      enabled: true,
      fightMode: true,
      staticResourceProtection: false,
      jsDetection: true,
    },

    // SSL/TLS Configuration
    ssl: {
      mode: 'strict',
      minTlsVersion: '1.2',
      ciphers: ['ECDHE-RSA-AES128-GCM-SHA256', 'ECDHE-RSA-AES256-GCM-SHA384'],
      hsts: {
        enabled: true,
        maxAge: 31536000,
        includeSubdomains: true,
        preload: true,
      },
    },
  },

  // Performance Optimization
  performance: {
    // Caching Rules
    caching: {
      cacheLevel: 'aggressive',
      browserCacheTtl: 86400, // 1 day
      edgeCacheTtl: 2592000, // 30 days
      alwaysOnline: true,
      developmentMode: false,
    },

    // Compression
    compression: {
      brotli: true,
      gzip: true,
      minify: {
        css: true,
        js: true,
        html: true,
      },
    },

    // Image Optimization
    imageOptimization: {
      polish: 'lossy',
      webp: true,
      avif: true,
      resizing: true,
    },

    // HTTP/3 and 0-RTT
    http3: true,
    zeroRtt: true,

    // Rocket Loader
    rocketLoader: false, // Disabled for AI applications

    // Mirage (Image optimization)
    mirage: true,
  },

  // Analytics and Monitoring
  analytics: {
    webAnalytics: true,
    logpush: {
      enabled: true,
      destination: 'https://logs.synapseai.com/cloudflare',
      fields: [
        'ClientIP',
        'ClientRequestHost',
        'ClientRequestMethod',
        'ClientRequestURI',
        'EdgeResponseStatus',
        'EdgeStartTimestamp',
        'EdgeEndTimestamp',
        'EdgeResponseBytes',
        'CacheResponseStatus',
        'SecurityLevel',
        'WAFAction',
        'WAFRuleID',
      ],
    },
  },

  // Load Balancing
  loadBalancing: {
    pools: [
      {
        name: 'synapseai-backend-pool',
        description: 'Backend API servers',
        enabled: true,
        minimumOrigins: 1,
        origins: [
          {
            name: 'backend-1',
            address: '*********',
            enabled: true,
            weight: 1,
          },
          {
            name: 'backend-2',
            address: '*********',
            enabled: true,
            weight: 1,
          },
        ],
        healthCheck: {
          enabled: true,
          method: 'GET',
          path: '/health',
          expectedCodes: '200',
          interval: 60,
          timeout: 5,
          retries: 2,
        },
      },
    ],
    loadBalancers: [
      {
        name: 'synapseai-api-lb',
        defaultPools: ['synapseai-backend-pool'],
        fallbackPool: 'synapseai-backend-pool',
        sessionAffinity: 'cookie',
        steeringPolicy: 'dynamic_latency',
      },
    ],
  },

  // Workers (Edge Computing)
  workers: [
    {
      name: 'widget-optimizer',
      script: `
        addEventListener('fetch', event => {
          event.respondWith(handleRequest(event.request))
        })

        async function handleRequest(request) {
          const url = new URL(request.url)
          
          // Optimize widget delivery
          if (url.pathname.startsWith('/widgets/')) {
            const response = await fetch(request)
            const headers = new Headers(response.headers)
            
            // Add widget-specific headers
            headers.set('Cache-Control', 'public, max-age=3600')
            headers.set('X-Widget-Optimized', 'true')
            
            return new Response(response.body, {
              status: response.status,
              statusText: response.statusText,
              headers: headers
            })
          }
          
          return fetch(request)
        }
      `,
      routes: [
        {
          pattern: 'widgets.synapseai.com/*',
          zone: 'synapseai.com',
        },
      ],
    },
  ],
};

module.exports = cloudflareConfig;
