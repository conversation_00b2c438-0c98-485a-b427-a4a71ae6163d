# joi

#### The most powerful schema description language and data validator for JavaScript.

## Installation
`npm install joi`

### Visit the [joi.dev](https://joi.dev) Developer Portal for tutorials, documentation, and support

## Useful resources

- [Documentation and API](https://joi.dev/api/)
- [Versions status](https://joi.dev/resources/status/#joi)
- [Changelog](https://joi.dev/resources/changelog/)
- [Project policies](https://joi.dev/policies/)
