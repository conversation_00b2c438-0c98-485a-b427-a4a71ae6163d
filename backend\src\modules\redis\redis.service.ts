import { Injectable, OnModuleInit, OnModule<PERSON><PERSON>roy, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis, { Cluster } from 'ioredis';

@Injectable()
export class RedisService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(RedisService.name);
  private client: Redis | Cluster;
  private pubClient: Redis | Cluster;
  private subClient: Redis | Cluster;

  constructor(private configService: ConfigService) {}

  async onModuleInit() {
    try {
      await this.initializeClients();
      this.logger.log('Successfully connected to Redis');
    } catch (error) {
      this.logger.error('Failed to connect to Redis:', error);
      throw error;
    }
  }

  async onModuleDestroy() {
    try {
      await Promise.all([
        this.client?.quit(),
        this.pubClient?.quit(),
        this.subClient?.quit(),
      ]);
      this.logger.log('Successfully disconnected from Redis');
    } catch (error) {
      this.logger.error('Error disconnecting from Redis:', error);
    }
  }

  private async initializeClients() {
    const redisConfig = {
      host: this.configService.get('REDIS_HOST', 'localhost'),
      port: this.configService.get('REDIS_PORT', 6379),
      password: this.configService.get('REDIS_PASSWORD'),
      db: 0,
      retryDelayOnFailover: 100,
      enableReadyCheck: true,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
      keepAlive: 30000,
      connectTimeout: 10000,
      commandTimeout: 5000,
    };

    // Check if clustering is enabled
    const clusterNodes = this.configService.get('REDIS_CLUSTER_NODES');
    
    if (clusterNodes) {
      // Initialize Redis Cluster
      const nodes = clusterNodes.split(',').map(node => {
        const [host, port] = node.split(':');
        return { host, port: parseInt(port) };
      });

      this.client = new Redis.Cluster(nodes, {
        redisOptions: redisConfig,
        enableOfflineQueue: false,
      });

      this.pubClient = new Redis.Cluster(nodes, {
        redisOptions: redisConfig,
        enableOfflineQueue: false,
      });

      this.subClient = new Redis.Cluster(nodes, {
        redisOptions: redisConfig,
        enableOfflineQueue: false,
      });
    } else {
      // Initialize single Redis instance
      this.client = new Redis(redisConfig);
      this.pubClient = new Redis(redisConfig);
      this.subClient = new Redis(redisConfig);
    }

    // Set up event listeners
    this.setupEventListeners();

    // Connect all clients
    await Promise.all([
      this.client.connect(),
      this.pubClient.connect(),
      this.subClient.connect(),
    ]);
  }

  private setupEventListeners() {
    const clients = [
      { client: this.client, name: 'main' },
      { client: this.pubClient, name: 'pub' },
      { client: this.subClient, name: 'sub' },
    ];

    clients.forEach(({ client, name }) => {
      client.on('connect', () => {
        this.logger.log(`Redis ${name} client connected`);
      });

      client.on('ready', () => {
        this.logger.log(`Redis ${name} client ready`);
      });

      client.on('error', (error) => {
        this.logger.error(`Redis ${name} client error:`, error);
      });

      client.on('close', () => {
        this.logger.warn(`Redis ${name} client connection closed`);
      });

      client.on('reconnecting', () => {
        this.logger.log(`Redis ${name} client reconnecting`);
      });
    });
  }

  /**
   * Get the main Redis client
   */
  getClient(): Redis | Cluster {
    return this.client;
  }

  /**
   * Get the pub/sub clients for real-time features
   */
  getPubSubClients(): { pub: Redis | Cluster; sub: Redis | Cluster } {
    return {
      pub: this.pubClient,
      sub: this.subClient,
    };
  }

  /**
   * Session management utilities
   */
  async createSession(organizationId: string, userId?: string, agentId?: string, context: any = {}) {
    const sessionId = `session:${organizationId}:${Date.now()}:${Math.random().toString(36).substr(2, 9)}`;
    const sessionData = {
      organizationId,
      userId,
      agentId,
      context,
      createdAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
    };

    await this.client.setex(sessionId, 24 * 60 * 60, JSON.stringify(sessionData));
    return sessionId;
  }

  async getSession(sessionId: string) {
    const data = await this.client.get(sessionId);
    return data ? JSON.parse(data) : null;
  }

  async updateSession(sessionId: string, context: any) {
    const session = await this.getSession(sessionId);
    if (session) {
      session.context = { ...session.context, ...context };
      session.updatedAt = new Date().toISOString();
      await this.client.setex(sessionId, 24 * 60 * 60, JSON.stringify(session));
    }
    return session;
  }

  async deleteSession(sessionId: string) {
    await this.client.del(sessionId);
  }

  /**
   * Cache utilities with namespace support
   */
  async set(key: string, value: any, ttl: number = 3600, namespace?: string) {
    const namespacedKey = namespace ? `${namespace}:${key}` : key;
    await this.client.setex(namespacedKey, ttl, JSON.stringify(value));
  }

  async get(key: string, namespace?: string) {
    const namespacedKey = namespace ? `${namespace}:${key}` : key;
    const data = await this.client.get(namespacedKey);
    return data ? JSON.parse(data) : null;
  }

  async del(key: string, namespace?: string) {
    const namespacedKey = namespace ? `${namespace}:${key}` : key;
    await this.client.del(namespacedKey);
  }

  async exists(key: string, namespace?: string) {
    const namespacedKey = namespace ? `${namespace}:${key}` : key;
    return await this.client.exists(namespacedKey);
  }

  /**
   * Health check for Redis connection
   */
  async isHealthy(): Promise<boolean> {
    try {
      const result = await this.client.ping();
      return result === 'PONG';
    } catch (error) {
      this.logger.error('Redis health check failed:', error);
      return false;
    }
  }

  /**
   * Pub/Sub utilities for real-time features
   */
  async publish(channel: string, message: any) {
    await this.pubClient.publish(channel, JSON.stringify(message));
  }

  async subscribe(channel: string, callback: (message: any) => void) {
    await this.subClient.subscribe(channel);
    this.subClient.on('message', (receivedChannel, message) => {
      if (receivedChannel === channel) {
        try {
          const parsedMessage = JSON.parse(message);
          callback(parsedMessage);
        } catch (error) {
          this.logger.error('Error parsing pub/sub message:', error);
        }
      }
    });
  }

  async unsubscribe(channel: string) {
    await this.subClient.unsubscribe(channel);
  }
}
