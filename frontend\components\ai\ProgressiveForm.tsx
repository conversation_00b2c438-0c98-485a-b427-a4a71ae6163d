'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { 
  smartDefaultsEngine, 
  ProgressiveDisclosureConfig, 
  ConfigurationLevel,
  SmartDefaultsContext 
} from '@/lib/ai/smart-defaults';
import { useAppStore } from '@/lib/store';
import {
  CheckCircleIcon,
  ClockIcon,
  ChevronRightIcon,
  ChevronLeftIcon,
  EyeIcon,
  EyeSlashIcon,
  SparklesIcon,
} from '@heroicons/react/24/outline';

interface ProgressiveFormProps {
  configType: 'agent' | 'tool' | 'workflow' | 'template';
  initialConfig?: Record<string, any>;
  context: SmartDefaultsContext;
  onConfigChange: (config: Record<string, any>) => void;
  onLevelComplete?: (level: ConfigurationLevel) => void;
  className?: string;
}

export function ProgressiveForm({
  configType,
  initialConfig = {},
  context,
  onConfigChange,
  onLevelComplete,
  className,
}: ProgressiveFormProps) {
  const [config, setConfig] = useState<Record<string, any>>(initialConfig);
  const [disclosure, setDisclosure] = useState<ProgressiveDisclosureConfig | null>(null);
  const [currentLevel, setCurrentLevel] = useState<ConfigurationLevel | null>(null);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [fieldRecommendations, setFieldRecommendations] = useState<Record<string, any[]>>({});

  const { addNotification } = useAppStore();

  useEffect(() => {
    initializeForm();
  }, [configType, context.userLevel]);

  useEffect(() => {
    onConfigChange(config);
  }, [config, onConfigChange]);

  const initializeForm = async () => {
    setIsLoading(true);
    try {
      // Get progressive disclosure configuration
      const newDisclosure = smartDefaultsEngine.getProgressiveDisclosure(
        configType,
        context.userLevel
      );
      setDisclosure(newDisclosure);

      // Get smart defaults
      const defaults = await smartDefaultsEngine.getSmartDefaults(context);
      const configWithDefaults = smartDefaultsEngine.applyDefaults(initialConfig, defaults);
      setConfig(configWithDefaults);

      // Set initial level
      const firstLevel = newDisclosure.levels[0];
      setCurrentLevel(firstLevel);

      addNotification({
        type: 'info',
        title: 'Smart Configuration Started',
        message: `Starting with ${firstLevel?.name || 'basic configuration'}`,
      });
    } catch (error) {
      console.error('Failed to initialize progressive form:', error);
      addNotification({
        type: 'error',
        title: 'Initialization Failed',
        message: 'Could not load smart configuration',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleFieldChange = async (field: string, value: any) => {
    const newConfig = { ...config };
    setNestedValue(newConfig, field, value);
    setConfig(newConfig);

    // Mark field as completed
    if (disclosure && currentLevel) {
      const validation = smartDefaultsEngine.validateFieldCompletion(field, value, currentLevel);
      if (validation.isComplete) {
        disclosure.completedFields.add(field);
      } else {
        disclosure.completedFields.delete(field);
      }
    }

    // Get field recommendations
    try {
      const recommendations = await smartDefaultsEngine.getFieldRecommendations(
        field,
        context,
        value
      );
      setFieldRecommendations(prev => ({ ...prev, [field]: recommendations }));
    } catch (error) {
      console.error('Failed to get field recommendations:', error);
    }
  };

  const handleNextLevel = () => {
    if (!disclosure || !currentLevel) return;

    // Validate current level completion
    const requiredFields = currentLevel.fields.filter(field => 
      currentLevel.importance === 'essential' || 
      (currentLevel.importance === 'recommended' && !disclosure.userPreferences.skipOptional)
    );

    const incompleteFields = requiredFields.filter(field => 
      !disclosure.completedFields.has(field) && !hasValue(config, field)
    );

    if (incompleteFields.length > 0) {
      addNotification({
        type: 'warning',
        title: 'Incomplete Fields',
        message: `Please complete: ${incompleteFields.join(', ')}`,
      });
      return;
    }

    // Move to next level
    const nextLevel = smartDefaultsEngine.getNextLevel(disclosure, config);
    if (nextLevel) {
      setCurrentLevel(nextLevel);
      disclosure.currentLevel++;
      onLevelComplete?.(currentLevel);
      
      addNotification({
        type: 'success',
        title: 'Level Complete',
        message: `Moving to ${nextLevel.name}`,
      });
    } else {
      addNotification({
        type: 'success',
        title: 'Configuration Complete',
        message: 'All configuration levels completed!',
      });
    }
  };

  const handlePreviousLevel = () => {
    if (!disclosure || disclosure.currentLevel === 0) return;

    disclosure.currentLevel--;
    const prevLevel = disclosure.levels[disclosure.currentLevel];
    setCurrentLevel(prevLevel);
  };

  const handleSkipLevel = () => {
    if (!disclosure || !currentLevel) return;

    // Mark level as skipped
    currentLevel.fields.forEach(field => {
      disclosure.skippedFields.add(field);
    });

    handleNextLevel();
  };

  const renderField = (field: string) => {
    const value = getNestedValue(config, field);
    const recommendations = fieldRecommendations[field] || [];
    const isCompleted = disclosure?.completedFields.has(field) || hasValue(config, field);

    return (
      <div key={field} className="space-y-2">
        <div className="flex items-center space-x-2">
          <Input
            label={formatFieldLabel(field)}
            value={value || ''}
            onChange={(e) => handleFieldChange(field, e.target.value)}
            placeholder={getFieldPlaceholder(field)}
            className={isCompleted ? 'border-success-500' : ''}
            rightIcon={
              isCompleted ? (
                <CheckCircleIcon className="h-4 w-4 text-success-500" />
              ) : undefined
            }
          />
        </div>

        {/* Field Recommendations */}
        {recommendations.length > 0 && (
          <div className="space-y-1">
            <div className="text-xs font-medium text-gray-700">Suggestions:</div>
            <div className="flex flex-wrap gap-1">
              {recommendations.slice(0, 3).map((rec, index) => (
                <button
                  key={index}
                  onClick={() => handleFieldChange(field, rec.value)}
                  className="text-xs px-2 py-1 bg-blue-50 text-blue-700 rounded hover:bg-blue-100"
                  title={rec.description}
                >
                  {rec.label}
                  {rec.isDefault && <SparklesIcon className="inline h-3 w-3 ml-1" />}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  const getProgressPercentage = () => {
    if (!disclosure) return 0;
    return Math.round((disclosure.currentLevel / disclosure.levels.length) * 100);
  };

  const getEstimatedTimeRemaining = () => {
    if (!disclosure) return '0 minutes';
    
    const remainingLevels = disclosure.levels.slice(disclosure.currentLevel);
    const totalMinutes = remainingLevels.reduce((sum, level) => {
      const minutes = parseInt(level.estimatedTime.split(' ')[0]);
      return sum + minutes;
    }, 0);
    
    return `${totalMinutes} minutes`;
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600" />
          <span className="ml-2">Loading smart configuration...</span>
        </CardContent>
      </Card>
    );
  }

  if (!disclosure || !currentLevel) {
    return (
      <Card className={className}>
        <CardContent className="text-center py-8">
          <p className="text-gray-500">Configuration not available</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <SparklesIcon className="h-5 w-5 text-primary-600" />
            <span>{currentLevel.name}</span>
            <Badge variant="info" size="sm">
              Level {disclosure.currentLevel + 1} of {disclosure.levels.length}
            </Badge>
          </CardTitle>
          
          <Button
            size="sm"
            variant="ghost"
            onClick={() => setShowAdvanced(!showAdvanced)}
            leftIcon={showAdvanced ? <EyeSlashIcon className="h-4 w-4" /> : <EyeIcon className="h-4 w-4" />}
          >
            {showAdvanced ? 'Hide' : 'Show'} Advanced
          </Button>
        </div>

        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>{getProgressPercentage()}% complete</span>
            <div className="flex items-center space-x-1">
              <ClockIcon className="h-4 w-4" />
              <span>{getEstimatedTimeRemaining()} remaining</span>
            </div>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-primary-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${getProgressPercentage()}%` }}
            />
          </div>
        </div>

        <p className="text-sm text-gray-600">{currentLevel.description}</p>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Current Level Fields */}
        <div className="space-y-4">
          {currentLevel.fields.map(field => renderField(field))}
        </div>

        {/* Advanced Fields */}
        {showAdvanced && disclosure.currentLevel < disclosure.levels.length - 1 && (
          <div className="border-t pt-4">
            <h4 className="font-medium text-sm mb-3 text-gray-700">Advanced Options</h4>
            <div className="space-y-4">
              {disclosure.levels
                .slice(disclosure.currentLevel + 1)
                .flatMap(level => level.fields)
                .slice(0, 3)
                .map(field => renderField(field))}
            </div>
          </div>
        )}

        {/* Navigation */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={handlePreviousLevel}
              disabled={disclosure.currentLevel === 0}
              leftIcon={<ChevronLeftIcon className="h-4 w-4" />}
            >
              Previous
            </Button>
            
            {currentLevel.importance !== 'essential' && (
              <Button
                variant="ghost"
                onClick={handleSkipLevel}
              >
                Skip Level
              </Button>
            )}
          </div>

          <div className="flex space-x-2">
            <Button
              onClick={handleNextLevel}
              rightIcon={<ChevronRightIcon className="h-4 w-4" />}
            >
              {disclosure.currentLevel === disclosure.levels.length - 1 ? 'Complete' : 'Next Level'}
            </Button>
          </div>
        </div>

        {/* Level Overview */}
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-xs font-medium text-gray-700 mb-2">Configuration Levels</div>
          <div className="flex space-x-2">
            {disclosure.levels.map((level, index) => (
              <div
                key={level.id}
                className={`flex-1 h-2 rounded ${
                  index < disclosure.currentLevel
                    ? 'bg-success-500'
                    : index === disclosure.currentLevel
                    ? 'bg-primary-500'
                    : 'bg-gray-200'
                }`}
                title={level.name}
              />
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Utility functions
function setNestedValue(obj: any, path: string, value: any): void {
  const keys = path.split('.');
  let current = obj;
  
  for (let i = 0; i < keys.length - 1; i++) {
    if (!(keys[i] in current)) {
      current[keys[i]] = {};
    }
    current = current[keys[i]];
  }
  
  current[keys[keys.length - 1]] = value;
}

function getNestedValue(obj: any, path: string): any {
  const keys = path.split('.');
  let current = obj;
  
  for (const key of keys) {
    if (current == null || !(key in current)) {
      return undefined;
    }
    current = current[key];
  }
  
  return current;
}

function hasValue(obj: any, path: string): boolean {
  const value = getNestedValue(obj, path);
  return value !== undefined && value !== null && value !== '';
}

function formatFieldLabel(field: string): string {
  return field
    .split(/(?=[A-Z])/)
    .join(' ')
    .replace(/^\w/, c => c.toUpperCase());
}

function getFieldPlaceholder(field: string): string {
  const placeholders: Record<string, string> = {
    name: 'Enter a descriptive name',
    description: 'Describe what this does',
    systemPrompt: 'Define the AI behavior and personality',
    model: 'Select AI model',
    temperature: 'Creativity level (0-1)',
    maxTokens: 'Maximum response length',
  };
  
  return placeholders[field] || `Enter ${formatFieldLabel(field).toLowerCase()}`;
}
