import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy as OpenIDConnectStrategy } from 'passport-openidconnect';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class OidcStrategy extends PassportStrategy(OpenIDConnectStrategy, 'oidc') {
  constructor(private configService: ConfigService) {
    super({
      issuer: configService.get('OIDC_ISSUER'),
      clientID: configService.get('OIDC_CLIENT_ID'),
      clientSecret: configService.get('OIDC_CLIENT_SECRET'),
      callbackURL: configService.get('OIDC_CALLBACK_URL'),
      scope: ['openid', 'profile', 'email'],
    });
  }

  async validate(
    issuer: string,
    profile: any,
    context: any,
    idToken: any,
    accessToken: string,
    refreshToken: string,
    done: any,
  ): Promise<any> {
    // Extract user information from OIDC profile
    const user = {
      email: profile.emails?.[0]?.value || profile.email,
      firstName: profile.name?.givenName || profile.given_name,
      lastName: profile.name?.familyName || profile.family_name,
      groups: profile.groups || [],
      attributes: {
        ...profile,
        idToken,
        accessToken,
      },
    };

    return user;
  }
}
