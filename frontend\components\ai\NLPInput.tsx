'use client';

import { useState, useRef, useEffect } from 'react';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { nlpProcessor, IntentAnalysis, NLPContext } from '@/lib/ai/nlp-processor';
import { useAppStore } from '@/lib/store';
import {
  MicrophoneIcon,
  SparklesIcon,
  CheckIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';

interface NLPInputProps {
  context?: NLPContext;
  onIntentDetected?: (analysis: IntentAnalysis) => void;
  onActionExecuted?: (action: any) => void;
  placeholder?: string;
  className?: string;
  showSuggestions?: boolean;
  autoExecute?: boolean;
}

export function NLPInput({
  context = {},
  onIntentDetected,
  onActionExecuted,
  placeholder = "Tell me what you want to do...",
  className,
  showSuggestions = true,
  autoExecute = false,
}: NLPInputProps) {
  const [input, setInput] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [currentAnalysis, setCurrentAnalysis] = useState<IntentAnalysis | null>(null);
  const [suggestions, setSuggestions] = useState<any[]>([]);
  const [showSuggestionDropdown, setShowSuggestionDropdown] = useState(false);
  const [isListening, setIsListening] = useState(false);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const { addNotification } = useAppStore();

  // Voice recognition setup
  const recognition = useRef<SpeechRecognition | null>(null);

  useEffect(() => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognition.current = new SpeechRecognition();
      recognition.current.continuous = false;
      recognition.current.interimResults = false;
      recognition.current.lang = 'en-US';

      recognition.current.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        setInput(transcript);
        handleAnalyze(transcript);
        setIsListening(false);
      };

      recognition.current.onerror = () => {
        setIsListening(false);
        addNotification({
          type: 'error',
          title: 'Voice Recognition Error',
          message: 'Could not process voice input. Please try again.',
        });
      };

      recognition.current.onend = () => {
        setIsListening(false);
      };
    }
  }, [addNotification]);

  // Get suggestions as user types
  useEffect(() => {
    if (input.length > 2 && showSuggestions) {
      const timeoutId = setTimeout(async () => {
        try {
          const suggestions = await nlpProcessor.getSuggestions(input, context);
          setSuggestions(suggestions);
          setShowSuggestionDropdown(suggestions.length > 0);
        } catch (error) {
          console.error('Failed to get suggestions:', error);
        }
      }, 300);

      return () => clearTimeout(timeoutId);
    } else {
      setSuggestions([]);
      setShowSuggestionDropdown(false);
    }
  }, [input, context, showSuggestions]);

  const handleAnalyze = async (inputText: string = input) => {
    if (!inputText.trim()) return;

    setIsAnalyzing(true);
    try {
      const analysis = await nlpProcessor.analyzeIntent(inputText, context);
      setCurrentAnalysis(analysis);
      onIntentDetected?.(analysis);

      // Validate intent
      const validation = nlpProcessor.validateIntent(analysis, context);
      if (!validation.isValid) {
        addNotification({
          type: 'warning',
          title: 'Intent Validation',
          message: validation.issues[0] || 'Could not understand the request',
        });
        return;
      }

      // Auto-execute if enabled and confidence is high
      if (autoExecute && analysis.confidence > 0.8) {
        const actions = nlpProcessor.intentToActions(analysis);
        if (actions.length > 0) {
          await executeAction(actions[0]);
        }
      }

      addNotification({
        type: 'success',
        title: 'Intent Detected',
        message: `Understood: ${analysis.intent} (${Math.round(analysis.confidence * 100)}% confidence)`,
      });
    } catch (error) {
      console.error('Failed to analyze intent:', error);
      addNotification({
        type: 'error',
        title: 'Analysis Failed',
        message: 'Could not analyze your request. Please try again.',
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  const executeAction = async (action: any) => {
    try {
      onActionExecuted?.(action);
      
      addNotification({
        type: 'success',
        title: 'Action Executed',
        message: action.description,
      });
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Action Failed',
        message: 'Could not execute the requested action.',
      });
    }
  };

  const handleSuggestionSelect = (suggestion: any) => {
    setInput(suggestion.completion);
    setShowSuggestionDropdown(false);
    handleAnalyze(suggestion.completion);
  };

  const handleVoiceInput = () => {
    if (!recognition.current) {
      addNotification({
        type: 'error',
        title: 'Voice Input Unavailable',
        message: 'Voice recognition is not supported in this browser.',
      });
      return;
    }

    if (isListening) {
      recognition.current.stop();
      setIsListening(false);
    } else {
      recognition.current.start();
      setIsListening(true);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleAnalyze();
    }
  };

  const clearAnalysis = () => {
    setCurrentAnalysis(null);
    setInput('');
    inputRef.current?.focus();
  };

  return (
    <div className={`relative ${className}`}>
      {/* Main Input */}
      <div className="relative">
        <Input
          ref={inputRef}
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder={placeholder}
          className="pr-24"
          leftIcon={<SparklesIcon className="h-4 w-4 text-primary-500" />}
          rightIcon={
            <div className="flex items-center space-x-1">
              {recognition.current && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={handleVoiceInput}
                  className={`p-1 ${isListening ? 'text-error-500' : 'text-gray-400'}`}
                >
                  <MicrophoneIcon className="h-4 w-4" />
                </Button>
              )}
              <Button
                size="sm"
                onClick={() => handleAnalyze()}
                loading={isAnalyzing}
                disabled={!input.trim() || isAnalyzing}
              >
                Analyze
              </Button>
            </div>
          }
        />

        {/* Suggestions Dropdown */}
        {showSuggestionDropdown && suggestions.length > 0 && (
          <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-50 max-h-60 overflow-y-auto">
            {suggestions.map((suggestion, index) => (
              <button
                key={index}
                className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center justify-between"
                onClick={() => handleSuggestionSelect(suggestion)}
              >
                <div>
                  <div className="font-medium text-sm">{suggestion.completion}</div>
                  <div className="text-xs text-gray-500">{suggestion.description}</div>
                </div>
                <Badge variant="secondary" size="sm">
                  {Math.round(suggestion.confidence * 100)}%
                </Badge>
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Analysis Results */}
      {currentAnalysis && (
        <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-2">
                <h4 className="font-medium text-blue-900">Intent Detected</h4>
                <Badge variant="info" size="sm">
                  {currentAnalysis.intent}
                </Badge>
                <Badge variant="secondary" size="sm">
                  {Math.round(currentAnalysis.confidence * 100)}% confidence
                </Badge>
              </div>

              {/* Entities */}
              {currentAnalysis.entities.length > 0 && (
                <div className="mb-3">
                  <div className="text-sm text-blue-700 mb-1">Detected entities:</div>
                  <div className="flex flex-wrap gap-1">
                    {currentAnalysis.entities.map((entity, index) => (
                      <Badge key={index} variant="outline" size="sm">
                        {entity.type}: {entity.value}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Suggested Actions */}
              {currentAnalysis.suggestedActions.length > 0 && (
                <div className="space-y-2">
                  <div className="text-sm text-blue-700">Suggested actions:</div>
                  {currentAnalysis.suggestedActions.map((action, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm">{action.description}</span>
                      <Button
                        size="sm"
                        onClick={() => executeAction(action)}
                        leftIcon={<CheckIcon className="h-3 w-3" />}
                      >
                        Execute
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <Button
              size="sm"
              variant="ghost"
              onClick={clearAnalysis}
              leftIcon={<XMarkIcon className="h-3 w-3" />}
            >
              Clear
            </Button>
          </div>
        </div>
      )}

      {/* Voice Input Indicator */}
      {isListening && (
        <div className="mt-2 flex items-center space-x-2 text-sm text-blue-600">
          <div className="w-2 h-2 bg-error-500 rounded-full animate-pulse" />
          <span>Listening...</span>
        </div>
      )}
    </div>
  );
}
