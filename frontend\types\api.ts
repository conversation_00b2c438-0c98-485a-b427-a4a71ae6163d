// Base API response interface
export interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  message?: string;
  error?: string;
  timestamp?: string;
}

export interface ApiError {
  message: string;
  error?: string;
  statusCode: number;
  timestamp: string;
  path?: string;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  search?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface HealthCheck {
  status: 'ok' | 'error';
  timestamp: string;
  uptime: number;
  version: string;
  services: {
    database: 'healthy' | 'unhealthy';
    redis: 'healthy' | 'unhealthy';
    [key: string]: 'healthy' | 'unhealthy';
  };
}

export interface RequestConfig {
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  headers?: Record<string, string>;
}

export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

export interface ApiEndpoint {
  method: HttpMethod;
  path: string;
  authenticated?: boolean;
  roles?: string[];
  rateLimit?: {
    requests: number;
    window: number; // in seconds
  };
}

// Specific API response types
export interface AuthResponse {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: string;
    organizationId: string;
    organizationName: string;
    organizationRole: string;
    permissions: string[];
    avatar?: string;
    emailVerified: boolean;
    twoFactorEnabled: boolean;
    lastLoginAt: string;
    createdAt: string;
  };
  accessToken: string;
  refreshToken: string;
}

export interface ValidationResponse {
  isValid: boolean;
  errors: Array<{
    field: string;
    message: string;
    code: string;
  }>;
}

export interface ExecutionResponse {
  executionId: string;
  status: 'running' | 'completed' | 'error' | 'idle';
  currentNodeId?: string;
  results?: Record<string, any>;
  newErrors?: Array<{
    nodeId: string;
    message: string;
    timestamp: number;
  }>;
  newLogs?: Array<{
    level: 'info' | 'warn' | 'error';
    message: string;
    timestamp: number;
    nodeId?: string;
  }>;
}

export interface RecommendationsResponse {
  recommendations: Array<{
    field: string;
    value: any;
    confidence: number;
    reason: string;
  }>;
}

export interface TemplatesResponse {
  templates: Array<{
    id: string;
    name: string;
    description: string;
    config: Record<string, any>;
  }>;
}

export interface DefaultsResponse {
  defaults: Record<string, any>;
}

export interface HelpSearchResponse {
  results: Array<{
    id: string;
    title: string;
    content: string;
    type: 'explanation' | 'tutorial' | 'troubleshooting' | 'reference' | 'tip';
    priority: 'low' | 'medium' | 'high' | 'critical';
    format: 'text' | 'markdown' | 'video' | 'interactive';
    tags: string[];
    confidence: number;
    source: 'ai' | 'documentation' | 'community' | 'support';
  }>;
}

export interface PersonalizedTipsResponse {
  tips: Array<{
    id: string;
    title: string;
    content: string;
    type: 'tip';
    priority: 'low' | 'medium' | 'high' | 'critical';
    format: 'text' | 'markdown' | 'video' | 'interactive';
    tags: string[];
    confidence: number;
    source: 'ai' | 'documentation' | 'community' | 'support';
  }>;
}

export interface IntentAnalysisResponse {
  intent: string;
  confidence: number;
  entities: Array<{
    type: string;
    value: string;
    confidence: number;
    start: number;
    end: number;
  }>;
  context: {
    domain: string;
    action: string;
    target?: string;
    parameters?: Record<string, any>;
  };
  suggestions: string[];
  alternatives: Array<{
    intent: string;
    confidence: number;
  }>;
}

export interface AgentTestResponse {
  success: boolean;
  output?: string;
  error?: string;
}

export interface AgentCreateResponse {
  id: string;
  name: string;
  description: string;
  config: Record<string, any>;
}

export interface TokenRefreshResponse {
  accessToken: string;
  refreshToken?: string;
}
