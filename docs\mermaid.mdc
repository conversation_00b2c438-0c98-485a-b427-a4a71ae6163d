graph TD
    A[User Accesses SynapseAI Platform] --> B[Dashboard Home]
    
    %% Main Navigation Options
    B --> C1[Agents Section]
    B --> C2[Tools Section]
    B --> C3[Workflows Section]
    B --> C4[Knowledge Base Section]
    B --> C5[Analytics Section]
    B --> C6[Settings Section]
    
    %% Dashboard Overview Cards
    B --> D1[View Usage Statistics]
    B --> D2[Check Recent Activities]
    B --> D3[Quick-Access Buttons]
    
    %% Quick Access Actions
    D3 --> E1[Create New Agent]
    D3 --> E2[Create New Tool]
    
    %% Activity Feed
    B --> F[Access Real-time Activity Feed]
    F --> F1[View Agent Executions]
    F --> F2[View Workflow Completions]
    F --> F3[View System Notifications]
    F --> F4[Apply Filters to Feed]
    
    %% Resource Management
    B --> G[Resource Management Panel]
    G --> G1[Check Quota Usage]
    G --> G2[View Billing Information]
    G --> G3[Monitor Performance Metrics]
    G --> G4[Explore Visual Charts]
    
    %% Customization Options
    B --> H[Customization Options]
    H --> H1[Toggle Dark/Light Mode]
    H --> H2[Customize Organization Branding]
    
    %% Section Details
    C1 --> C1a[Create Agent]
    C1 --> C1b[View Agent List]
    C1 --> C1c[Edit Existing Agent]
    C1 --> C1d[Monitor Agent Performance]
    
    C2 --> C2a[Create Tool]
    C2 --> C2b[View Tool List]
    C2 --> C2c[Edit Existing Tool]
    C2 --> C2d[Test Tool Integration]
    
    C3 --> C3a[Create Workflow]
    C3 --> C3b[View Workflow List]
    C3 --> C3c[Edit Existing Workflow]
    C3 --> C3d[Run Workflow]
    
    C4 --> C4a[Add Knowledge Source]
    C4 --> C4b[View Knowledge Items]
    C4 --> C4c[Update Knowledge Base]
    C4 --> C4d[Search Knowledge Base]
    
    C5 --> C5a[View Performance Analytics]
    C5 --> C5b[Generate Reports]
    C5 --> C5c[Set Custom Metrics]
    C5 --> C5d[Export Analytics Data]
    
    C6 --> C6a[Manage User Preferences]
    C6 --> C6b[Configure API Keys]
    C6 --> C6c[Manage Team Access]
    C6 --> C6d[Update Billing Information]