version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: synapseai-postgres-dev
    environment:
      POSTGRES_DB: synapseai_dev
      POSTGRES_USER: synapseai
      POSTGRES_PASSWORD: synapseai_dev_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - synapseai-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U synapseai -d synapseai_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache and Session Store
  redis:
    image: redis:7-alpine
    container_name: synapseai-redis-dev
    command: redis-server --appendonly yes --requirepass synapseai_redis_password
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - synapseai-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # NESTJS Backend
  backend:
    build:
      context: .
      dockerfile: backend/Dockerfile
      target: dev
    container_name: synapseai-backend-dev
    environment:
      NODE_ENV: development
      PORT: 3001
      DATABASE_URL: ***********************************************************/synapseai_dev
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: synapseai_redis_password
      JWT_SECRET: synapseai_jwt_secret_dev
      CORS_ORIGINS: http://localhost:3000,http://localhost:3001
    ports:
      - "3001:3001"
    volumes:
      - ./backend:/app/backend
      - ./shared:/app/shared
      - /app/backend/node_modules
      - /app/shared/node_modules
    networks:
      - synapseai-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3001/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Next.js Frontend (will be added in subsequent tasks)
  # frontend:
  #   build:
  #     context: .
  #     dockerfile: frontend/Dockerfile
  #     target: dev
  #   container_name: synapseai-frontend-dev
  #   environment:
  #     NODE_ENV: development
  #     NEXT_PUBLIC_API_URL: http://localhost:3001
  #   ports:
  #     - "3000:3000"
  #   volumes:
  #     - ./frontend:/app/frontend
  #     - ./shared:/app/shared
  #     - /app/frontend/node_modules
  #     - /app/shared/node_modules
  #   networks:
  #     - synapseai-network
  #   depends_on:
  #     - backend

  # Message Queue UI (Bull Dashboard)
  bull-dashboard:
    image: deadly0/bull-board
    container_name: synapseai-bull-dashboard-dev
    environment:
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: synapseai_redis_password
    ports:
      - "3002:3000"
    networks:
      - synapseai-network
    depends_on:
      redis:
        condition: service_healthy

  # Redis Commander (Redis GUI)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: synapseai-redis-commander-dev
    environment:
      REDIS_HOSTS: local:redis:6379:0:synapseai_redis_password
    ports:
      - "8081:8081"
    networks:
      - synapseai-network
    depends_on:
      redis:
        condition: service_healthy

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local

networks:
  synapseai-network:
    driver: bridge
