"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var SentryService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SentryService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const Sentry = require("@sentry/node");
const profiling_node_1 = require("@sentry/profiling-node");
let SentryService = SentryService_1 = class SentryService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(SentryService_1.name);
        this.initialized = false;
    }
    init() {
        const dsn = this.configService.get('SENTRY_DSN');
        const environment = this.configService.get('NODE_ENV', 'development');
        const release = this.configService.get('APP_VERSION', '1.0.0');
        if (!dsn) {
            this.logger.warn('Sentry DSN not configured, skipping initialization');
            return;
        }
        try {
            Sentry.init({
                dsn,
                environment,
                release: `synapseai@${release}`,
                tracesSampleRate: environment === 'production' ? 0.1 : 1.0,
                profilesSampleRate: environment === 'production' ? 0.1 : 1.0,
                integrations: [
                    new profiling_node_1.ProfilingIntegration(),
                    new Sentry.Integrations.Http({ tracing: true }),
                    new Sentry.Integrations.Express({ app: undefined }),
                    new Sentry.Integrations.Console(),
                    new Sentry.Integrations.OnUncaughtException({
                        exitEvenIfOtherHandlersAreRegistered: false,
                    }),
                    new Sentry.Integrations.OnUnhandledRejection({
                        mode: 'warn',
                    }),
                ],
                beforeSend(event, hint) {
                    const error = hint.originalException;
                    if (error instanceof Error) {
                        if (environment === 'development' && error.message.includes('connect ECONNREFUSED')) {
                            return null;
                        }
                        if (environment === 'development' && error.message.includes('Redis connection')) {
                            return null;
                        }
                        if (error.message.includes('ValidationError')) {
                            return null;
                        }
                    }
                    return event;
                },
                beforeSendTransaction(event) {
                    if (event.transaction === 'GET /health') {
                        return null;
                    }
                    if (environment === 'production' && event.start_timestamp && event.timestamp) {
                        const duration = (event.timestamp - event.start_timestamp) * 1000;
                        if (duration < 100) {
                            return null;
                        }
                    }
                    return event;
                },
                maxBreadcrumbs: 50,
                attachStacktrace: true,
                sendDefaultPii: false,
                initialScope: {
                    tags: {
                        component: 'backend',
                        service: 'synapseai',
                    },
                },
            });
            this.initialized = true;
            this.logger.log('Sentry initialized successfully');
        }
        catch (error) {
            this.logger.error('Failed to initialize Sentry:', error);
        }
    }
    captureException(error, context) {
        if (!this.initialized) {
            this.logger.error('Sentry not initialized, cannot capture exception');
            return;
        }
        return Sentry.withScope((scope) => {
            if (context?.user) {
                scope.setUser({
                    id: context.user.id,
                    email: context.user.email,
                });
                if (context.user.organizationId) {
                    scope.setTag('organizationId', context.user.organizationId);
                }
            }
            if (context?.tags) {
                Object.entries(context.tags).forEach(([key, value]) => {
                    scope.setTag(key, value);
                });
            }
            if (context?.extra) {
                Object.entries(context.extra).forEach(([key, value]) => {
                    scope.setExtra(key, value);
                });
            }
            if (context?.level) {
                scope.setLevel(context.level);
            }
            return Sentry.captureException(error);
        });
    }
    captureMessage(message, level = 'info', context) {
        if (!this.initialized) {
            this.logger.warn('Sentry not initialized, cannot capture message');
            return;
        }
        return Sentry.withScope((scope) => {
            if (context?.user) {
                scope.setUser({
                    id: context.user.id,
                    email: context.user.email,
                });
                if (context.user.organizationId) {
                    scope.setTag('organizationId', context.user.organizationId);
                }
            }
            if (context?.tags) {
                Object.entries(context.tags).forEach(([key, value]) => {
                    scope.setTag(key, value);
                });
            }
            if (context?.extra) {
                Object.entries(context.extra).forEach(([key, value]) => {
                    scope.setExtra(key, value);
                });
            }
            scope.setLevel(level);
            return Sentry.captureMessage(message);
        });
    }
    startTransaction(name, op, description) {
        if (!this.initialized) {
            return;
        }
        return Sentry.startTransaction({
            name,
            op,
            description,
        });
    }
    addBreadcrumb(message, category, level = 'info', data) {
        if (!this.initialized) {
            return;
        }
        Sentry.addBreadcrumb({
            message,
            category,
            level,
            data,
            timestamp: Date.now() / 1000,
        });
    }
    setUser(user) {
        if (!this.initialized) {
            return;
        }
        Sentry.setUser({
            id: user.id,
            email: user.email,
        });
        if (user.organizationId) {
            Sentry.setTag('organizationId', user.organizationId);
        }
        if (user.role) {
            Sentry.setTag('userRole', user.role);
        }
    }
    setTags(tags) {
        if (!this.initialized) {
            return;
        }
        Sentry.setTags(tags);
    }
    setContext(key, context) {
        if (!this.initialized) {
            return;
        }
        Sentry.setContext(key, context);
    }
    async flush(timeout = 2000) {
        if (!this.initialized) {
            return true;
        }
        try {
            return await Sentry.flush(timeout);
        }
        catch (error) {
            this.logger.error('Failed to flush Sentry events:', error);
            return false;
        }
    }
    async close(timeout = 2000) {
        if (!this.initialized) {
            return true;
        }
        try {
            return await Sentry.close(timeout);
        }
        catch (error) {
            this.logger.error('Failed to close Sentry client:', error);
            return false;
        }
    }
    isInitialized() {
        return this.initialized;
    }
};
exports.SentryService = SentryService;
exports.SentryService = SentryService = SentryService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], SentryService);
//# sourceMappingURL=sentry.service.js.map