'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { CanvasState, CanvasNode } from '@/lib/visual-builder/canvas';
import { apiClient } from '@/lib/api';
import { useAppStore } from '@/lib/store';
import {
  PlayIcon,
  PauseIcon,
  StopIcon,
  ArrowPathIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';

interface PreviewExecution {
  id: string;
  status: 'idle' | 'running' | 'paused' | 'completed' | 'error';
  currentNodeId?: string;
  startTime?: number;
  endTime?: number;
  results: Record<string, any>;
  errors: Array<{
    nodeId: string;
    message: string;
    timestamp: number;
  }>;
  logs: Array<{
    nodeId: string;
    level: 'info' | 'warn' | 'error';
    message: string;
    timestamp: number;
    data?: any;
  }>;
}

interface RealTimePreviewProps {
  canvasState: CanvasState;
  selectedNodes: CanvasNode[];
  onNodeHighlight?: (nodeId: string | null) => void;
  className?: string;
}

export function RealTimePreview({
  canvasState,
  selectedNodes,
  onNodeHighlight,
  className,
}: RealTimePreviewProps) {
  const [execution, setExecution] = useState<PreviewExecution>({
    id: '',
    status: 'idle',
    results: {},
    errors: [],
    logs: [],
  });
  const [isValidWorkflow, setIsValidWorkflow] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [previewMode, setPreviewMode] = useState<'full' | 'selected' | 'step'>('full');
  const [autoRefresh, setAutoRefresh] = useState(true);

  const executionRef = useRef<PreviewExecution>(execution);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const { addNotification } = useAppStore();

  // Update ref when execution changes
  useEffect(() => {
    executionRef.current = execution;
  }, [execution]);

  // Validate workflow when canvas state changes
  useEffect(() => {
    validateWorkflow();
  }, [canvasState]);

  // Auto-refresh execution status
  useEffect(() => {
    if (autoRefresh && execution.status === 'running') {
      intervalRef.current = setInterval(() => {
        refreshExecutionStatus();
      }, 1000);
    } else if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [autoRefresh, execution.status]);

  const validateWorkflow = async () => {
    try {
      const response = await apiClient.post('/api/v1/workflows/validate', {
        nodes: canvasState.nodes,
        connections: canvasState.connections,
      });

      setIsValidWorkflow(response.data.isValid);
      setValidationErrors(response.data.errors || []);
    } catch (error) {
      console.error('Workflow validation failed:', error);
      setIsValidWorkflow(false);
      setValidationErrors(['Failed to validate workflow']);
    }
  };

  const startExecution = async () => {
    if (!isValidWorkflow) {
      addNotification({
        type: 'error',
        title: 'Invalid Workflow',
        message: 'Please fix validation errors before running',
      });
      return;
    }

    try {
      const nodesToExecute = previewMode === 'selected' && selectedNodes.length > 0
        ? selectedNodes.map(n => n.id)
        : canvasState.nodes.map(n => n.id);

      const response = await apiClient.post('/api/v1/workflows/execute', {
        nodes: canvasState.nodes,
        connections: canvasState.connections,
        nodeIds: nodesToExecute,
        mode: previewMode,
      });

      setExecution({
        id: response.data.executionId,
        status: 'running',
        currentNodeId: undefined,
        startTime: Date.now(),
        results: {},
        errors: [],
        logs: [],
      });

      addNotification({
        type: 'success',
        title: 'Execution Started',
        message: 'Workflow execution has begun',
      });
    } catch (error: any) {
      addNotification({
        type: 'error',
        title: 'Execution Failed',
        message: error.message || 'Failed to start workflow execution',
      });
    }
  };

  const pauseExecution = async () => {
    if (!execution.id) return;

    try {
      await apiClient.post(`/api/v1/workflows/executions/${execution.id}/pause`);
      setExecution(prev => ({ ...prev, status: 'paused' }));
      
      addNotification({
        type: 'info',
        title: 'Execution Paused',
        message: 'Workflow execution has been paused',
      });
    } catch (error: any) {
      addNotification({
        type: 'error',
        title: 'Pause Failed',
        message: error.message || 'Failed to pause execution',
      });
    }
  };

  const resumeExecution = async () => {
    if (!execution.id) return;

    try {
      await apiClient.post(`/api/v1/workflows/executions/${execution.id}/resume`);
      setExecution(prev => ({ ...prev, status: 'running' }));
      
      addNotification({
        type: 'success',
        title: 'Execution Resumed',
        message: 'Workflow execution has been resumed',
      });
    } catch (error: any) {
      addNotification({
        type: 'error',
        title: 'Resume Failed',
        message: error.message || 'Failed to resume execution',
      });
    }
  };

  const stopExecution = async () => {
    if (!execution.id) return;

    try {
      await apiClient.post(`/api/v1/workflows/executions/${execution.id}/stop`);
      setExecution(prev => ({ 
        ...prev, 
        status: 'idle',
        currentNodeId: undefined,
        endTime: Date.now(),
      }));
      
      onNodeHighlight?.(null);
      
      addNotification({
        type: 'info',
        title: 'Execution Stopped',
        message: 'Workflow execution has been stopped',
      });
    } catch (error: any) {
      addNotification({
        type: 'error',
        title: 'Stop Failed',
        message: error.message || 'Failed to stop execution',
      });
    }
  };

  const refreshExecutionStatus = async () => {
    if (!execution.id) return;

    try {
      const response = await apiClient.get(`/api/v1/workflows/executions/${execution.id}/status`);
      const status = response.data;

      setExecution(prev => ({
        ...prev,
        status: status.status,
        currentNodeId: status.currentNodeId,
        results: { ...prev.results, ...status.results },
        errors: [...prev.errors, ...status.newErrors],
        logs: [...prev.logs, ...status.newLogs],
        endTime: status.status === 'completed' || status.status === 'error' ? Date.now() : prev.endTime,
      }));

      // Highlight current node
      onNodeHighlight?.(status.currentNodeId || null);

      // Handle completion
      if (status.status === 'completed') {
        addNotification({
          type: 'success',
          title: 'Execution Complete',
          message: 'Workflow execution completed successfully',
        });
      } else if (status.status === 'error') {
        addNotification({
          type: 'error',
          title: 'Execution Error',
          message: 'Workflow execution encountered an error',
        });
      }
    } catch (error) {
      console.error('Failed to refresh execution status:', error);
    }
  };

  const getExecutionDuration = () => {
    if (!execution.startTime) return 0;
    const endTime = execution.endTime || Date.now();
    return Math.round((endTime - execution.startTime) / 1000);
  };

  const getStatusColor = (status: PreviewExecution['status']) => {
    switch (status) {
      case 'running': return 'info';
      case 'paused': return 'warning';
      case 'completed': return 'success';
      case 'error': return 'error';
      default: return 'secondary';
    }
  };

  const getStatusIcon = (status: PreviewExecution['status']) => {
    switch (status) {
      case 'running': return <ArrowPathIcon className="h-4 w-4 animate-spin" />;
      case 'paused': return <PauseIcon className="h-4 w-4" />;
      case 'completed': return <CheckCircleIcon className="h-4 w-4" />;
      case 'error': return <ExclamationTriangleIcon className="h-4 w-4" />;
      default: return <ClockIcon className="h-4 w-4" />;
    }
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <span>Real-Time Preview</span>
            <Badge variant={getStatusColor(execution.status)} size="sm">
              <span className="flex items-center space-x-1">
                {getStatusIcon(execution.status)}
                <span className="capitalize">{execution.status}</span>
              </span>
            </Badge>
          </CardTitle>

          <div className="flex items-center space-x-2">
            <select
              value={previewMode}
              onChange={(e) => setPreviewMode(e.target.value as any)}
              className="text-sm border border-gray-300 rounded px-2 py-1"
              disabled={execution.status === 'running'}
            >
              <option value="full">Full Workflow</option>
              <option value="selected">Selected Nodes</option>
              <option value="step">Step by Step</option>
            </select>
          </div>
        </div>

        {/* Validation Status */}
        {!isValidWorkflow && (
          <div className="bg-error-50 border border-error-200 rounded-lg p-3">
            <div className="flex items-start space-x-2">
              <ExclamationTriangleIcon className="h-5 w-5 text-error-500 mt-0.5" />
              <div>
                <h4 className="font-medium text-error-900">Validation Errors</h4>
                <ul className="text-sm text-error-700 mt-1 space-y-1">
                  {validationErrors.map((error, index) => (
                    <li key={index}>• {error}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Control Buttons */}
        <div className="flex items-center space-x-2">
          {execution.status === 'idle' && (
            <Button
              onClick={startExecution}
              disabled={!isValidWorkflow || canvasState.nodes.length === 0}
              leftIcon={<PlayIcon className="h-4 w-4" />}
            >
              Run
            </Button>
          )}

          {execution.status === 'running' && (
            <>
              <Button
                onClick={pauseExecution}
                variant="outline"
                leftIcon={<PauseIcon className="h-4 w-4" />}
              >
                Pause
              </Button>
              <Button
                onClick={stopExecution}
                variant="outline"
                leftIcon={<StopIcon className="h-4 w-4" />}
              >
                Stop
              </Button>
            </>
          )}

          {execution.status === 'paused' && (
            <>
              <Button
                onClick={resumeExecution}
                leftIcon={<PlayIcon className="h-4 w-4" />}
              >
                Resume
              </Button>
              <Button
                onClick={stopExecution}
                variant="outline"
                leftIcon={<StopIcon className="h-4 w-4" />}
              >
                Stop
              </Button>
            </>
          )}

          {(execution.status === 'completed' || execution.status === 'error') && (
            <Button
              onClick={() => setExecution({
                id: '',
                status: 'idle',
                results: {},
                errors: [],
                logs: [],
              })}
              variant="outline"
              leftIcon={<ArrowPathIcon className="h-4 w-4" />}
            >
              Reset
            </Button>
          )}
        </div>

        {/* Execution Info */}
        {execution.status !== 'idle' && (
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">Duration:</span>
              <span className="ml-2">{getExecutionDuration()}s</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Nodes:</span>
              <span className="ml-2">{canvasState.nodes.length}</span>
            </div>
            {execution.currentNodeId && (
              <div className="col-span-2">
                <span className="font-medium text-gray-700">Current Node:</span>
                <span className="ml-2">
                  {canvasState.nodes.find(n => n.id === execution.currentNodeId)?.metadata?.label || execution.currentNodeId}
                </span>
              </div>
            )}
          </div>
        )}

        {/* Results */}
        {Object.keys(execution.results).length > 0 && (
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Results</h4>
            <div className="bg-gray-50 rounded-lg p-3 max-h-40 overflow-y-auto">
              <pre className="text-xs text-gray-700">
                {JSON.stringify(execution.results, null, 2)}
              </pre>
            </div>
          </div>
        )}

        {/* Errors */}
        {execution.errors.length > 0 && (
          <div>
            <h4 className="font-medium text-error-900 mb-2">Errors</h4>
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {execution.errors.map((error, index) => (
                <div key={index} className="bg-error-50 border border-error-200 rounded p-2">
                  <div className="font-medium text-error-900 text-sm">
                    {canvasState.nodes.find(n => n.id === error.nodeId)?.metadata?.label || error.nodeId}
                  </div>
                  <div className="text-error-700 text-xs">{error.message}</div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Logs */}
        {execution.logs.length > 0 && (
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Execution Log</h4>
            <div className="bg-gray-900 text-gray-100 rounded-lg p-3 max-h-40 overflow-y-auto font-mono text-xs">
              {execution.logs.map((log, index) => (
                <div key={index} className="mb-1">
                  <span className="text-gray-400">
                    {new Date(log.timestamp).toLocaleTimeString()}
                  </span>
                  <span className={`ml-2 ${
                    log.level === 'error' ? 'text-red-400' :
                    log.level === 'warn' ? 'text-yellow-400' :
                    'text-green-400'
                  }`}>
                    [{log.level.toUpperCase()}]
                  </span>
                  <span className="ml-2">{log.message}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Empty State */}
        {execution.status === 'idle' && canvasState.nodes.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <ClockIcon className="h-12 w-12 mx-auto mb-3 opacity-50" />
            <p className="text-sm font-medium">No workflow to preview</p>
            <p className="text-xs">Add nodes to the canvas to start building</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
