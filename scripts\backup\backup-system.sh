#!/bin/bash

# SynapseAI Production Backup System
# Automated backup and disaster recovery for PostgreSQL, Redis, and application data

set -euo pipefail

# Configuration
BACKUP_DIR="/var/backups/synapseai"
S3_BUCKET="synapseai-backups"
RETENTION_DAYS=30
LOG_FILE="/var/log/synapseai/backup.log"
NOTIFICATION_WEBHOOK="${SLACK_WEBHOOK_URL:-}"

# Database configuration
DB_HOST="${POSTGRES_HOST:-localhost}"
DB_PORT="${POSTGRES_PORT:-5432}"
DB_NAME="${POSTGRES_DB:-synapseai}"
DB_USER="${POSTGRES_USER:-synapseai}"
PGPASSWORD="${POSTGRES_PASSWORD}"

# Redis configuration
REDIS_HOST="${REDIS_HOST:-localhost}"
REDIS_PORT="${REDIS_PORT:-6379}"
REDIS_PASSWORD="${REDIS_PASSWORD:-}"

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Error handling
error_exit() {
    log "ERROR: $1"
    send_notification "❌ SynapseAI Backup Failed" "$1"
    exit 1
}

# Success notification
send_notification() {
    local title="$1"
    local message="$2"
    
    if [[ -n "$NOTIFICATION_WEBHOOK" ]]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"$title\n$message\"}" \
            "$NOTIFICATION_WEBHOOK" || true
    fi
}

# Create backup directories
create_directories() {
    log "Creating backup directories..."
    
    mkdir -p "$BACKUP_DIR"/{postgres,redis,files,logs}
    mkdir -p "$BACKUP_DIR"/archive/{daily,weekly,monthly}
    
    # Set proper permissions
    chmod 750 "$BACKUP_DIR"
    chown -R postgres:postgres "$BACKUP_DIR"/postgres
}

# PostgreSQL backup
backup_postgres() {
    log "Starting PostgreSQL backup..."
    
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local backup_file="$BACKUP_DIR/postgres/synapseai_${timestamp}.sql"
    local compressed_file="${backup_file}.gz"
    
    # Create database dump
    pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        --verbose --clean --if-exists --create \
        --format=custom --compress=9 \
        --file="$backup_file" || error_exit "PostgreSQL backup failed"
    
    # Compress the backup
    gzip "$backup_file" || error_exit "Failed to compress PostgreSQL backup"
    
    # Verify backup integrity
    pg_restore --list "$compressed_file" > /dev/null || error_exit "PostgreSQL backup verification failed"
    
    # Calculate backup size
    local backup_size=$(du -h "$compressed_file" | cut -f1)
    log "PostgreSQL backup completed: $compressed_file ($backup_size)"
    
    echo "$compressed_file"
}

# Redis backup
backup_redis() {
    log "Starting Redis backup..."
    
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local backup_file="$BACKUP_DIR/redis/redis_${timestamp}.rdb"
    
    # Create Redis backup using BGSAVE
    if [[ -n "$REDIS_PASSWORD" ]]; then
        redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" BGSAVE || error_exit "Redis BGSAVE failed"
    else
        redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" BGSAVE || error_exit "Redis BGSAVE failed"
    fi
    
    # Wait for background save to complete
    local save_in_progress=1
    while [[ $save_in_progress -eq 1 ]]; do
        sleep 5
        if [[ -n "$REDIS_PASSWORD" ]]; then
            save_in_progress=$(redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" LASTSAVE | xargs)
        else
            save_in_progress=$(redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" LASTSAVE | xargs)
        fi
        
        # Check if save completed by comparing timestamps
        local current_save=$(date +%s)
        if [[ $((current_save - save_in_progress)) -lt 60 ]]; then
            save_in_progress=0
        fi
    done
    
    # Copy the RDB file
    docker cp synapseai-redis-prod:/data/dump.rdb "$backup_file" || error_exit "Failed to copy Redis backup"
    
    # Compress the backup
    gzip "$backup_file" || error_exit "Failed to compress Redis backup"
    
    local backup_size=$(du -h "${backup_file}.gz" | cut -f1)
    log "Redis backup completed: ${backup_file}.gz ($backup_size)"
    
    echo "${backup_file}.gz"
}

# Application files backup
backup_files() {
    log "Starting application files backup..."
    
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local backup_file="$BACKUP_DIR/files/app_files_${timestamp}.tar.gz"
    
    # Backup configuration files, logs, and uploads
    tar -czf "$backup_file" \
        --exclude='node_modules' \
        --exclude='*.log' \
        --exclude='tmp' \
        /app/config \
        /app/uploads \
        /app/ssl \
        /etc/nginx/nginx.conf \
        /etc/redis/redis.conf \
        2>/dev/null || error_exit "Application files backup failed"
    
    local backup_size=$(du -h "$backup_file" | cut -f1)
    log "Application files backup completed: $backup_file ($backup_size)"
    
    echo "$backup_file"
}

# Upload to S3
upload_to_s3() {
    local file="$1"
    local s3_path="$2"
    
    log "Uploading $file to S3..."
    
    aws s3 cp "$file" "s3://$S3_BUCKET/$s3_path" \
        --storage-class STANDARD_IA \
        --server-side-encryption AES256 || error_exit "Failed to upload $file to S3"
    
    log "Successfully uploaded to S3: s3://$S3_BUCKET/$s3_path"
}

# Cleanup old backups
cleanup_old_backups() {
    log "Cleaning up old backups..."
    
    # Local cleanup
    find "$BACKUP_DIR" -type f -name "*.gz" -mtime +$RETENTION_DAYS -delete
    find "$BACKUP_DIR" -type f -name "*.sql" -mtime +$RETENTION_DAYS -delete
    find "$BACKUP_DIR" -type f -name "*.tar.gz" -mtime +$RETENTION_DAYS -delete
    
    # S3 cleanup (using lifecycle policy is preferred, but this is a backup)
    aws s3 ls "s3://$S3_BUCKET/" --recursive | \
        awk '$1 < "'$(date -d "$RETENTION_DAYS days ago" '+%Y-%m-%d')'" {print $4}' | \
        xargs -I {} aws s3 rm "s3://$S3_BUCKET/{}" || true
    
    log "Cleanup completed"
}

# Verify backup integrity
verify_backups() {
    local postgres_backup="$1"
    local redis_backup="$2"
    local files_backup="$3"
    
    log "Verifying backup integrity..."
    
    # Verify PostgreSQL backup
    pg_restore --list "$postgres_backup" > /dev/null || error_exit "PostgreSQL backup verification failed"
    
    # Verify Redis backup
    file "$redis_backup" | grep -q "gzip compressed" || error_exit "Redis backup verification failed"
    
    # Verify files backup
    tar -tzf "$files_backup" > /dev/null || error_exit "Files backup verification failed"
    
    log "All backups verified successfully"
}

# Generate backup report
generate_report() {
    local postgres_backup="$1"
    local redis_backup="$2"
    local files_backup="$3"
    
    local postgres_size=$(du -h "$postgres_backup" | cut -f1)
    local redis_size=$(du -h "$redis_backup" | cut -f1)
    local files_size=$(du -h "$files_backup" | cut -f1)
    
    local report="SynapseAI Backup Report - $(date '+%Y-%m-%d %H:%M:%S')
    
PostgreSQL Backup: $postgres_size
Redis Backup: $redis_size
Application Files: $files_size

All backups completed successfully and uploaded to S3.
Retention period: $RETENTION_DAYS days"
    
    echo "$report" > "$BACKUP_DIR/backup_report_$(date '+%Y%m%d_%H%M%S').txt"
    
    send_notification "✅ SynapseAI Backup Completed" "$report"
}

# Main backup function
main() {
    log "Starting SynapseAI backup process..."
    
    # Check prerequisites
    command -v pg_dump >/dev/null 2>&1 || error_exit "pg_dump not found"
    command -v redis-cli >/dev/null 2>&1 || error_exit "redis-cli not found"
    command -v aws >/dev/null 2>&1 || error_exit "AWS CLI not found"
    
    # Create directories
    create_directories
    
    # Perform backups
    local postgres_backup=$(backup_postgres)
    local redis_backup=$(backup_redis)
    local files_backup=$(backup_files)
    
    # Verify backups
    verify_backups "$postgres_backup" "$redis_backup" "$files_backup"
    
    # Upload to S3
    local date_path=$(date '+%Y/%m/%d')
    upload_to_s3 "$postgres_backup" "postgres/$date_path/$(basename "$postgres_backup")"
    upload_to_s3 "$redis_backup" "redis/$date_path/$(basename "$redis_backup")"
    upload_to_s3 "$files_backup" "files/$date_path/$(basename "$files_backup")"
    
    # Cleanup old backups
    cleanup_old_backups
    
    # Generate report
    generate_report "$postgres_backup" "$redis_backup" "$files_backup"
    
    log "Backup process completed successfully"
}

# Run main function
main "$@"
