{"version": 3, "file": "casl-ability.factory.js", "sourceRoot": "", "sources": ["../../../../../../src/modules/auth/casl/casl-ability.factory.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAA4C;AAC5C,2CAAyG;AACzG,IAAK,QAKJ;AALD,WAAK,QAAQ;IACX,uCAA2B,CAAA;IAC3B,mCAAuB,CAAA;IACvB,mCAAuB,CAAA;IACvB,6BAAiB,CAAA;AACnB,CAAC,EALI,QAAQ,KAAR,QAAQ,QAKZ;AAmCM,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;IAC7B,aAAa,CAAC,IAAiB;QAC7B,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,wBAAc,CAC/C,iBAAmC,CACpC,CAAC;QAGF,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,QAAQ,CAAC,WAAW;gBAEvB,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;gBACrB,MAAM;YAER,KAAK,QAAQ,CAAC,SAAS;gBAErB,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBACtB,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;gBACvB,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBACtB,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;gBAC1B,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;gBAC1B,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBACxB,GAAG,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;gBACzB,GAAG,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;gBAC9B,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;gBAC1B,GAAG,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;gBACzB,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;gBACxB,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;gBACzB,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;gBAGvB,MAAM,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;gBACjC,MAAM,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;gBACjC,MAAM,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;gBAC5B,MAAM;YAER,KAAK,QAAQ,CAAC,SAAS;gBAErB,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;gBACpB,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;gBACvB,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBACtB,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;gBAC1B,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;gBAC1B,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBACxB,GAAG,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;gBACzB,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;gBAC1B,GAAG,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;gBACzB,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;gBACxB,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;gBACzB,GAAG,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;gBAC5B,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;gBACxB,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;gBACvB,GAAG,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;gBAC3B,MAAM;YAER,KAAK,QAAQ,CAAC,MAAM;gBAElB,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBACrB,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;gBACpB,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;gBACxB,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;gBACxB,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBACtB,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;gBACxB,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;gBACzB,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;gBACxB,GAAG,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;gBAC5B,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;gBACvB,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;gBACvB,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;gBACxB,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;gBACvB,GAAG,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;gBAC3B,MAAM;YAER;gBAEE,MAAM;QACV,CAAC;QAGD,MAAM,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;QAGjC,OAAO,KAAK,CAAC;YACX,iBAAiB,EAAE,CAAC,IAAI,EAAE,EAAE,CACzB,IAAY,CAAC,WAAW,EAAE,IAAoC;SAClE,CAAC,CAAC;IACL,CAAC;IAKD,eAAe,CAAC,cAAsB,EAAE,WAAqB;QAC3D,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,IAAI,wBAAc,CACvC,iBAAmC,CACpC,CAAC;QAGF,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;YAC/B,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAChD,IAAI,MAAM,IAAI,OAAO,EAAE,CAAC;gBACtB,GAAG,CAAC,MAAgB,EAAE,OAAc,CAAC,CAAC;YACxC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;YACX,iBAAiB,EAAE,CAAC,IAAI,EAAE,EAAE,CACzB,IAAY,CAAC,WAAW,EAAE,IAAoC;SAClE,CAAC,CAAC;IACL,CAAC;CACF,CAAA;AA5GY,gDAAkB;6BAAlB,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;GACA,kBAAkB,CA4G9B"}