'use client';

import { useRef, useEffect, useState, useCallback } from 'react';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { CanvasEngine, CanvasNode, CanvasConnection, CanvasState } from '@/lib/visual-builder/canvas';
import { useAppStore } from '@/lib/store';
import {
  PlusIcon,
  TrashIcon,
  ArrowsPointingOutIcon,
  MagnifyingGlassMinusIcon,
  MagnifyingGlassPlusIcon,
  Squares2X2Icon,
  EyeIcon,
  EyeSlashIcon,
} from '@heroicons/react/24/outline';

interface VisualCanvasProps {
  initialState?: Partial<CanvasState>;
  onStateChange?: (state: CanvasState) => void;
  onNodeSelect?: (nodes: CanvasNode[]) => void;
  onNodeAdd?: (node: CanvasNode) => void;
  className?: string;
  readOnly?: boolean;
}

export function VisualCanvas({
  initialState,
  onStateChange,
  onNodeSelect,
  onNodeAdd,
  className,
  readOnly = false,
}: VisualCanvasProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const engineRef = useRef<CanvasEngine | null>(null);
  const [canvasState, setCanvasState] = useState<CanvasState | null>(null);
  const [selectedNodes, setSelectedNodes] = useState<CanvasNode[]>([]);
  const [showGrid, setShowGrid] = useState(true);
  const [showMinimap, setShowMinimap] = useState(true);

  const { addNotification } = useAppStore();

  // Initialize canvas engine
  useEffect(() => {
    if (!canvasRef.current) return;

    const engine = new CanvasEngine(initialState);
    engineRef.current = engine;

    engine.initialize(canvasRef.current, {
      onNodeSelect: (nodeIds) => {
        const nodes = nodeIds.map(id => 
          engine.getState().nodes.find(n => n.id === id)
        ).filter(Boolean) as CanvasNode[];
        
        setSelectedNodes(nodes);
        onNodeSelect?.(nodes);
      },
      
      onNodeDeselect: () => {
        setSelectedNodes([]);
        onNodeSelect?.([]);
      },
      
      onNodeMove: (_nodeId, _position) => {
        const state = engine.getState();
        setCanvasState({ ...state });
        onStateChange?.(state);
      },

      onNodeDelete: (nodeIds) => {
        addNotification({
          type: 'info',
          title: 'Nodes Deleted',
          message: `Deleted ${nodeIds.length} node${nodeIds.length !== 1 ? 's' : ''}`,
        });
        
        const state = engine.getState();
        setCanvasState({ ...state });
        onStateChange?.(state);
      },
      
      onNodeAdd: (node) => {
        const state = engine.getState();
        const addedNode = state.nodes.find(n => n.position.x === node.position.x && n.position.y === node.position.y);
        if (addedNode) {
          onNodeAdd?.(addedNode);
        }
        
        setCanvasState({ ...state });
        onStateChange?.(state);
      },
      
      onConnectionCreate: (_connection) => {
        addNotification({
          type: 'success',
          title: 'Connection Created',
          message: 'Successfully connected nodes',
        });

        const state = engine.getState();
        setCanvasState({ ...state });
        onStateChange?.(state);
      },
      
      onConnectionDelete: (connectionIds) => {
        addNotification({
          type: 'info',
          title: 'Connections Deleted',
          message: `Deleted ${connectionIds.length} connection${connectionIds.length !== 1 ? 's' : ''}`,
        });
        
        const state = engine.getState();
        setCanvasState({ ...state });
        onStateChange?.(state);
      },
      
      onViewportChange: (_viewport) => {
        const state = engine.getState();
        setCanvasState({ ...state });
        onStateChange?.(state);
      },

      onCanvasClick: (_position) => {
        // Handle canvas click for context menu, etc.
      },
    });

    setCanvasState(engine.getState());

    // Handle canvas resize
    const handleResize = () => {
      if (canvasRef.current) {
        const rect = canvasRef.current.parentElement!.getBoundingClientRect();
        canvasRef.current.width = rect.width;
        canvasRef.current.height = rect.height;
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [initialState, onStateChange, onNodeSelect, onNodeAdd, addNotification]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!engineRef.current || readOnly) return;

      if (event.key === 'Delete' || event.key === 'Backspace') {
        if (selectedNodes.length > 0) {
          engineRef.current.removeNodes(selectedNodes.map(n => n.id));
        }
      }

      if (event.key === 'a' && (event.ctrlKey || event.metaKey)) {
        event.preventDefault();
        const allNodeIds = engineRef.current.getState().nodes.map(n => n.id);
        engineRef.current.selectNodes(allNodeIds);
      }

      if (event.key === 'Escape') {
        engineRef.current.selectNodes([]);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedNodes, readOnly]);

  const handleAddNode = useCallback((type: CanvasNode['type']) => {
    if (!engineRef.current || readOnly) return;

    const state = engineRef.current.getState();
    const centerX = (-state.viewport.x + 400) / state.viewport.zoom;
    const centerY = (-state.viewport.y + 300) / state.viewport.zoom;

    const newNode: Omit<CanvasNode, 'id'> = {
      type,
      position: { x: centerX, y: centerY },
      size: { width: 150, height: 80 },
      data: {},
      inputs: getDefaultInputs(type),
      outputs: getDefaultOutputs(type),
      metadata: {
        label: `New ${type.charAt(0).toUpperCase() + type.slice(1)}`,
        category: type,
      },
    };

    engineRef.current.addNode(newNode);
  }, [readOnly]);

  const handleZoomIn = useCallback(() => {
    if (!engineRef.current) return;
    const state = engineRef.current.getState();
    engineRef.current.updateViewport({ zoom: Math.min(3, state.viewport.zoom * 1.2) });
  }, []);

  const handleZoomOut = useCallback(() => {
    if (!engineRef.current) return;
    const state = engineRef.current.getState();
    engineRef.current.updateViewport({ zoom: Math.max(0.1, state.viewport.zoom / 1.2) });
  }, []);

  const handleZoomToFit = useCallback(() => {
    if (!engineRef.current) return;
    
    const state = engineRef.current.getState();
    if (state.nodes.length === 0) return;

    // Calculate bounding box of all nodes
    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
    
    state.nodes.forEach(node => {
      minX = Math.min(minX, node.position.x);
      minY = Math.min(minY, node.position.y);
      maxX = Math.max(maxX, node.position.x + node.size.width);
      maxY = Math.max(maxY, node.position.y + node.size.height);
    });

    const padding = 50;
    const contentWidth = maxX - minX + padding * 2;
    const contentHeight = maxY - minY + padding * 2;
    
    const canvas = canvasRef.current!;
    const scaleX = canvas.width / contentWidth;
    const scaleY = canvas.height / contentHeight;
    const scale = Math.min(scaleX, scaleY, 1);

    const centerX = (minX + maxX) / 2;
    const centerY = (minY + maxY) / 2;
    
    engineRef.current.updateViewport({
      x: canvas.width / 2 - centerX * scale,
      y: canvas.height / 2 - centerY * scale,
      zoom: scale,
    });
  }, []);

  const handleAutoLayout = useCallback(() => {
    if (!engineRef.current || readOnly) return;
    
    engineRef.current.autoLayout();
    addNotification({
      type: 'success',
      title: 'Auto Layout Applied',
      message: 'Nodes have been automatically arranged',
    });
  }, [readOnly, addNotification]);

  const handleDeleteSelected = useCallback(() => {
    if (!engineRef.current || readOnly || selectedNodes.length === 0) return;
    
    engineRef.current.removeNodes(selectedNodes.map(n => n.id));
  }, [selectedNodes, readOnly]);

  const handleToggleGrid = useCallback(() => {
    if (!engineRef.current) return;
    
    const newShowGrid = !showGrid;
    setShowGrid(newShowGrid);
    
    const state = engineRef.current.getState();
    state.settings.showGrid = newShowGrid;
  }, [showGrid]);

  return (
    <div className={`relative w-full h-full bg-gray-50 ${className}`}>
      {/* Toolbar */}
      <div className="absolute top-4 left-4 z-10 flex items-center space-x-2">
        <Card className="flex items-center space-x-2 p-2">
          {!readOnly && (
            <>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleAddNode('agent')}
                leftIcon={<PlusIcon className="h-4 w-4" />}
              >
                Agent
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleAddNode('tool')}
                leftIcon={<PlusIcon className="h-4 w-4" />}
              >
                Tool
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleAddNode('condition')}
                leftIcon={<PlusIcon className="h-4 w-4" />}
              >
                Condition
              </Button>
              
              <div className="w-px h-6 bg-gray-300" />
              
              <Button
                size="sm"
                variant="outline"
                onClick={handleDeleteSelected}
                disabled={selectedNodes.length === 0}
                leftIcon={<TrashIcon className="h-4 w-4" />}
              >
                Delete
              </Button>
              
              <Button
                size="sm"
                variant="outline"
                onClick={handleAutoLayout}
                leftIcon={<Squares2X2Icon className="h-4 w-4" />}
              >
                Auto Layout
              </Button>
            </>
          )}
        </Card>
      </div>

      {/* Zoom Controls */}
      <div className="absolute top-4 right-4 z-10">
        <Card className="flex flex-col space-y-1 p-2">
          <Button
            size="sm"
            variant="outline"
            onClick={handleZoomIn}
            leftIcon={<MagnifyingGlassPlusIcon className="h-4 w-4" />}
          />
          <Button
            size="sm"
            variant="outline"
            onClick={handleZoomOut}
            leftIcon={<MagnifyingGlassMinusIcon className="h-4 w-4" />}
          />
          <Button
            size="sm"
            variant="outline"
            onClick={handleZoomToFit}
            leftIcon={<ArrowsPointingOutIcon className="h-4 w-4" />}
          />
          <div className="w-px h-2 bg-gray-300 mx-auto" />
          <Button
            size="sm"
            variant="outline"
            onClick={handleToggleGrid}
            leftIcon={showGrid ? <EyeSlashIcon className="h-4 w-4" /> : <EyeIcon className="h-4 w-4" />}
          />
        </Card>
      </div>

      {/* Status Bar */}
      <div className="absolute bottom-4 left-4 z-10">
        <Card className="px-3 py-2">
          <div className="flex items-center space-x-4 text-sm text-gray-600">
            <span>Nodes: {canvasState?.nodes.length || 0}</span>
            <span>Connections: {canvasState?.connections.length || 0}</span>
            <span>Selected: {selectedNodes.length}</span>
            {canvasState && (
              <span>
                Zoom: {Math.round(canvasState.viewport.zoom * 100)}%
              </span>
            )}
          </div>
        </Card>
      </div>

      {/* Canvas */}
      <canvas
        ref={canvasRef}
        className="w-full h-full cursor-grab active:cursor-grabbing"
        style={{ touchAction: 'none' }}
      />

      {/* Minimap */}
      {showMinimap && canvasState && (
        <div className="absolute bottom-4 right-4 z-10">
          <Card className="p-2">
            <div className="w-32 h-24 bg-gray-100 border border-gray-300 relative">
              {/* Minimap content would be rendered here */}
              <div className="absolute inset-0 flex items-center justify-center text-xs text-gray-500">
                Minimap
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
}

// Helper functions for default node configurations
function getDefaultInputs(type: CanvasNode['type']): CanvasNode['inputs'] {
  switch (type) {
    case 'agent':
      return [
        { id: 'input', name: 'Input', type: 'string', required: true },
        { id: 'context', name: 'Context', type: 'object', required: false },
      ];
    case 'tool':
      return [
        { id: 'params', name: 'Parameters', type: 'object', required: true },
      ];
    case 'condition':
      return [
        { id: 'value', name: 'Value', type: 'any', required: true },
      ];
    default:
      return [];
  }
}

function getDefaultOutputs(type: CanvasNode['type']): CanvasNode['outputs'] {
  switch (type) {
    case 'agent':
      return [
        { id: 'response', name: 'Response', type: 'string' },
        { id: 'metadata', name: 'Metadata', type: 'object' },
      ];
    case 'tool':
      return [
        { id: 'result', name: 'Result', type: 'any' },
        { id: 'error', name: 'Error', type: 'string' },
      ];
    case 'condition':
      return [
        { id: 'true', name: 'True', type: 'any' },
        { id: 'false', name: 'False', type: 'any' },
      ];
    default:
      return [];
  }
}
