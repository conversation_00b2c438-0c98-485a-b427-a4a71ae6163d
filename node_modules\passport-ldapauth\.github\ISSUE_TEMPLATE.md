If you know how to fix the issue, make a pull request instead.

- [ ] I have a question that is inappropriate for [StackOverflow](https://stackoverflow.com/).  (Please ask any appropriate questions, such as how to use the library, there).
- [ ] I believe this is an issue in this library and not in the underlying libraries [ldapjs](https://github.com/joyent/node-ldapjs/) or [ldapauth-fork](https://github.com/vesse/node-ldapauth-fork). (This library is a passport strategy and does not implement the LDAP communication)

**Note:** if the issue template is not used, the issue will be closed.

## Problem Description


## Steps to Reproduce

