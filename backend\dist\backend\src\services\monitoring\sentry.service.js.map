{"version": 3, "file": "sentry.service.js", "sourceRoot": "", "sources": ["../../../../../src/services/monitoring/sentry.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA+C;AAC/C,uCAAuC;AACvC,2DAA8D;AAGvD,IAAM,aAAa,qBAAnB,MAAM,aAAa;IAIxB,YAAoB,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QAH/B,WAAM,GAAG,IAAI,eAAM,CAAC,eAAa,CAAC,IAAI,CAAC,CAAC;QACjD,gBAAW,GAAG,KAAK,CAAC;IAEuB,CAAC;IAKpD,IAAI;QACF,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QACjD,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;QACtE,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;QAE/D,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;YACvE,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,CAAC,IAAI,CAAC;gBACV,GAAG;gBACH,WAAW;gBACX,OAAO,EAAE,aAAa,OAAO,EAAE;gBAG/B,gBAAgB,EAAE,WAAW,KAAK,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;gBAC1D,kBAAkB,EAAE,WAAW,KAAK,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;gBAG5D,YAAY,EAAE;oBAEZ,IAAI,qCAAoB,EAAE;oBAG1B,IAAI,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;oBAG/C,IAAI,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,GAAG,EAAE,SAAS,EAAE,CAAC;oBAGnD,IAAI,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE;oBAGjC,IAAI,MAAM,CAAC,YAAY,CAAC,mBAAmB,CAAC;wBAC1C,oCAAoC,EAAE,KAAK;qBAC5C,CAAC;oBAGF,IAAI,MAAM,CAAC,YAAY,CAAC,oBAAoB,CAAC;wBAC3C,IAAI,EAAE,MAAM;qBACb,CAAC;iBACH;gBAGD,UAAU,CAAC,KAAK,EAAE,IAAI;oBAEpB,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC;oBAErC,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;wBAE3B,IAAI,WAAW,KAAK,aAAa,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,sBAAsB,CAAC,EAAE,CAAC;4BACpF,OAAO,IAAI,CAAC;wBACd,CAAC;wBAGD,IAAI,WAAW,KAAK,aAAa,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE,CAAC;4BAChF,OAAO,IAAI,CAAC;wBACd,CAAC;wBAGD,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE,CAAC;4BAC9C,OAAO,IAAI,CAAC;wBACd,CAAC;oBACH,CAAC;oBAED,OAAO,KAAK,CAAC;gBACf,CAAC;gBAGD,qBAAqB,CAAC,KAAK;oBAEzB,IAAI,KAAK,CAAC,WAAW,KAAK,aAAa,EAAE,CAAC;wBACxC,OAAO,IAAI,CAAC;oBACd,CAAC;oBAGD,IAAI,WAAW,KAAK,YAAY,IAAI,KAAK,CAAC,eAAe,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;wBAC7E,MAAM,QAAQ,GAAG,CAAC,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC;wBAClE,IAAI,QAAQ,GAAG,GAAG,EAAE,CAAC;4BACnB,OAAO,IAAI,CAAC;wBACd,CAAC;oBACH,CAAC;oBAED,OAAO,KAAK,CAAC;gBACf,CAAC;gBAGD,cAAc,EAAE,EAAE;gBAClB,gBAAgB,EAAE,IAAI;gBACtB,cAAc,EAAE,KAAK;gBAGrB,YAAY,EAAE;oBACZ,IAAI,EAAE;wBACJ,SAAS,EAAE,SAAS;wBACpB,OAAO,EAAE,WAAW;qBACrB;iBACF;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAKD,gBAAgB,CACd,KAAY,EACZ,OAKC;QAED,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAC;YACtE,OAAO;QACT,CAAC;QAED,OAAO,MAAM,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,EAAE;YAChC,IAAI,OAAO,EAAE,IAAI,EAAE,CAAC;gBAClB,KAAK,CAAC,OAAO,CAAC;oBACZ,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;oBACnB,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK;iBAC1B,CAAC,CAAC;gBAEH,IAAI,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;oBAChC,KAAK,CAAC,MAAM,CAAC,gBAAgB,EAAE,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBAC9D,CAAC;YACH,CAAC;YAED,IAAI,OAAO,EAAE,IAAI,EAAE,CAAC;gBAClB,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;oBACpD,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;gBAC3B,CAAC,CAAC,CAAC;YACL,CAAC;YAED,IAAI,OAAO,EAAE,KAAK,EAAE,CAAC;gBACnB,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;oBACrD,KAAK,CAAC,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;gBAC7B,CAAC,CAAC,CAAC;YACL,CAAC;YAED,IAAI,OAAO,EAAE,KAAK,EAAE,CAAC;gBACnB,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAChC,CAAC;YAED,OAAO,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC;IAKD,cAAc,CACZ,OAAe,EACf,QAA8B,MAAM,EACpC,OAIC;QAED,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;YACnE,OAAO;QACT,CAAC;QAED,OAAO,MAAM,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,EAAE;YAChC,IAAI,OAAO,EAAE,IAAI,EAAE,CAAC;gBAClB,KAAK,CAAC,OAAO,CAAC;oBACZ,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;oBACnB,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK;iBAC1B,CAAC,CAAC;gBAEH,IAAI,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;oBAChC,KAAK,CAAC,MAAM,CAAC,gBAAgB,EAAE,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBAC9D,CAAC;YACH,CAAC;YAED,IAAI,OAAO,EAAE,IAAI,EAAE,CAAC;gBAClB,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;oBACpD,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;gBAC3B,CAAC,CAAC,CAAC;YACL,CAAC;YAED,IAAI,OAAO,EAAE,KAAK,EAAE,CAAC;gBACnB,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;oBACrD,KAAK,CAAC,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;gBAC7B,CAAC,CAAC,CAAC;YACL,CAAC;YAED,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACtB,OAAO,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC;IAKD,gBAAgB,CACd,IAAY,EACZ,EAAU,EACV,WAAoB;QAEpB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,OAAO;QACT,CAAC;QAED,OAAO,MAAM,CAAC,gBAAgB,CAAC;YAC7B,IAAI;YACJ,EAAE;YACF,WAAW;SACZ,CAAC,CAAC;IACL,CAAC;IAKD,aAAa,CACX,OAAe,EACf,QAAgB,EAChB,QAA8B,MAAM,EACpC,IAA0B;QAE1B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,OAAO;QACT,CAAC;QAED,MAAM,CAAC,aAAa,CAAC;YACnB,OAAO;YACP,QAAQ;YACR,KAAK;YACL,IAAI;YACJ,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI;SAC7B,CAAC,CAAC;IACL,CAAC;IAKD,OAAO,CAAC,IAKP;QACC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,OAAO;QACT,CAAC;QAED,MAAM,CAAC,OAAO,CAAC;YACb,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,KAAK,EAAE,IAAI,CAAC,KAAK;SAClB,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAKD,OAAO,CAAC,IAA4B;QAClC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,OAAO;QACT,CAAC;QAED,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;IAKD,UAAU,CAAC,GAAW,EAAE,OAA4B;QAClD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,OAAO;QACT,CAAC;QAED,MAAM,CAAC,UAAU,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IAClC,CAAC;IAKD,KAAK,CAAC,KAAK,CAAC,UAAkB,IAAI;QAChC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,CAAC;YACH,OAAO,MAAM,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACrC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YAC3D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,KAAK,CAAC,UAAkB,IAAI;QAChC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,CAAC;YACH,OAAO,MAAM,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACrC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YAC3D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAKD,aAAa;QACX,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;CACF,CAAA;AArVY,sCAAa;wBAAb,aAAa;IADzB,IAAA,mBAAU,GAAE;qCAKwB,sBAAa;GAJrC,aAAa,CAqVzB"}