"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"f5bfb1db2691\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vYXBwL2dsb2JhbHMuY3NzP2NkZjYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJmNWJmYjFkYjI2OTFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/accessibility/a11y-utils.ts":
/*!*****************************************!*\
  !*** ./lib/accessibility/a11y-utils.ts ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ColorContrast: function() { return /* binding */ ColorContrast; },\n/* harmony export */   FocusManager: function() { return /* binding */ FocusManager; },\n/* harmony export */   KeyboardNavigation: function() { return /* binding */ KeyboardNavigation; },\n/* harmony export */   MotionPreferences: function() { return /* binding */ MotionPreferences; },\n/* harmony export */   ScreenReaderAnnouncer: function() { return /* binding */ ScreenReaderAnnouncer; },\n/* harmony export */   colorContrast: function() { return /* binding */ colorContrast; },\n/* harmony export */   defaultA11yConfig: function() { return /* binding */ defaultA11yConfig; },\n/* harmony export */   focusManager: function() { return /* binding */ focusManager; },\n/* harmony export */   initializeA11y: function() { return /* binding */ initializeA11y; },\n/* harmony export */   keyboardNavigation: function() { return /* binding */ keyboardNavigation; },\n/* harmony export */   motionPreferences: function() { return /* binding */ motionPreferences; },\n/* harmony export */   screenReaderAnnouncer: function() { return /* binding */ screenReaderAnnouncer; }\n/* harmony export */ });\n/**\n * Accessibility utilities for WCAG 2.1 AA compliance\n */ const defaultA11yConfig = {\n    announcePageChanges: true,\n    announceFormErrors: true,\n    announceStatusUpdates: true,\n    enableKeyboardNavigation: true,\n    enableFocusManagement: true,\n    enableHighContrast: true,\n    enableReducedMotion: true,\n    minimumTouchTarget: 44,\n    minimumColorContrast: 4.5\n};\n/**\n * Screen reader announcements\n */ class ScreenReaderAnnouncer {\n    /**\n   * Create ARIA live regions for announcements\n   */ createLiveRegions() {\n        // Assertive live region for urgent announcements\n        this.liveRegion = document.createElement(\"div\");\n        this.liveRegion.setAttribute(\"aria-live\", \"assertive\");\n        this.liveRegion.setAttribute(\"aria-atomic\", \"true\");\n        this.liveRegion.setAttribute(\"class\", \"sr-only\");\n        this.liveRegion.style.cssText = \"\\n      position: absolute !important;\\n      width: 1px !important;\\n      height: 1px !important;\\n      padding: 0 !important;\\n      margin: -1px !important;\\n      overflow: hidden !important;\\n      clip: rect(0, 0, 0, 0) !important;\\n      white-space: nowrap !important;\\n      border: 0 !important;\\n    \";\n        document.body.appendChild(this.liveRegion);\n        // Polite live region for non-urgent announcements\n        this.politeRegion = document.createElement(\"div\");\n        this.politeRegion.setAttribute(\"aria-live\", \"polite\");\n        this.politeRegion.setAttribute(\"aria-atomic\", \"true\");\n        this.politeRegion.setAttribute(\"class\", \"sr-only\");\n        this.politeRegion.style.cssText = this.liveRegion.style.cssText;\n        document.body.appendChild(this.politeRegion);\n    }\n    /**\n   * Announce message to screen readers\n   */ announce(message) {\n        let priority = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"polite\";\n        if (!message.trim()) return;\n        const region = priority === \"assertive\" ? this.liveRegion : this.politeRegion;\n        if (!region) return;\n        // Clear previous message\n        region.textContent = \"\";\n        // Add new message after a brief delay to ensure it's announced\n        setTimeout(()=>{\n            region.textContent = message;\n        }, 100);\n        // Clear message after announcement\n        setTimeout(()=>{\n            region.textContent = \"\";\n        }, 1000);\n    }\n    /**\n   * Announce page change\n   */ announcePageChange(pageName) {\n        this.announce(\"Navigated to \".concat(pageName), \"polite\");\n    }\n    /**\n   * Announce form errors\n   */ announceFormErrors(errors) {\n        if (errors.length === 0) return;\n        const message = errors.length === 1 ? \"Form error: \".concat(errors[0]) : \"Form has \".concat(errors.length, \" errors: \").concat(errors.join(\", \"));\n        this.announce(message, \"assertive\");\n    }\n    /**\n   * Announce status updates\n   */ announceStatus(status) {\n        let type = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"info\";\n        const prefix = {\n            success: \"Success:\",\n            error: \"Error:\",\n            warning: \"Warning:\",\n            info: \"Info:\"\n        }[type];\n        this.announce(\"\".concat(prefix, \" \").concat(status), type === \"error\" ? \"assertive\" : \"polite\");\n    }\n    /**\n   * Cleanup\n   */ destroy() {\n        if (this.liveRegion) {\n            document.body.removeChild(this.liveRegion);\n            this.liveRegion = null;\n        }\n        if (this.politeRegion) {\n            document.body.removeChild(this.politeRegion);\n            this.politeRegion = null;\n        }\n    }\n    constructor(){\n        this.liveRegion = null;\n        this.politeRegion = null;\n        if (true) {\n            this.createLiveRegions();\n        }\n    }\n}\n/**\n * Focus management utilities\n */ class FocusManager {\n    /**\n   * Set focus to element with optional announcement\n   */ setFocus(element, announce) {\n        const target = typeof element === \"string\" ? document.querySelector(element) : element;\n        if (!target) return;\n        // Store previous focus\n        const activeElement = document.activeElement;\n        if (activeElement && activeElement !== target) {\n            this.focusStack.push(activeElement);\n        }\n        // Set focus\n        target.focus();\n        // Announce if provided\n        if (announce) {\n            screenReaderAnnouncer.announce(announce);\n        }\n    }\n    /**\n   * Restore previous focus\n   */ restoreFocus() {\n        const previousElement = this.focusStack.pop();\n        if (previousElement && document.contains(previousElement)) {\n            previousElement.focus();\n        }\n    }\n    /**\n   * Trap focus within container\n   */ trapFocus(container) {\n        this.trapStack.push(container);\n        this.setupFocusTrap(container);\n    }\n    /**\n   * Release focus trap\n   */ releaseFocusTrap() {\n        const container = this.trapStack.pop();\n        if (container) {\n            this.removeFocusTrap(container);\n        }\n    }\n    /**\n   * Setup focus trap for container\n   */ setupFocusTrap(container) {\n        const focusableElements = this.getFocusableElements(container);\n        if (focusableElements.length === 0) return;\n        const firstElement = focusableElements[0];\n        const lastElement = focusableElements[focusableElements.length - 1];\n        const handleKeyDown = (event)=>{\n            if (event.key !== \"Tab\") return;\n            if (event.shiftKey) {\n                // Shift + Tab\n                if (document.activeElement === firstElement) {\n                    event.preventDefault();\n                    lastElement === null || lastElement === void 0 ? void 0 : lastElement.focus();\n                }\n            } else {\n                // Tab\n                if (document.activeElement === lastElement) {\n                    event.preventDefault();\n                    firstElement === null || firstElement === void 0 ? void 0 : firstElement.focus();\n                }\n            }\n        };\n        container.addEventListener(\"keydown\", handleKeyDown);\n        container.setAttribute(\"data-focus-trap\", \"true\");\n        // Set initial focus\n        firstElement === null || firstElement === void 0 ? void 0 : firstElement.focus();\n    }\n    /**\n   * Remove focus trap from container\n   */ removeFocusTrap(container) {\n        const handleKeyDown = container.getAttribute(\"data-focus-trap-handler\");\n        if (handleKeyDown) {\n            container.removeEventListener(\"keydown\", handleKeyDown);\n        }\n        container.removeAttribute(\"data-focus-trap\");\n        container.removeAttribute(\"data-focus-trap-handler\");\n    }\n    /**\n   * Get focusable elements within container\n   */ getFocusableElements(container) {\n        const focusableSelectors = [\n            \"a[href]\",\n            \"button:not([disabled])\",\n            \"input:not([disabled])\",\n            \"select:not([disabled])\",\n            \"textarea:not([disabled])\",\n            '[tabindex]:not([tabindex=\"-1\"])',\n            '[contenteditable=\"true\"]'\n        ].join(\", \");\n        return Array.from(container.querySelectorAll(focusableSelectors)).filter((element)=>{\n            const el = element;\n            return el.offsetWidth > 0 && el.offsetHeight > 0 && !el.hidden;\n        });\n    }\n    /**\n   * Check if element is focusable\n   */ isFocusable(element) {\n        return this.getFocusableElements(document.body).includes(element);\n    }\n    constructor(){\n        this.focusStack = [];\n        this.trapStack = [];\n    }\n}\n/**\n * Keyboard navigation utilities\n */ class KeyboardNavigation {\n    /**\n   * Add keyboard shortcut\n   */ addShortcut(key, handler) {\n        let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        const shortcutKey = this.createShortcutKey(key, options);\n        this.keyHandlers.set(shortcutKey, handler);\n    }\n    /**\n   * Remove keyboard shortcut\n   */ removeShortcut(key) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const shortcutKey = this.createShortcutKey(key, options);\n        this.keyHandlers.delete(shortcutKey);\n    }\n    /**\n   * Handle keyboard events\n   */ handleKeyDown(event) {\n        if (!event.key) return;\n        const shortcutKey = this.createShortcutKey(event.key, {\n            ctrl: event.ctrlKey,\n            alt: event.altKey,\n            shift: event.shiftKey,\n            meta: event.metaKey\n        });\n        const handler = this.keyHandlers.get(shortcutKey);\n        if (handler) {\n            event.preventDefault();\n            handler(event);\n        }\n    }\n    /**\n   * Create shortcut key string\n   */ createShortcutKey(key, options) {\n        if (!key) return \"\";\n        const modifiers = [];\n        if (options.ctrl) modifiers.push(\"ctrl\");\n        if (options.alt) modifiers.push(\"alt\");\n        if (options.shift) modifiers.push(\"shift\");\n        if (options.meta) modifiers.push(\"meta\");\n        return [\n            ...modifiers,\n            key.toLowerCase()\n        ].join(\"+\");\n    }\n    /**\n   * Setup arrow key navigation for container\n   */ setupArrowNavigation(container) {\n        let direction = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"vertical\";\n        const handleKeyDown = (event)=>{\n            const focusableElements = Array.from(container.querySelectorAll('[tabindex]:not([tabindex=\"-1\"]), button, input, select, textarea, a[href]'));\n            const currentIndex = focusableElements.indexOf(document.activeElement);\n            if (currentIndex === -1) return;\n            let nextIndex = currentIndex;\n            switch(direction){\n                case \"horizontal\":\n                    if (event.key === \"ArrowLeft\") nextIndex = Math.max(0, currentIndex - 1);\n                    if (event.key === \"ArrowRight\") nextIndex = Math.min(focusableElements.length - 1, currentIndex + 1);\n                    break;\n                case \"vertical\":\n                    if (event.key === \"ArrowUp\") nextIndex = Math.max(0, currentIndex - 1);\n                    if (event.key === \"ArrowDown\") nextIndex = Math.min(focusableElements.length - 1, currentIndex + 1);\n                    break;\n                case \"grid\":\n                    break;\n            }\n            if (nextIndex !== currentIndex) {\n                var _focusableElements_nextIndex;\n                event.preventDefault();\n                (_focusableElements_nextIndex = focusableElements[nextIndex]) === null || _focusableElements_nextIndex === void 0 ? void 0 : _focusableElements_nextIndex.focus();\n            }\n        };\n        container.addEventListener(\"keydown\", handleKeyDown);\n    }\n    constructor(){\n        this.keyHandlers = new Map();\n    }\n}\n/**\n * Color contrast utilities\n */ class ColorContrast {\n    /**\n   * Calculate relative luminance\n   */ getRelativeLuminance(color) {\n        const rgb = this.hexToRgb(color);\n        if (!rgb) return 0;\n        const [r, g, b] = rgb.map((c)=>{\n            c = c / 255;\n            return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);\n        });\n        return 0.2126 * r + 0.7152 * g + 0.0722 * b;\n    }\n    /**\n   * Convert hex color to RGB\n   */ hexToRgb(hex) {\n        const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\n        return result ? [\n            parseInt(result[1], 16),\n            parseInt(result[2], 16),\n            parseInt(result[3], 16)\n        ] : null;\n    }\n    /**\n   * Calculate contrast ratio between two colors\n   */ getContrastRatio(color1, color2) {\n        const lum1 = this.getRelativeLuminance(color1);\n        const lum2 = this.getRelativeLuminance(color2);\n        const lighter = Math.max(lum1, lum2);\n        const darker = Math.min(lum1, lum2);\n        return (lighter + 0.05) / (darker + 0.05);\n    }\n    /**\n   * Check if contrast ratio meets WCAG standards\n   */ meetsWCAG(color1, color2) {\n        let level = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"AA\", size = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : \"normal\";\n        const ratio = this.getContrastRatio(color1, color2);\n        const requirements = {\n            AA: {\n                normal: 4.5,\n                large: 3\n            },\n            AAA: {\n                normal: 7,\n                large: 4.5\n            }\n        };\n        return ratio >= requirements[level][size];\n    }\n    /**\n   * Suggest accessible color alternatives\n   */ suggestAccessibleColor(foreground, background) {\n        let level = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"AA\";\n        const targetRatio = level === \"AA\" ? 4.5 : 7;\n        const currentRatio = this.getContrastRatio(foreground, background);\n        if (currentRatio >= targetRatio) return foreground;\n        // Simple approach: darken or lighten the foreground color\n        const rgb = this.hexToRgb(foreground);\n        if (!rgb) return foreground;\n        let [r, g, b] = rgb;\n        const step = currentRatio < targetRatio ? -10 : 10;\n        while(this.getContrastRatio(this.rgbToHex(r, g, b), background) < targetRatio){\n            r = Math.max(0, Math.min(255, r + step));\n            g = Math.max(0, Math.min(255, g + step));\n            b = Math.max(0, Math.min(255, b + step));\n            if (r === 0 && g === 0 && b === 0 || r === 255 && g === 255 && b === 255) {\n                break;\n            }\n        }\n        return this.rgbToHex(r, g, b);\n    }\n    /**\n   * Convert RGB to hex\n   */ rgbToHex(r, g, b) {\n        return \"#\".concat(((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1));\n    }\n}\n/**\n * Motion preferences utilities\n */ class MotionPreferences {\n    /**\n   * Check if user prefers reduced motion\n   */ prefersReducedMotion() {\n        if (false) {}\n        return window.matchMedia(\"(prefers-reduced-motion: reduce)\").matches;\n    }\n    /**\n   * Apply motion preferences to element\n   */ applyMotionPreferences(element) {\n        if (this.prefersReducedMotion()) {\n            element.style.animation = \"none\";\n            element.style.transition = \"none\";\n        }\n    }\n    /**\n   * Get safe animation duration\n   */ getSafeAnimationDuration(defaultDuration) {\n        return this.prefersReducedMotion() ? 0 : defaultDuration;\n    }\n}\n// Global instances\nconst screenReaderAnnouncer = new ScreenReaderAnnouncer();\nconst focusManager = new FocusManager();\nconst keyboardNavigation = new KeyboardNavigation();\nconst colorContrast = new ColorContrast();\nconst motionPreferences = new MotionPreferences();\n/**\n * Initialize accessibility features\n */ function initializeA11y() {\n    let config = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const mergedConfig = {\n        ...defaultA11yConfig,\n        ...config\n    };\n    if (false) {}\n    // Setup keyboard navigation\n    if (mergedConfig.enableKeyboardNavigation) {\n        document.addEventListener(\"keydown\", (event)=>{\n            keyboardNavigation.handleKeyDown(event);\n        });\n    }\n    // Setup skip links\n    setupSkipLinks();\n    // Setup focus indicators\n    setupFocusIndicators();\n    // Setup reduced motion\n    if (mergedConfig.enableReducedMotion) {\n        setupReducedMotion();\n    }\n}\n/**\n * Setup skip links for keyboard navigation\n */ function setupSkipLinks() {\n    const skipLink = document.createElement(\"a\");\n    skipLink.href = \"#main-content\";\n    skipLink.textContent = \"Skip to main content\";\n    skipLink.className = \"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-primary-600 focus:text-white focus:rounded\";\n    document.body.insertBefore(skipLink, document.body.firstChild);\n}\n/**\n * Setup focus indicators\n */ function setupFocusIndicators() {\n    const style = document.createElement(\"style\");\n    style.textContent = \"\\n    .focus-visible {\\n      outline: 2px solid #3b82f6;\\n      outline-offset: 2px;\\n    }\\n    \\n    .focus-visible:not(.focus-visible-force) {\\n      outline: none;\\n    }\\n    \\n    .focus-visible-force {\\n      outline: 2px solid #3b82f6 !important;\\n      outline-offset: 2px !important;\\n    }\\n  \";\n    document.head.appendChild(style);\n}\n/**\n * Setup reduced motion preferences\n */ function setupReducedMotion() {\n    if (motionPreferences.prefersReducedMotion()) {\n        const style = document.createElement(\"style\");\n        style.textContent = \"\\n      *, *::before, *::after {\\n        animation-duration: 0.01ms !important;\\n        animation-iteration-count: 1 !important;\\n        transition-duration: 0.01ms !important;\\n        scroll-behavior: auto !important;\\n      }\\n    \";\n        document.head.appendChild(style);\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2xpYi9hY2Nlc3NpYmlsaXR5L2ExMXktdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7O0NBRUMsR0FjTSxNQUFNQSxvQkFBZ0M7SUFDM0NDLHFCQUFxQjtJQUNyQkMsb0JBQW9CO0lBQ3BCQyx1QkFBdUI7SUFDdkJDLDBCQUEwQjtJQUMxQkMsdUJBQXVCO0lBQ3ZCQyxvQkFBb0I7SUFDcEJDLHFCQUFxQjtJQUNyQkMsb0JBQW9CO0lBQ3BCQyxzQkFBc0I7QUFDeEIsRUFBRTtBQUVGOztDQUVDLEdBQ00sTUFBTUM7SUFVWDs7R0FFQyxHQUNELG9CQUFrQztRQUNoQyxpREFBaUQ7UUFDakQsSUFBSSxDQUFDRSxVQUFVLEdBQUdDLFNBQVNDLGFBQWEsQ0FBQztRQUN6QyxJQUFJLENBQUNGLFVBQVUsQ0FBQ0csWUFBWSxDQUFDLGFBQWE7UUFDMUMsSUFBSSxDQUFDSCxVQUFVLENBQUNHLFlBQVksQ0FBQyxlQUFlO1FBQzVDLElBQUksQ0FBQ0gsVUFBVSxDQUFDRyxZQUFZLENBQUMsU0FBUztRQUN0QyxJQUFJLENBQUNILFVBQVUsQ0FBQ0ksS0FBSyxDQUFDQyxPQUFPLEdBQUk7UUFXakNKLFNBQVNLLElBQUksQ0FBQ0MsV0FBVyxDQUFDLElBQUksQ0FBQ1AsVUFBVTtRQUV6QyxrREFBa0Q7UUFDbEQsSUFBSSxDQUFDUSxZQUFZLEdBQUdQLFNBQVNDLGFBQWEsQ0FBQztRQUMzQyxJQUFJLENBQUNNLFlBQVksQ0FBQ0wsWUFBWSxDQUFDLGFBQWE7UUFDNUMsSUFBSSxDQUFDSyxZQUFZLENBQUNMLFlBQVksQ0FBQyxlQUFlO1FBQzlDLElBQUksQ0FBQ0ssWUFBWSxDQUFDTCxZQUFZLENBQUMsU0FBUztRQUN4QyxJQUFJLENBQUNLLFlBQVksQ0FBQ0osS0FBSyxDQUFDQyxPQUFPLEdBQUcsSUFBSSxDQUFDTCxVQUFVLENBQUNJLEtBQUssQ0FBQ0MsT0FBTztRQUMvREosU0FBU0ssSUFBSSxDQUFDQyxXQUFXLENBQUMsSUFBSSxDQUFDQyxZQUFZO0lBQzdDO0lBRUE7O0dBRUMsR0FDREMsU0FBU0MsT0FBZSxFQUFxRDtZQUFuREMsV0FBQUEsaUVBQW1DO1FBQzNELElBQUksQ0FBQ0QsUUFBUUUsSUFBSSxJQUFJO1FBRXJCLE1BQU1DLFNBQVNGLGFBQWEsY0FBYyxJQUFJLENBQUNYLFVBQVUsR0FBRyxJQUFJLENBQUNRLFlBQVk7UUFDN0UsSUFBSSxDQUFDSyxRQUFRO1FBRWIseUJBQXlCO1FBQ3pCQSxPQUFPQyxXQUFXLEdBQUc7UUFFckIsK0RBQStEO1FBQy9EQyxXQUFXO1lBQ1RGLE9BQU9DLFdBQVcsR0FBR0o7UUFDdkIsR0FBRztRQUVILG1DQUFtQztRQUNuQ0ssV0FBVztZQUNURixPQUFPQyxXQUFXLEdBQUc7UUFDdkIsR0FBRztJQUNMO0lBRUE7O0dBRUMsR0FDREUsbUJBQW1CQyxRQUFnQixFQUFRO1FBQ3pDLElBQUksQ0FBQ1IsUUFBUSxDQUFDLGdCQUF5QixPQUFUUSxXQUFZO0lBQzVDO0lBRUE7O0dBRUMsR0FDRDNCLG1CQUFtQjRCLE1BQWdCLEVBQVE7UUFDekMsSUFBSUEsT0FBT0MsTUFBTSxLQUFLLEdBQUc7UUFFekIsTUFBTVQsVUFBVVEsT0FBT0MsTUFBTSxLQUFLLElBQzlCLGVBQXlCLE9BQVZELE1BQU0sQ0FBQyxFQUFFLElBQ3hCLFlBQXFDQSxPQUF6QkEsT0FBT0MsTUFBTSxFQUFDLGFBQTZCLE9BQWxCRCxPQUFPRSxJQUFJLENBQUM7UUFFckQsSUFBSSxDQUFDWCxRQUFRLENBQUNDLFNBQVM7SUFDekI7SUFFQTs7R0FFQyxHQUNEVyxlQUFlQyxNQUFjLEVBQWlFO1lBQS9EQyxPQUFBQSxpRUFBaUQ7UUFDOUUsTUFBTUMsU0FBUztZQUNiQyxTQUFTO1lBQ1RDLE9BQU87WUFDUEMsU0FBUztZQUNUQyxNQUFNO1FBQ1IsQ0FBQyxDQUFDTCxLQUFLO1FBRVAsSUFBSSxDQUFDZCxRQUFRLENBQUMsR0FBYWEsT0FBVkUsUUFBTyxLQUFVLE9BQVBGLFNBQVVDLFNBQVMsVUFBVSxjQUFjO0lBQ3hFO0lBRUE7O0dBRUMsR0FDRE0sVUFBZ0I7UUFDZCxJQUFJLElBQUksQ0FBQzdCLFVBQVUsRUFBRTtZQUNuQkMsU0FBU0ssSUFBSSxDQUFDd0IsV0FBVyxDQUFDLElBQUksQ0FBQzlCLFVBQVU7WUFDekMsSUFBSSxDQUFDQSxVQUFVLEdBQUc7UUFDcEI7UUFDQSxJQUFJLElBQUksQ0FBQ1EsWUFBWSxFQUFFO1lBQ3JCUCxTQUFTSyxJQUFJLENBQUN3QixXQUFXLENBQUMsSUFBSSxDQUFDdEIsWUFBWTtZQUMzQyxJQUFJLENBQUNBLFlBQVksR0FBRztRQUN0QjtJQUNGO0lBMUdBdUIsYUFBYzthQUhOL0IsYUFBaUM7YUFDakNRLGVBQW1DO1FBR3pDLElBQUksSUFBa0IsRUFBYTtZQUNqQyxJQUFJLENBQUNULGlCQUFpQjtRQUN4QjtJQUNGO0FBdUdGO0FBRUE7O0NBRUMsR0FDTSxNQUFNaUM7SUFJWDs7R0FFQyxHQUNEQyxTQUFTQyxPQUE2QixFQUFFekIsUUFBaUIsRUFBUTtRQUMvRCxNQUFNMEIsU0FBUyxPQUFPRCxZQUFZLFdBQzlCakMsU0FBU21DLGFBQWEsQ0FBQ0YsV0FDdkJBO1FBRUosSUFBSSxDQUFDQyxRQUFRO1FBRWIsdUJBQXVCO1FBQ3ZCLE1BQU1FLGdCQUFnQnBDLFNBQVNvQyxhQUFhO1FBQzVDLElBQUlBLGlCQUFpQkEsa0JBQWtCRixRQUFRO1lBQzdDLElBQUksQ0FBQ0csVUFBVSxDQUFDQyxJQUFJLENBQUNGO1FBQ3ZCO1FBRUEsWUFBWTtRQUNaRixPQUFPSyxLQUFLO1FBRVosdUJBQXVCO1FBQ3ZCLElBQUkvQixVQUFVO1lBQ1pnQyxzQkFBc0JoQyxRQUFRLENBQUNBO1FBQ2pDO0lBQ0Y7SUFFQTs7R0FFQyxHQUNEaUMsZUFBcUI7UUFDbkIsTUFBTUMsa0JBQWtCLElBQUksQ0FBQ0wsVUFBVSxDQUFDTSxHQUFHO1FBQzNDLElBQUlELG1CQUFtQjFDLFNBQVM0QyxRQUFRLENBQUNGLGtCQUFrQjtZQUN6REEsZ0JBQWdCSCxLQUFLO1FBQ3ZCO0lBQ0Y7SUFFQTs7R0FFQyxHQUNETSxVQUFVQyxTQUFzQixFQUFRO1FBQ3RDLElBQUksQ0FBQ0MsU0FBUyxDQUFDVCxJQUFJLENBQUNRO1FBQ3BCLElBQUksQ0FBQ0UsY0FBYyxDQUFDRjtJQUN0QjtJQUVBOztHQUVDLEdBQ0RHLG1CQUF5QjtRQUN2QixNQUFNSCxZQUFZLElBQUksQ0FBQ0MsU0FBUyxDQUFDSixHQUFHO1FBQ3BDLElBQUlHLFdBQVc7WUFDYixJQUFJLENBQUNJLGVBQWUsQ0FBQ0o7UUFDdkI7SUFDRjtJQUVBOztHQUVDLEdBQ0QsZUFBdUJBLFNBQXNCLEVBQVE7UUFDbkQsTUFBTUssb0JBQW9CLElBQUksQ0FBQ0Msb0JBQW9CLENBQUNOO1FBQ3BELElBQUlLLGtCQUFrQmpDLE1BQU0sS0FBSyxHQUFHO1FBRXBDLE1BQU1tQyxlQUFlRixpQkFBaUIsQ0FBQyxFQUFFO1FBQ3pDLE1BQU1HLGNBQWNILGlCQUFpQixDQUFDQSxrQkFBa0JqQyxNQUFNLEdBQUcsRUFBRTtRQUVuRSxNQUFNcUMsZ0JBQWdCLENBQUNDO1lBQ3JCLElBQUlBLE1BQU1DLEdBQUcsS0FBSyxPQUFPO1lBRXpCLElBQUlELE1BQU1FLFFBQVEsRUFBRTtnQkFDbEIsY0FBYztnQkFDZCxJQUFJMUQsU0FBU29DLGFBQWEsS0FBS2lCLGNBQWM7b0JBQzNDRyxNQUFNRyxjQUFjO29CQUNwQkwsd0JBQUFBLGtDQUFBQSxZQUFhZixLQUFLO2dCQUNwQjtZQUNGLE9BQU87Z0JBQ0wsTUFBTTtnQkFDTixJQUFJdkMsU0FBU29DLGFBQWEsS0FBS2tCLGFBQWE7b0JBQzFDRSxNQUFNRyxjQUFjO29CQUNwQk4seUJBQUFBLG1DQUFBQSxhQUFjZCxLQUFLO2dCQUNyQjtZQUNGO1FBQ0Y7UUFFQU8sVUFBVWMsZ0JBQWdCLENBQUMsV0FBV0w7UUFDdENULFVBQVU1QyxZQUFZLENBQUMsbUJBQW1CO1FBRTFDLG9CQUFvQjtRQUNwQm1ELHlCQUFBQSxtQ0FBQUEsYUFBY2QsS0FBSztJQUNyQjtJQUVBOztHQUVDLEdBQ0QsZ0JBQXdCTyxTQUFzQixFQUFRO1FBQ3BELE1BQU1TLGdCQUFnQlQsVUFBVWUsWUFBWSxDQUFDO1FBQzdDLElBQUlOLGVBQWU7WUFDakJULFVBQVVnQixtQkFBbUIsQ0FBQyxXQUFXUDtRQUMzQztRQUNBVCxVQUFVaUIsZUFBZSxDQUFDO1FBQzFCakIsVUFBVWlCLGVBQWUsQ0FBQztJQUM1QjtJQUVBOztHQUVDLEdBQ0QscUJBQTZCakIsU0FBc0IsRUFBaUI7UUFDbEUsTUFBTWtCLHFCQUFxQjtZQUN6QjtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtTQUNELENBQUM3QyxJQUFJLENBQUM7UUFFUCxPQUFPOEMsTUFBTUMsSUFBSSxDQUFDcEIsVUFBVXFCLGdCQUFnQixDQUFDSCxxQkFDMUNJLE1BQU0sQ0FBQ25DLENBQUFBO1lBQ04sTUFBTW9DLEtBQUtwQztZQUNYLE9BQU9vQyxHQUFHQyxXQUFXLEdBQUcsS0FBS0QsR0FBR0UsWUFBWSxHQUFHLEtBQUssQ0FBQ0YsR0FBR0csTUFBTTtRQUNoRTtJQUNKO0lBRUE7O0dBRUMsR0FDREMsWUFBWXhDLE9BQW9CLEVBQVc7UUFDekMsT0FBTyxJQUFJLENBQUNtQixvQkFBb0IsQ0FBQ3BELFNBQVNLLElBQUksRUFBRXFFLFFBQVEsQ0FBQ3pDO0lBQzNEOzthQWpJUUksYUFBNEIsRUFBRTthQUM5QlUsWUFBMkIsRUFBRTs7QUFpSXZDO0FBRUE7O0NBRUMsR0FDTSxNQUFNNEI7SUFHWDs7R0FFQyxHQUNEQyxZQUNFbkIsR0FBVyxFQUNYb0IsT0FBdUMsRUFPakM7WUFOTkMsVUFBQUEsaUVBS0ksQ0FBQztRQUVMLE1BQU1DLGNBQWMsSUFBSSxDQUFDQyxpQkFBaUIsQ0FBQ3ZCLEtBQUtxQjtRQUNoRCxJQUFJLENBQUNHLFdBQVcsQ0FBQ0MsR0FBRyxDQUFDSCxhQUFhRjtJQUNwQztJQUVBOztHQUVDLEdBQ0RNLGVBQWUxQixHQUFXLEVBS1o7WUFMY3FCLFVBQUFBLGlFQUt4QixDQUFDO1FBQ0gsTUFBTUMsY0FBYyxJQUFJLENBQUNDLGlCQUFpQixDQUFDdkIsS0FBS3FCO1FBQ2hELElBQUksQ0FBQ0csV0FBVyxDQUFDRyxNQUFNLENBQUNMO0lBQzFCO0lBRUE7O0dBRUMsR0FDRHhCLGNBQWNDLEtBQW9CLEVBQVE7UUFDeEMsSUFBSSxDQUFDQSxNQUFNQyxHQUFHLEVBQUU7UUFFaEIsTUFBTXNCLGNBQWMsSUFBSSxDQUFDQyxpQkFBaUIsQ0FBQ3hCLE1BQU1DLEdBQUcsRUFBRTtZQUNwRDRCLE1BQU03QixNQUFNOEIsT0FBTztZQUNuQkMsS0FBSy9CLE1BQU1nQyxNQUFNO1lBQ2pCQyxPQUFPakMsTUFBTUUsUUFBUTtZQUNyQmdDLE1BQU1sQyxNQUFNbUMsT0FBTztRQUNyQjtRQUVBLE1BQU1kLFVBQVUsSUFBSSxDQUFDSSxXQUFXLENBQUNXLEdBQUcsQ0FBQ2I7UUFDckMsSUFBSUYsU0FBUztZQUNYckIsTUFBTUcsY0FBYztZQUNwQmtCLFFBQVFyQjtRQUNWO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELGtCQUEwQkMsR0FBVyxFQUFFcUIsT0FLdEMsRUFBVTtRQUNULElBQUksQ0FBQ3JCLEtBQUssT0FBTztRQUVqQixNQUFNb0MsWUFBWSxFQUFFO1FBQ3BCLElBQUlmLFFBQVFPLElBQUksRUFBRVEsVUFBVXZELElBQUksQ0FBQztRQUNqQyxJQUFJd0MsUUFBUVMsR0FBRyxFQUFFTSxVQUFVdkQsSUFBSSxDQUFDO1FBQ2hDLElBQUl3QyxRQUFRVyxLQUFLLEVBQUVJLFVBQVV2RCxJQUFJLENBQUM7UUFDbEMsSUFBSXdDLFFBQVFZLElBQUksRUFBRUcsVUFBVXZELElBQUksQ0FBQztRQUVqQyxPQUFPO2VBQUl1RDtZQUFXcEMsSUFBSXFDLFdBQVc7U0FBRyxDQUFDM0UsSUFBSSxDQUFDO0lBQ2hEO0lBRUE7O0dBRUMsR0FDRDRFLHFCQUNFakQsU0FBc0IsRUFFaEI7WUFETmtELFlBQUFBLGlFQUFnRDtRQUVoRCxNQUFNekMsZ0JBQWdCLENBQUNDO1lBQ3JCLE1BQU1MLG9CQUFvQmMsTUFBTUMsSUFBSSxDQUNsQ3BCLFVBQVVxQixnQkFBZ0IsQ0FBQztZQUc3QixNQUFNOEIsZUFBZTlDLGtCQUFrQitDLE9BQU8sQ0FBQ2xHLFNBQVNvQyxhQUFhO1lBQ3JFLElBQUk2RCxpQkFBaUIsQ0FBQyxHQUFHO1lBRXpCLElBQUlFLFlBQVlGO1lBRWhCLE9BQVFEO2dCQUNOLEtBQUs7b0JBQ0gsSUFBSXhDLE1BQU1DLEdBQUcsS0FBSyxhQUFhMEMsWUFBWUMsS0FBS0MsR0FBRyxDQUFDLEdBQUdKLGVBQWU7b0JBQ3RFLElBQUl6QyxNQUFNQyxHQUFHLEtBQUssY0FBYzBDLFlBQVlDLEtBQUtFLEdBQUcsQ0FBQ25ELGtCQUFrQmpDLE1BQU0sR0FBRyxHQUFHK0UsZUFBZTtvQkFDbEc7Z0JBRUYsS0FBSztvQkFDSCxJQUFJekMsTUFBTUMsR0FBRyxLQUFLLFdBQVcwQyxZQUFZQyxLQUFLQyxHQUFHLENBQUMsR0FBR0osZUFBZTtvQkFDcEUsSUFBSXpDLE1BQU1DLEdBQUcsS0FBSyxhQUFhMEMsWUFBWUMsS0FBS0UsR0FBRyxDQUFDbkQsa0JBQWtCakMsTUFBTSxHQUFHLEdBQUcrRSxlQUFlO29CQUNqRztnQkFFRixLQUFLO29CQUdIO1lBQ0o7WUFFQSxJQUFJRSxjQUFjRixjQUFjO29CQUU5QjlDO2dCQURBSyxNQUFNRyxjQUFjO2lCQUNwQlIsK0JBQUFBLGlCQUFpQixDQUFDZ0QsVUFBVSxjQUE1QmhELG1EQUFBQSw2QkFBOEJaLEtBQUs7WUFDckM7UUFDRjtRQUVBTyxVQUFVYyxnQkFBZ0IsQ0FBQyxXQUFXTDtJQUN4Qzs7YUFqSFEwQixjQUEyRCxJQUFJc0I7O0FBa0h6RTtBQUVBOztDQUVDLEdBQ00sTUFBTUM7SUFDWDs7R0FFQyxHQUNELHFCQUE2QkUsS0FBYSxFQUFVO1FBQ2xELE1BQU1DLE1BQU0sSUFBSSxDQUFDQyxRQUFRLENBQUNGO1FBQzFCLElBQUksQ0FBQ0MsS0FBSyxPQUFPO1FBRWpCLE1BQU0sQ0FBQ0UsR0FBR0MsR0FBR0MsRUFBRSxHQUFHSixJQUFJSyxHQUFHLENBQUNDLENBQUFBO1lBQ3hCQSxJQUFJQSxJQUFJO1lBQ1IsT0FBT0EsS0FBSyxVQUFVQSxJQUFJLFFBQVFiLEtBQUtjLEdBQUcsQ0FBQyxDQUFDRCxJQUFJLEtBQUksSUFBSyxPQUFPO1FBQ2xFO1FBRUEsT0FBTyxTQUFTSixJQUFLLFNBQVNDLElBQUssU0FBU0M7SUFDOUM7SUFFQTs7R0FFQyxHQUNELFNBQWlCSSxHQUFXLEVBQW1DO1FBQzdELE1BQU1DLFNBQVMsNENBQTRDQyxJQUFJLENBQUNGO1FBQ2hFLE9BQU9DLFNBQVM7WUFDZEUsU0FBU0YsTUFBTSxDQUFDLEVBQUUsRUFBRztZQUNyQkUsU0FBU0YsTUFBTSxDQUFDLEVBQUUsRUFBRztZQUNyQkUsU0FBU0YsTUFBTSxDQUFDLEVBQUUsRUFBRztTQUN0QixHQUFHO0lBQ047SUFFQTs7R0FFQyxHQUNERyxpQkFBaUJDLE1BQWMsRUFBRUMsTUFBYyxFQUFVO1FBQ3ZELE1BQU1DLE9BQU8sSUFBSSxDQUFDakIsb0JBQW9CLENBQUNlO1FBQ3ZDLE1BQU1HLE9BQU8sSUFBSSxDQUFDbEIsb0JBQW9CLENBQUNnQjtRQUV2QyxNQUFNRyxVQUFVeEIsS0FBS0MsR0FBRyxDQUFDcUIsTUFBTUM7UUFDL0IsTUFBTUUsU0FBU3pCLEtBQUtFLEdBQUcsQ0FBQ29CLE1BQU1DO1FBRTlCLE9BQU8sQ0FBQ0MsVUFBVSxJQUFHLElBQU1DLENBQUFBLFNBQVMsSUFBRztJQUN6QztJQUVBOztHQUVDLEdBQ0RDLFVBQ0VOLE1BQWMsRUFDZEMsTUFBYyxFQUdMO1lBRlRNLFFBQUFBLGlFQUFzQixNQUN0QkMsT0FBQUEsaUVBQTJCO1FBRTNCLE1BQU1DLFFBQVEsSUFBSSxDQUFDVixnQkFBZ0IsQ0FBQ0MsUUFBUUM7UUFFNUMsTUFBTVMsZUFBZTtZQUNuQkMsSUFBSTtnQkFBRUMsUUFBUTtnQkFBS0MsT0FBTztZQUFFO1lBQzVCQyxLQUFLO2dCQUFFRixRQUFRO2dCQUFHQyxPQUFPO1lBQUk7UUFDL0I7UUFFQSxPQUFPSixTQUFTQyxZQUFZLENBQUNILE1BQU0sQ0FBQ0MsS0FBSztJQUMzQztJQUVBOztHQUVDLEdBQ0RPLHVCQUNFQyxVQUFrQixFQUNsQkMsVUFBa0IsRUFFVjtZQURSVixRQUFBQSxpRUFBc0I7UUFFdEIsTUFBTVcsY0FBY1gsVUFBVSxPQUFPLE1BQU07UUFDM0MsTUFBTVksZUFBZSxJQUFJLENBQUNwQixnQkFBZ0IsQ0FBQ2lCLFlBQVlDO1FBRXZELElBQUlFLGdCQUFnQkQsYUFBYSxPQUFPRjtRQUV4QywwREFBMEQ7UUFDMUQsTUFBTTdCLE1BQU0sSUFBSSxDQUFDQyxRQUFRLENBQUM0QjtRQUMxQixJQUFJLENBQUM3QixLQUFLLE9BQU82QjtRQUVqQixJQUFJLENBQUMzQixHQUFHQyxHQUFHQyxFQUFFLEdBQUdKO1FBQ2hCLE1BQU1pQyxPQUFPRCxlQUFlRCxjQUFjLENBQUMsS0FBSztRQUVoRCxNQUFPLElBQUksQ0FBQ25CLGdCQUFnQixDQUFDLElBQUksQ0FBQ3NCLFFBQVEsQ0FBQ2hDLEdBQUdDLEdBQUdDLElBQUkwQixjQUFjQyxZQUFhO1lBQzlFN0IsSUFBSVQsS0FBS0MsR0FBRyxDQUFDLEdBQUdELEtBQUtFLEdBQUcsQ0FBQyxLQUFLTyxJQUFJK0I7WUFDbEM5QixJQUFJVixLQUFLQyxHQUFHLENBQUMsR0FBR0QsS0FBS0UsR0FBRyxDQUFDLEtBQUtRLElBQUk4QjtZQUNsQzdCLElBQUlYLEtBQUtDLEdBQUcsQ0FBQyxHQUFHRCxLQUFLRSxHQUFHLENBQUMsS0FBS1MsSUFBSTZCO1lBRWxDLElBQUksTUFBTyxLQUFLOUIsTUFBTSxLQUFLQyxNQUFNLEtBQU9GLE1BQU0sT0FBT0MsTUFBTSxPQUFPQyxNQUFNLEtBQU07Z0JBQzVFO1lBQ0Y7UUFDRjtRQUVBLE9BQU8sSUFBSSxDQUFDOEIsUUFBUSxDQUFDaEMsR0FBR0MsR0FBR0M7SUFDN0I7SUFFQTs7R0FFQyxHQUNELFNBQWlCRixDQUFTLEVBQUVDLENBQVMsRUFBRUMsQ0FBUyxFQUFVO1FBQ3hELE9BQU8sSUFBaUUsT0FBN0QsQ0FBQyxDQUFDLEtBQUssRUFBQyxJQUFNRixDQUFBQSxLQUFLLEVBQUMsSUFBTUMsQ0FBQUEsS0FBSyxLQUFLQyxDQUFBQSxFQUFHK0IsUUFBUSxDQUFDLElBQUlDLEtBQUssQ0FBQztJQUN2RTtBQUNGO0FBRUE7O0NBRUMsR0FDTSxNQUFNQztJQUNYOztHQUVDLEdBQ0RDLHVCQUFnQztRQUM5QixJQUFJLEtBQWtCLEVBQWEsRUFBYTtRQUNoRCxPQUFPQyxPQUFPQyxVQUFVLENBQUMsb0NBQW9DQyxPQUFPO0lBQ3RFO0lBRUE7O0dBRUMsR0FDREMsdUJBQXVCcEgsT0FBb0IsRUFBUTtRQUNqRCxJQUFJLElBQUksQ0FBQ2dILG9CQUFvQixJQUFJO1lBQy9CaEgsUUFBUTlCLEtBQUssQ0FBQ21KLFNBQVMsR0FBRztZQUMxQnJILFFBQVE5QixLQUFLLENBQUNvSixVQUFVLEdBQUc7UUFDN0I7SUFDRjtJQUVBOztHQUVDLEdBQ0RDLHlCQUF5QkMsZUFBdUIsRUFBVTtRQUN4RCxPQUFPLElBQUksQ0FBQ1Isb0JBQW9CLEtBQUssSUFBSVE7SUFDM0M7QUFDRjtBQUVBLG1CQUFtQjtBQUNaLE1BQU1qSCx3QkFBd0IsSUFBSTNDLHdCQUF3QjtBQUMxRCxNQUFNNkosZUFBZSxJQUFJM0gsZUFBZTtBQUN4QyxNQUFNNEgscUJBQXFCLElBQUloRixxQkFBcUI7QUFDcEQsTUFBTWlGLGdCQUFnQixJQUFJcEQsZ0JBQWdCO0FBQzFDLE1BQU1xRCxvQkFBb0IsSUFBSWIsb0JBQW9CO0FBRXpEOztDQUVDLEdBQ00sU0FBU2M7UUFBZUMsU0FBQUEsaUVBQThCLENBQUM7SUFDNUQsTUFBTUMsZUFBZTtRQUFFLEdBQUc3SyxpQkFBaUI7UUFBRSxHQUFHNEssTUFBTTtJQUFDO0lBRXZELElBQUksS0FBa0IsRUFBYSxFQUFPO0lBRTFDLDRCQUE0QjtJQUM1QixJQUFJQyxhQUFhekssd0JBQXdCLEVBQUU7UUFDekNTLFNBQVM0RCxnQkFBZ0IsQ0FBQyxXQUFXLENBQUNKO1lBQ3BDbUcsbUJBQW1CcEcsYUFBYSxDQUFDQztRQUNuQztJQUNGO0lBRUEsbUJBQW1CO0lBQ25CeUc7SUFFQSx5QkFBeUI7SUFDekJDO0lBRUEsdUJBQXVCO0lBQ3ZCLElBQUlGLGFBQWF0SyxtQkFBbUIsRUFBRTtRQUNwQ3lLO0lBQ0Y7QUFDRjtBQUVBOztDQUVDLEdBQ0QsU0FBU0Y7SUFDUCxNQUFNRyxXQUFXcEssU0FBU0MsYUFBYSxDQUFDO0lBQ3hDbUssU0FBU0MsSUFBSSxHQUFHO0lBQ2hCRCxTQUFTdkosV0FBVyxHQUFHO0lBQ3ZCdUosU0FBU0UsU0FBUyxHQUFHO0lBRXJCdEssU0FBU0ssSUFBSSxDQUFDa0ssWUFBWSxDQUFDSCxVQUFVcEssU0FBU0ssSUFBSSxDQUFDbUssVUFBVTtBQUMvRDtBQUVBOztDQUVDLEdBQ0QsU0FBU047SUFDUCxNQUFNL0osUUFBUUgsU0FBU0MsYUFBYSxDQUFDO0lBQ3JDRSxNQUFNVSxXQUFXLEdBQUk7SUFlckJiLFNBQVN5SyxJQUFJLENBQUNuSyxXQUFXLENBQUNIO0FBQzVCO0FBRUE7O0NBRUMsR0FDRCxTQUFTZ0s7SUFDUCxJQUFJTixrQkFBa0JaLG9CQUFvQixJQUFJO1FBQzVDLE1BQU05SSxRQUFRSCxTQUFTQyxhQUFhLENBQUM7UUFDckNFLE1BQU1VLFdBQVcsR0FBSTtRQVFyQmIsU0FBU3lLLElBQUksQ0FBQ25LLFdBQVcsQ0FBQ0g7SUFDNUI7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9saWIvYWNjZXNzaWJpbGl0eS9hMTF5LXV0aWxzLnRzPzcyODEiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBBY2Nlc3NpYmlsaXR5IHV0aWxpdGllcyBmb3IgV0NBRyAyLjEgQUEgY29tcGxpYW5jZVxuICovXG5cbmV4cG9ydCBpbnRlcmZhY2UgQTExeUNvbmZpZyB7XG4gIGFubm91bmNlUGFnZUNoYW5nZXM6IGJvb2xlYW47XG4gIGFubm91bmNlRm9ybUVycm9yczogYm9vbGVhbjtcbiAgYW5ub3VuY2VTdGF0dXNVcGRhdGVzOiBib29sZWFuO1xuICBlbmFibGVLZXlib2FyZE5hdmlnYXRpb246IGJvb2xlYW47XG4gIGVuYWJsZUZvY3VzTWFuYWdlbWVudDogYm9vbGVhbjtcbiAgZW5hYmxlSGlnaENvbnRyYXN0OiBib29sZWFuO1xuICBlbmFibGVSZWR1Y2VkTW90aW9uOiBib29sZWFuO1xuICBtaW5pbXVtVG91Y2hUYXJnZXQ6IG51bWJlcjtcbiAgbWluaW11bUNvbG9yQ29udHJhc3Q6IG51bWJlcjtcbn1cblxuZXhwb3J0IGNvbnN0IGRlZmF1bHRBMTF5Q29uZmlnOiBBMTF5Q29uZmlnID0ge1xuICBhbm5vdW5jZVBhZ2VDaGFuZ2VzOiB0cnVlLFxuICBhbm5vdW5jZUZvcm1FcnJvcnM6IHRydWUsXG4gIGFubm91bmNlU3RhdHVzVXBkYXRlczogdHJ1ZSxcbiAgZW5hYmxlS2V5Ym9hcmROYXZpZ2F0aW9uOiB0cnVlLFxuICBlbmFibGVGb2N1c01hbmFnZW1lbnQ6IHRydWUsXG4gIGVuYWJsZUhpZ2hDb250cmFzdDogdHJ1ZSxcbiAgZW5hYmxlUmVkdWNlZE1vdGlvbjogdHJ1ZSxcbiAgbWluaW11bVRvdWNoVGFyZ2V0OiA0NCxcbiAgbWluaW11bUNvbG9yQ29udHJhc3Q6IDQuNSxcbn07XG5cbi8qKlxuICogU2NyZWVuIHJlYWRlciBhbm5vdW5jZW1lbnRzXG4gKi9cbmV4cG9ydCBjbGFzcyBTY3JlZW5SZWFkZXJBbm5vdW5jZXIge1xuICBwcml2YXRlIGxpdmVSZWdpb246IEhUTUxFbGVtZW50IHwgbnVsbCA9IG51bGw7XG4gIHByaXZhdGUgcG9saXRlUmVnaW9uOiBIVE1MRWxlbWVudCB8IG51bGwgPSBudWxsO1xuXG4gIGNvbnN0cnVjdG9yKCkge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgdGhpcy5jcmVhdGVMaXZlUmVnaW9ucygpO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBDcmVhdGUgQVJJQSBsaXZlIHJlZ2lvbnMgZm9yIGFubm91bmNlbWVudHNcbiAgICovXG4gIHByaXZhdGUgY3JlYXRlTGl2ZVJlZ2lvbnMoKTogdm9pZCB7XG4gICAgLy8gQXNzZXJ0aXZlIGxpdmUgcmVnaW9uIGZvciB1cmdlbnQgYW5ub3VuY2VtZW50c1xuICAgIHRoaXMubGl2ZVJlZ2lvbiA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2RpdicpO1xuICAgIHRoaXMubGl2ZVJlZ2lvbi5zZXRBdHRyaWJ1dGUoJ2FyaWEtbGl2ZScsICdhc3NlcnRpdmUnKTtcbiAgICB0aGlzLmxpdmVSZWdpb24uc2V0QXR0cmlidXRlKCdhcmlhLWF0b21pYycsICd0cnVlJyk7XG4gICAgdGhpcy5saXZlUmVnaW9uLnNldEF0dHJpYnV0ZSgnY2xhc3MnLCAnc3Itb25seScpO1xuICAgIHRoaXMubGl2ZVJlZ2lvbi5zdHlsZS5jc3NUZXh0ID0gYFxuICAgICAgcG9zaXRpb246IGFic29sdXRlICFpbXBvcnRhbnQ7XG4gICAgICB3aWR0aDogMXB4ICFpbXBvcnRhbnQ7XG4gICAgICBoZWlnaHQ6IDFweCAhaW1wb3J0YW50O1xuICAgICAgcGFkZGluZzogMCAhaW1wb3J0YW50O1xuICAgICAgbWFyZ2luOiAtMXB4ICFpbXBvcnRhbnQ7XG4gICAgICBvdmVyZmxvdzogaGlkZGVuICFpbXBvcnRhbnQ7XG4gICAgICBjbGlwOiByZWN0KDAsIDAsIDAsIDApICFpbXBvcnRhbnQ7XG4gICAgICB3aGl0ZS1zcGFjZTogbm93cmFwICFpbXBvcnRhbnQ7XG4gICAgICBib3JkZXI6IDAgIWltcG9ydGFudDtcbiAgICBgO1xuICAgIGRvY3VtZW50LmJvZHkuYXBwZW5kQ2hpbGQodGhpcy5saXZlUmVnaW9uKTtcblxuICAgIC8vIFBvbGl0ZSBsaXZlIHJlZ2lvbiBmb3Igbm9uLXVyZ2VudCBhbm5vdW5jZW1lbnRzXG4gICAgdGhpcy5wb2xpdGVSZWdpb24gPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdkaXYnKTtcbiAgICB0aGlzLnBvbGl0ZVJlZ2lvbi5zZXRBdHRyaWJ1dGUoJ2FyaWEtbGl2ZScsICdwb2xpdGUnKTtcbiAgICB0aGlzLnBvbGl0ZVJlZ2lvbi5zZXRBdHRyaWJ1dGUoJ2FyaWEtYXRvbWljJywgJ3RydWUnKTtcbiAgICB0aGlzLnBvbGl0ZVJlZ2lvbi5zZXRBdHRyaWJ1dGUoJ2NsYXNzJywgJ3NyLW9ubHknKTtcbiAgICB0aGlzLnBvbGl0ZVJlZ2lvbi5zdHlsZS5jc3NUZXh0ID0gdGhpcy5saXZlUmVnaW9uLnN0eWxlLmNzc1RleHQ7XG4gICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZCh0aGlzLnBvbGl0ZVJlZ2lvbik7XG4gIH1cblxuICAvKipcbiAgICogQW5ub3VuY2UgbWVzc2FnZSB0byBzY3JlZW4gcmVhZGVyc1xuICAgKi9cbiAgYW5ub3VuY2UobWVzc2FnZTogc3RyaW5nLCBwcmlvcml0eTogJ3BvbGl0ZScgfCAnYXNzZXJ0aXZlJyA9ICdwb2xpdGUnKTogdm9pZCB7XG4gICAgaWYgKCFtZXNzYWdlLnRyaW0oKSkgcmV0dXJuO1xuXG4gICAgY29uc3QgcmVnaW9uID0gcHJpb3JpdHkgPT09ICdhc3NlcnRpdmUnID8gdGhpcy5saXZlUmVnaW9uIDogdGhpcy5wb2xpdGVSZWdpb247XG4gICAgaWYgKCFyZWdpb24pIHJldHVybjtcblxuICAgIC8vIENsZWFyIHByZXZpb3VzIG1lc3NhZ2VcbiAgICByZWdpb24udGV4dENvbnRlbnQgPSAnJztcblxuICAgIC8vIEFkZCBuZXcgbWVzc2FnZSBhZnRlciBhIGJyaWVmIGRlbGF5IHRvIGVuc3VyZSBpdCdzIGFubm91bmNlZFxuICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgcmVnaW9uLnRleHRDb250ZW50ID0gbWVzc2FnZTtcbiAgICB9LCAxMDApO1xuXG4gICAgLy8gQ2xlYXIgbWVzc2FnZSBhZnRlciBhbm5vdW5jZW1lbnRcbiAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgIHJlZ2lvbi50ZXh0Q29udGVudCA9ICcnO1xuICAgIH0sIDEwMDApO1xuICB9XG5cbiAgLyoqXG4gICAqIEFubm91bmNlIHBhZ2UgY2hhbmdlXG4gICAqL1xuICBhbm5vdW5jZVBhZ2VDaGFuZ2UocGFnZU5hbWU6IHN0cmluZyk6IHZvaWQge1xuICAgIHRoaXMuYW5ub3VuY2UoYE5hdmlnYXRlZCB0byAke3BhZ2VOYW1lfWAsICdwb2xpdGUnKTtcbiAgfVxuXG4gIC8qKlxuICAgKiBBbm5vdW5jZSBmb3JtIGVycm9yc1xuICAgKi9cbiAgYW5ub3VuY2VGb3JtRXJyb3JzKGVycm9yczogc3RyaW5nW10pOiB2b2lkIHtcbiAgICBpZiAoZXJyb3JzLmxlbmd0aCA9PT0gMCkgcmV0dXJuO1xuXG4gICAgY29uc3QgbWVzc2FnZSA9IGVycm9ycy5sZW5ndGggPT09IDFcbiAgICAgID8gYEZvcm0gZXJyb3I6ICR7ZXJyb3JzWzBdfWBcbiAgICAgIDogYEZvcm0gaGFzICR7ZXJyb3JzLmxlbmd0aH0gZXJyb3JzOiAke2Vycm9ycy5qb2luKCcsICcpfWA7XG5cbiAgICB0aGlzLmFubm91bmNlKG1lc3NhZ2UsICdhc3NlcnRpdmUnKTtcbiAgfVxuXG4gIC8qKlxuICAgKiBBbm5vdW5jZSBzdGF0dXMgdXBkYXRlc1xuICAgKi9cbiAgYW5ub3VuY2VTdGF0dXMoc3RhdHVzOiBzdHJpbmcsIHR5cGU6ICdzdWNjZXNzJyB8ICdlcnJvcicgfCAnd2FybmluZycgfCAnaW5mbycgPSAnaW5mbycpOiB2b2lkIHtcbiAgICBjb25zdCBwcmVmaXggPSB7XG4gICAgICBzdWNjZXNzOiAnU3VjY2VzczonLFxuICAgICAgZXJyb3I6ICdFcnJvcjonLFxuICAgICAgd2FybmluZzogJ1dhcm5pbmc6JyxcbiAgICAgIGluZm86ICdJbmZvOicsXG4gICAgfVt0eXBlXTtcblxuICAgIHRoaXMuYW5ub3VuY2UoYCR7cHJlZml4fSAke3N0YXR1c31gLCB0eXBlID09PSAnZXJyb3InID8gJ2Fzc2VydGl2ZScgOiAncG9saXRlJyk7XG4gIH1cblxuICAvKipcbiAgICogQ2xlYW51cFxuICAgKi9cbiAgZGVzdHJveSgpOiB2b2lkIHtcbiAgICBpZiAodGhpcy5saXZlUmVnaW9uKSB7XG4gICAgICBkb2N1bWVudC5ib2R5LnJlbW92ZUNoaWxkKHRoaXMubGl2ZVJlZ2lvbik7XG4gICAgICB0aGlzLmxpdmVSZWdpb24gPSBudWxsO1xuICAgIH1cbiAgICBpZiAodGhpcy5wb2xpdGVSZWdpb24pIHtcbiAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQodGhpcy5wb2xpdGVSZWdpb24pO1xuICAgICAgdGhpcy5wb2xpdGVSZWdpb24gPSBudWxsO1xuICAgIH1cbiAgfVxufVxuXG4vKipcbiAqIEZvY3VzIG1hbmFnZW1lbnQgdXRpbGl0aWVzXG4gKi9cbmV4cG9ydCBjbGFzcyBGb2N1c01hbmFnZXIge1xuICBwcml2YXRlIGZvY3VzU3RhY2s6IEhUTUxFbGVtZW50W10gPSBbXTtcbiAgcHJpdmF0ZSB0cmFwU3RhY2s6IEhUTUxFbGVtZW50W10gPSBbXTtcblxuICAvKipcbiAgICogU2V0IGZvY3VzIHRvIGVsZW1lbnQgd2l0aCBvcHRpb25hbCBhbm5vdW5jZW1lbnRcbiAgICovXG4gIHNldEZvY3VzKGVsZW1lbnQ6IEhUTUxFbGVtZW50IHwgc3RyaW5nLCBhbm5vdW5jZT86IHN0cmluZyk6IHZvaWQge1xuICAgIGNvbnN0IHRhcmdldCA9IHR5cGVvZiBlbGVtZW50ID09PSAnc3RyaW5nJyBcbiAgICAgID8gZG9jdW1lbnQucXVlcnlTZWxlY3RvcihlbGVtZW50KSBhcyBIVE1MRWxlbWVudFxuICAgICAgOiBlbGVtZW50O1xuXG4gICAgaWYgKCF0YXJnZXQpIHJldHVybjtcblxuICAgIC8vIFN0b3JlIHByZXZpb3VzIGZvY3VzXG4gICAgY29uc3QgYWN0aXZlRWxlbWVudCA9IGRvY3VtZW50LmFjdGl2ZUVsZW1lbnQgYXMgSFRNTEVsZW1lbnQ7XG4gICAgaWYgKGFjdGl2ZUVsZW1lbnQgJiYgYWN0aXZlRWxlbWVudCAhPT0gdGFyZ2V0KSB7XG4gICAgICB0aGlzLmZvY3VzU3RhY2sucHVzaChhY3RpdmVFbGVtZW50KTtcbiAgICB9XG5cbiAgICAvLyBTZXQgZm9jdXNcbiAgICB0YXJnZXQuZm9jdXMoKTtcblxuICAgIC8vIEFubm91bmNlIGlmIHByb3ZpZGVkXG4gICAgaWYgKGFubm91bmNlKSB7XG4gICAgICBzY3JlZW5SZWFkZXJBbm5vdW5jZXIuYW5ub3VuY2UoYW5ub3VuY2UpO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBSZXN0b3JlIHByZXZpb3VzIGZvY3VzXG4gICAqL1xuICByZXN0b3JlRm9jdXMoKTogdm9pZCB7XG4gICAgY29uc3QgcHJldmlvdXNFbGVtZW50ID0gdGhpcy5mb2N1c1N0YWNrLnBvcCgpO1xuICAgIGlmIChwcmV2aW91c0VsZW1lbnQgJiYgZG9jdW1lbnQuY29udGFpbnMocHJldmlvdXNFbGVtZW50KSkge1xuICAgICAgcHJldmlvdXNFbGVtZW50LmZvY3VzKCk7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFRyYXAgZm9jdXMgd2l0aGluIGNvbnRhaW5lclxuICAgKi9cbiAgdHJhcEZvY3VzKGNvbnRhaW5lcjogSFRNTEVsZW1lbnQpOiB2b2lkIHtcbiAgICB0aGlzLnRyYXBTdGFjay5wdXNoKGNvbnRhaW5lcik7XG4gICAgdGhpcy5zZXR1cEZvY3VzVHJhcChjb250YWluZXIpO1xuICB9XG5cbiAgLyoqXG4gICAqIFJlbGVhc2UgZm9jdXMgdHJhcFxuICAgKi9cbiAgcmVsZWFzZUZvY3VzVHJhcCgpOiB2b2lkIHtcbiAgICBjb25zdCBjb250YWluZXIgPSB0aGlzLnRyYXBTdGFjay5wb3AoKTtcbiAgICBpZiAoY29udGFpbmVyKSB7XG4gICAgICB0aGlzLnJlbW92ZUZvY3VzVHJhcChjb250YWluZXIpO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBTZXR1cCBmb2N1cyB0cmFwIGZvciBjb250YWluZXJcbiAgICovXG4gIHByaXZhdGUgc2V0dXBGb2N1c1RyYXAoY29udGFpbmVyOiBIVE1MRWxlbWVudCk6IHZvaWQge1xuICAgIGNvbnN0IGZvY3VzYWJsZUVsZW1lbnRzID0gdGhpcy5nZXRGb2N1c2FibGVFbGVtZW50cyhjb250YWluZXIpO1xuICAgIGlmIChmb2N1c2FibGVFbGVtZW50cy5sZW5ndGggPT09IDApIHJldHVybjtcblxuICAgIGNvbnN0IGZpcnN0RWxlbWVudCA9IGZvY3VzYWJsZUVsZW1lbnRzWzBdO1xuICAgIGNvbnN0IGxhc3RFbGVtZW50ID0gZm9jdXNhYmxlRWxlbWVudHNbZm9jdXNhYmxlRWxlbWVudHMubGVuZ3RoIC0gMV07XG5cbiAgICBjb25zdCBoYW5kbGVLZXlEb3duID0gKGV2ZW50OiBLZXlib2FyZEV2ZW50KSA9PiB7XG4gICAgICBpZiAoZXZlbnQua2V5ICE9PSAnVGFiJykgcmV0dXJuO1xuXG4gICAgICBpZiAoZXZlbnQuc2hpZnRLZXkpIHtcbiAgICAgICAgLy8gU2hpZnQgKyBUYWJcbiAgICAgICAgaWYgKGRvY3VtZW50LmFjdGl2ZUVsZW1lbnQgPT09IGZpcnN0RWxlbWVudCkge1xuICAgICAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgbGFzdEVsZW1lbnQ/LmZvY3VzKCk7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8vIFRhYlxuICAgICAgICBpZiAoZG9jdW1lbnQuYWN0aXZlRWxlbWVudCA9PT0gbGFzdEVsZW1lbnQpIHtcbiAgICAgICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICAgIGZpcnN0RWxlbWVudD8uZm9jdXMoKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH07XG5cbiAgICBjb250YWluZXIuYWRkRXZlbnRMaXN0ZW5lcigna2V5ZG93bicsIGhhbmRsZUtleURvd24pO1xuICAgIGNvbnRhaW5lci5zZXRBdHRyaWJ1dGUoJ2RhdGEtZm9jdXMtdHJhcCcsICd0cnVlJyk7XG5cbiAgICAvLyBTZXQgaW5pdGlhbCBmb2N1c1xuICAgIGZpcnN0RWxlbWVudD8uZm9jdXMoKTtcbiAgfVxuXG4gIC8qKlxuICAgKiBSZW1vdmUgZm9jdXMgdHJhcCBmcm9tIGNvbnRhaW5lclxuICAgKi9cbiAgcHJpdmF0ZSByZW1vdmVGb2N1c1RyYXAoY29udGFpbmVyOiBIVE1MRWxlbWVudCk6IHZvaWQge1xuICAgIGNvbnN0IGhhbmRsZUtleURvd24gPSBjb250YWluZXIuZ2V0QXR0cmlidXRlKCdkYXRhLWZvY3VzLXRyYXAtaGFuZGxlcicpO1xuICAgIGlmIChoYW5kbGVLZXlEb3duKSB7XG4gICAgICBjb250YWluZXIucmVtb3ZlRXZlbnRMaXN0ZW5lcigna2V5ZG93bicsIGhhbmRsZUtleURvd24gYXMgYW55KTtcbiAgICB9XG4gICAgY29udGFpbmVyLnJlbW92ZUF0dHJpYnV0ZSgnZGF0YS1mb2N1cy10cmFwJyk7XG4gICAgY29udGFpbmVyLnJlbW92ZUF0dHJpYnV0ZSgnZGF0YS1mb2N1cy10cmFwLWhhbmRsZXInKTtcbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgZm9jdXNhYmxlIGVsZW1lbnRzIHdpdGhpbiBjb250YWluZXJcbiAgICovXG4gIHByaXZhdGUgZ2V0Rm9jdXNhYmxlRWxlbWVudHMoY29udGFpbmVyOiBIVE1MRWxlbWVudCk6IEhUTUxFbGVtZW50W10ge1xuICAgIGNvbnN0IGZvY3VzYWJsZVNlbGVjdG9ycyA9IFtcbiAgICAgICdhW2hyZWZdJyxcbiAgICAgICdidXR0b246bm90KFtkaXNhYmxlZF0pJyxcbiAgICAgICdpbnB1dDpub3QoW2Rpc2FibGVkXSknLFxuICAgICAgJ3NlbGVjdDpub3QoW2Rpc2FibGVkXSknLFxuICAgICAgJ3RleHRhcmVhOm5vdChbZGlzYWJsZWRdKScsXG4gICAgICAnW3RhYmluZGV4XTpub3QoW3RhYmluZGV4PVwiLTFcIl0pJyxcbiAgICAgICdbY29udGVudGVkaXRhYmxlPVwidHJ1ZVwiXScsXG4gICAgXS5qb2luKCcsICcpO1xuXG4gICAgcmV0dXJuIEFycmF5LmZyb20oY29udGFpbmVyLnF1ZXJ5U2VsZWN0b3JBbGwoZm9jdXNhYmxlU2VsZWN0b3JzKSlcbiAgICAgIC5maWx0ZXIoZWxlbWVudCA9PiB7XG4gICAgICAgIGNvbnN0IGVsID0gZWxlbWVudCBhcyBIVE1MRWxlbWVudDtcbiAgICAgICAgcmV0dXJuIGVsLm9mZnNldFdpZHRoID4gMCAmJiBlbC5vZmZzZXRIZWlnaHQgPiAwICYmICFlbC5oaWRkZW47XG4gICAgICB9KSBhcyBIVE1MRWxlbWVudFtdO1xuICB9XG5cbiAgLyoqXG4gICAqIENoZWNrIGlmIGVsZW1lbnQgaXMgZm9jdXNhYmxlXG4gICAqL1xuICBpc0ZvY3VzYWJsZShlbGVtZW50OiBIVE1MRWxlbWVudCk6IGJvb2xlYW4ge1xuICAgIHJldHVybiB0aGlzLmdldEZvY3VzYWJsZUVsZW1lbnRzKGRvY3VtZW50LmJvZHkpLmluY2x1ZGVzKGVsZW1lbnQpO1xuICB9XG59XG5cbi8qKlxuICogS2V5Ym9hcmQgbmF2aWdhdGlvbiB1dGlsaXRpZXNcbiAqL1xuZXhwb3J0IGNsYXNzIEtleWJvYXJkTmF2aWdhdGlvbiB7XG4gIHByaXZhdGUga2V5SGFuZGxlcnM6IE1hcDxzdHJpbmcsIChldmVudDogS2V5Ym9hcmRFdmVudCkgPT4gdm9pZD4gPSBuZXcgTWFwKCk7XG5cbiAgLyoqXG4gICAqIEFkZCBrZXlib2FyZCBzaG9ydGN1dFxuICAgKi9cbiAgYWRkU2hvcnRjdXQoXG4gICAga2V5OiBzdHJpbmcsXG4gICAgaGFuZGxlcjogKGV2ZW50OiBLZXlib2FyZEV2ZW50KSA9PiB2b2lkLFxuICAgIG9wdGlvbnM6IHtcbiAgICAgIGN0cmw/OiBib29sZWFuO1xuICAgICAgYWx0PzogYm9vbGVhbjtcbiAgICAgIHNoaWZ0PzogYm9vbGVhbjtcbiAgICAgIG1ldGE/OiBib29sZWFuO1xuICAgIH0gPSB7fVxuICApOiB2b2lkIHtcbiAgICBjb25zdCBzaG9ydGN1dEtleSA9IHRoaXMuY3JlYXRlU2hvcnRjdXRLZXkoa2V5LCBvcHRpb25zKTtcbiAgICB0aGlzLmtleUhhbmRsZXJzLnNldChzaG9ydGN1dEtleSwgaGFuZGxlcik7XG4gIH1cblxuICAvKipcbiAgICogUmVtb3ZlIGtleWJvYXJkIHNob3J0Y3V0XG4gICAqL1xuICByZW1vdmVTaG9ydGN1dChrZXk6IHN0cmluZywgb3B0aW9uczoge1xuICAgIGN0cmw/OiBib29sZWFuO1xuICAgIGFsdD86IGJvb2xlYW47XG4gICAgc2hpZnQ/OiBib29sZWFuO1xuICAgIG1ldGE/OiBib29sZWFuO1xuICB9ID0ge30pOiB2b2lkIHtcbiAgICBjb25zdCBzaG9ydGN1dEtleSA9IHRoaXMuY3JlYXRlU2hvcnRjdXRLZXkoa2V5LCBvcHRpb25zKTtcbiAgICB0aGlzLmtleUhhbmRsZXJzLmRlbGV0ZShzaG9ydGN1dEtleSk7XG4gIH1cblxuICAvKipcbiAgICogSGFuZGxlIGtleWJvYXJkIGV2ZW50c1xuICAgKi9cbiAgaGFuZGxlS2V5RG93bihldmVudDogS2V5Ym9hcmRFdmVudCk6IHZvaWQge1xuICAgIGlmICghZXZlbnQua2V5KSByZXR1cm47XG5cbiAgICBjb25zdCBzaG9ydGN1dEtleSA9IHRoaXMuY3JlYXRlU2hvcnRjdXRLZXkoZXZlbnQua2V5LCB7XG4gICAgICBjdHJsOiBldmVudC5jdHJsS2V5LFxuICAgICAgYWx0OiBldmVudC5hbHRLZXksXG4gICAgICBzaGlmdDogZXZlbnQuc2hpZnRLZXksXG4gICAgICBtZXRhOiBldmVudC5tZXRhS2V5LFxuICAgIH0pO1xuXG4gICAgY29uc3QgaGFuZGxlciA9IHRoaXMua2V5SGFuZGxlcnMuZ2V0KHNob3J0Y3V0S2V5KTtcbiAgICBpZiAoaGFuZGxlcikge1xuICAgICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICAgIGhhbmRsZXIoZXZlbnQpO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBDcmVhdGUgc2hvcnRjdXQga2V5IHN0cmluZ1xuICAgKi9cbiAgcHJpdmF0ZSBjcmVhdGVTaG9ydGN1dEtleShrZXk6IHN0cmluZywgb3B0aW9uczoge1xuICAgIGN0cmw/OiBib29sZWFuO1xuICAgIGFsdD86IGJvb2xlYW47XG4gICAgc2hpZnQ/OiBib29sZWFuO1xuICAgIG1ldGE/OiBib29sZWFuO1xuICB9KTogc3RyaW5nIHtcbiAgICBpZiAoIWtleSkgcmV0dXJuICcnO1xuXG4gICAgY29uc3QgbW9kaWZpZXJzID0gW107XG4gICAgaWYgKG9wdGlvbnMuY3RybCkgbW9kaWZpZXJzLnB1c2goJ2N0cmwnKTtcbiAgICBpZiAob3B0aW9ucy5hbHQpIG1vZGlmaWVycy5wdXNoKCdhbHQnKTtcbiAgICBpZiAob3B0aW9ucy5zaGlmdCkgbW9kaWZpZXJzLnB1c2goJ3NoaWZ0Jyk7XG4gICAgaWYgKG9wdGlvbnMubWV0YSkgbW9kaWZpZXJzLnB1c2goJ21ldGEnKTtcblxuICAgIHJldHVybiBbLi4ubW9kaWZpZXJzLCBrZXkudG9Mb3dlckNhc2UoKV0uam9pbignKycpO1xuICB9XG5cbiAgLyoqXG4gICAqIFNldHVwIGFycm93IGtleSBuYXZpZ2F0aW9uIGZvciBjb250YWluZXJcbiAgICovXG4gIHNldHVwQXJyb3dOYXZpZ2F0aW9uKFxuICAgIGNvbnRhaW5lcjogSFRNTEVsZW1lbnQsXG4gICAgZGlyZWN0aW9uOiAnaG9yaXpvbnRhbCcgfCAndmVydGljYWwnIHwgJ2dyaWQnID0gJ3ZlcnRpY2FsJ1xuICApOiB2b2lkIHtcbiAgICBjb25zdCBoYW5kbGVLZXlEb3duID0gKGV2ZW50OiBLZXlib2FyZEV2ZW50KSA9PiB7XG4gICAgICBjb25zdCBmb2N1c2FibGVFbGVtZW50cyA9IEFycmF5LmZyb20oXG4gICAgICAgIGNvbnRhaW5lci5xdWVyeVNlbGVjdG9yQWxsKCdbdGFiaW5kZXhdOm5vdChbdGFiaW5kZXg9XCItMVwiXSksIGJ1dHRvbiwgaW5wdXQsIHNlbGVjdCwgdGV4dGFyZWEsIGFbaHJlZl0nKVxuICAgICAgKSBhcyBIVE1MRWxlbWVudFtdO1xuXG4gICAgICBjb25zdCBjdXJyZW50SW5kZXggPSBmb2N1c2FibGVFbGVtZW50cy5pbmRleE9mKGRvY3VtZW50LmFjdGl2ZUVsZW1lbnQgYXMgSFRNTEVsZW1lbnQpO1xuICAgICAgaWYgKGN1cnJlbnRJbmRleCA9PT0gLTEpIHJldHVybjtcblxuICAgICAgbGV0IG5leHRJbmRleCA9IGN1cnJlbnRJbmRleDtcblxuICAgICAgc3dpdGNoIChkaXJlY3Rpb24pIHtcbiAgICAgICAgY2FzZSAnaG9yaXpvbnRhbCc6XG4gICAgICAgICAgaWYgKGV2ZW50LmtleSA9PT0gJ0Fycm93TGVmdCcpIG5leHRJbmRleCA9IE1hdGgubWF4KDAsIGN1cnJlbnRJbmRleCAtIDEpO1xuICAgICAgICAgIGlmIChldmVudC5rZXkgPT09ICdBcnJvd1JpZ2h0JykgbmV4dEluZGV4ID0gTWF0aC5taW4oZm9jdXNhYmxlRWxlbWVudHMubGVuZ3RoIC0gMSwgY3VycmVudEluZGV4ICsgMSk7XG4gICAgICAgICAgYnJlYWs7XG5cbiAgICAgICAgY2FzZSAndmVydGljYWwnOlxuICAgICAgICAgIGlmIChldmVudC5rZXkgPT09ICdBcnJvd1VwJykgbmV4dEluZGV4ID0gTWF0aC5tYXgoMCwgY3VycmVudEluZGV4IC0gMSk7XG4gICAgICAgICAgaWYgKGV2ZW50LmtleSA9PT0gJ0Fycm93RG93bicpIG5leHRJbmRleCA9IE1hdGgubWluKGZvY3VzYWJsZUVsZW1lbnRzLmxlbmd0aCAtIDEsIGN1cnJlbnRJbmRleCArIDEpO1xuICAgICAgICAgIGJyZWFrO1xuXG4gICAgICAgIGNhc2UgJ2dyaWQnOlxuICAgICAgICAgIC8vIEltcGxlbWVudCBncmlkIG5hdmlnYXRpb24gYmFzZWQgb24gY29udGFpbmVyIGxheW91dFxuICAgICAgICAgIC8vIFRoaXMgd291bGQgbmVlZCBtb3JlIGNvbXBsZXggbG9naWMgYmFzZWQgb24gdGhlIGFjdHVhbCBncmlkIHN0cnVjdHVyZVxuICAgICAgICAgIGJyZWFrO1xuICAgICAgfVxuXG4gICAgICBpZiAobmV4dEluZGV4ICE9PSBjdXJyZW50SW5kZXgpIHtcbiAgICAgICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgZm9jdXNhYmxlRWxlbWVudHNbbmV4dEluZGV4XT8uZm9jdXMoKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgY29udGFpbmVyLmFkZEV2ZW50TGlzdGVuZXIoJ2tleWRvd24nLCBoYW5kbGVLZXlEb3duKTtcbiAgfVxufVxuXG4vKipcbiAqIENvbG9yIGNvbnRyYXN0IHV0aWxpdGllc1xuICovXG5leHBvcnQgY2xhc3MgQ29sb3JDb250cmFzdCB7XG4gIC8qKlxuICAgKiBDYWxjdWxhdGUgcmVsYXRpdmUgbHVtaW5hbmNlXG4gICAqL1xuICBwcml2YXRlIGdldFJlbGF0aXZlTHVtaW5hbmNlKGNvbG9yOiBzdHJpbmcpOiBudW1iZXIge1xuICAgIGNvbnN0IHJnYiA9IHRoaXMuaGV4VG9SZ2IoY29sb3IpO1xuICAgIGlmICghcmdiKSByZXR1cm4gMDtcblxuICAgIGNvbnN0IFtyLCBnLCBiXSA9IHJnYi5tYXAoYyA9PiB7XG4gICAgICBjID0gYyAvIDI1NTtcbiAgICAgIHJldHVybiBjIDw9IDAuMDM5MjggPyBjIC8gMTIuOTIgOiBNYXRoLnBvdygoYyArIDAuMDU1KSAvIDEuMDU1LCAyLjQpO1xuICAgIH0pO1xuXG4gICAgcmV0dXJuIDAuMjEyNiAqIHIhICsgMC43MTUyICogZyEgKyAwLjA3MjIgKiBiITtcbiAgfVxuXG4gIC8qKlxuICAgKiBDb252ZXJ0IGhleCBjb2xvciB0byBSR0JcbiAgICovXG4gIHByaXZhdGUgaGV4VG9SZ2IoaGV4OiBzdHJpbmcpOiBbbnVtYmVyLCBudW1iZXIsIG51bWJlcl0gfCBudWxsIHtcbiAgICBjb25zdCByZXN1bHQgPSAvXiM/KFthLWZcXGRdezJ9KShbYS1mXFxkXXsyfSkoW2EtZlxcZF17Mn0pJC9pLmV4ZWMoaGV4KTtcbiAgICByZXR1cm4gcmVzdWx0ID8gWyBcbiAgICAgIHBhcnNlSW50KHJlc3VsdFsxXSEsIDE2KSxcbiAgICAgIHBhcnNlSW50KHJlc3VsdFsyXSEsIDE2KSxcbiAgICAgIHBhcnNlSW50KHJlc3VsdFszXSEsIDE2KVxuICAgIF0gOiBudWxsO1xuICB9XG5cbiAgLyoqXG4gICAqIENhbGN1bGF0ZSBjb250cmFzdCByYXRpbyBiZXR3ZWVuIHR3byBjb2xvcnNcbiAgICovXG4gIGdldENvbnRyYXN0UmF0aW8oY29sb3IxOiBzdHJpbmcsIGNvbG9yMjogc3RyaW5nKTogbnVtYmVyIHtcbiAgICBjb25zdCBsdW0xID0gdGhpcy5nZXRSZWxhdGl2ZUx1bWluYW5jZShjb2xvcjEpO1xuICAgIGNvbnN0IGx1bTIgPSB0aGlzLmdldFJlbGF0aXZlTHVtaW5hbmNlKGNvbG9yMik7XG4gICAgXG4gICAgY29uc3QgbGlnaHRlciA9IE1hdGgubWF4KGx1bTEsIGx1bTIpO1xuICAgIGNvbnN0IGRhcmtlciA9IE1hdGgubWluKGx1bTEsIGx1bTIpO1xuICAgIFxuICAgIHJldHVybiAobGlnaHRlciArIDAuMDUpIC8gKGRhcmtlciArIDAuMDUpO1xuICB9XG5cbiAgLyoqXG4gICAqIENoZWNrIGlmIGNvbnRyYXN0IHJhdGlvIG1lZXRzIFdDQUcgc3RhbmRhcmRzXG4gICAqL1xuICBtZWV0c1dDQUcoXG4gICAgY29sb3IxOiBzdHJpbmcsXG4gICAgY29sb3IyOiBzdHJpbmcsXG4gICAgbGV2ZWw6ICdBQScgfCAnQUFBJyA9ICdBQScsXG4gICAgc2l6ZTogJ25vcm1hbCcgfCAnbGFyZ2UnID0gJ25vcm1hbCdcbiAgKTogYm9vbGVhbiB7XG4gICAgY29uc3QgcmF0aW8gPSB0aGlzLmdldENvbnRyYXN0UmF0aW8oY29sb3IxLCBjb2xvcjIpO1xuICAgIFxuICAgIGNvbnN0IHJlcXVpcmVtZW50cyA9IHtcbiAgICAgIEFBOiB7IG5vcm1hbDogNC41LCBsYXJnZTogMyB9LFxuICAgICAgQUFBOiB7IG5vcm1hbDogNywgbGFyZ2U6IDQuNSB9LFxuICAgIH07XG4gICAgXG4gICAgcmV0dXJuIHJhdGlvID49IHJlcXVpcmVtZW50c1tsZXZlbF1bc2l6ZV07XG4gIH1cblxuICAvKipcbiAgICogU3VnZ2VzdCBhY2Nlc3NpYmxlIGNvbG9yIGFsdGVybmF0aXZlc1xuICAgKi9cbiAgc3VnZ2VzdEFjY2Vzc2libGVDb2xvcihcbiAgICBmb3JlZ3JvdW5kOiBzdHJpbmcsXG4gICAgYmFja2dyb3VuZDogc3RyaW5nLFxuICAgIGxldmVsOiAnQUEnIHwgJ0FBQScgPSAnQUEnXG4gICk6IHN0cmluZyB7XG4gICAgY29uc3QgdGFyZ2V0UmF0aW8gPSBsZXZlbCA9PT0gJ0FBJyA/IDQuNSA6IDc7XG4gICAgY29uc3QgY3VycmVudFJhdGlvID0gdGhpcy5nZXRDb250cmFzdFJhdGlvKGZvcmVncm91bmQsIGJhY2tncm91bmQpO1xuICAgIFxuICAgIGlmIChjdXJyZW50UmF0aW8gPj0gdGFyZ2V0UmF0aW8pIHJldHVybiBmb3JlZ3JvdW5kO1xuICAgIFxuICAgIC8vIFNpbXBsZSBhcHByb2FjaDogZGFya2VuIG9yIGxpZ2h0ZW4gdGhlIGZvcmVncm91bmQgY29sb3JcbiAgICBjb25zdCByZ2IgPSB0aGlzLmhleFRvUmdiKGZvcmVncm91bmQpO1xuICAgIGlmICghcmdiKSByZXR1cm4gZm9yZWdyb3VuZDtcbiAgICBcbiAgICBsZXQgW3IsIGcsIGJdID0gcmdiO1xuICAgIGNvbnN0IHN0ZXAgPSBjdXJyZW50UmF0aW8gPCB0YXJnZXRSYXRpbyA/IC0xMCA6IDEwO1xuICAgIFxuICAgIHdoaWxlICh0aGlzLmdldENvbnRyYXN0UmF0aW8odGhpcy5yZ2JUb0hleChyLCBnLCBiKSwgYmFja2dyb3VuZCkgPCB0YXJnZXRSYXRpbykge1xuICAgICAgciA9IE1hdGgubWF4KDAsIE1hdGgubWluKDI1NSwgciArIHN0ZXApKTtcbiAgICAgIGcgPSBNYXRoLm1heCgwLCBNYXRoLm1pbigyNTUsIGcgKyBzdGVwKSk7XG4gICAgICBiID0gTWF0aC5tYXgoMCwgTWF0aC5taW4oMjU1LCBiICsgc3RlcCkpO1xuICAgICAgXG4gICAgICBpZiAoKHIgPT09IDAgJiYgZyA9PT0gMCAmJiBiID09PSAwKSB8fCAociA9PT0gMjU1ICYmIGcgPT09IDI1NSAmJiBiID09PSAyNTUpKSB7XG4gICAgICAgIGJyZWFrO1xuICAgICAgfVxuICAgIH1cbiAgICBcbiAgICByZXR1cm4gdGhpcy5yZ2JUb0hleChyLCBnLCBiKTtcbiAgfVxuXG4gIC8qKlxuICAgKiBDb252ZXJ0IFJHQiB0byBoZXhcbiAgICovXG4gIHByaXZhdGUgcmdiVG9IZXgocjogbnVtYmVyLCBnOiBudW1iZXIsIGI6IG51bWJlcik6IHN0cmluZyB7XG4gICAgcmV0dXJuIGAjJHsoKDEgPDwgMjQpICsgKHIgPDwgMTYpICsgKGcgPDwgOCkgKyBiKS50b1N0cmluZygxNikuc2xpY2UoMSl9YDtcbiAgfVxufVxuXG4vKipcbiAqIE1vdGlvbiBwcmVmZXJlbmNlcyB1dGlsaXRpZXNcbiAqL1xuZXhwb3J0IGNsYXNzIE1vdGlvblByZWZlcmVuY2VzIHtcbiAgLyoqXG4gICAqIENoZWNrIGlmIHVzZXIgcHJlZmVycyByZWR1Y2VkIG1vdGlvblxuICAgKi9cbiAgcHJlZmVyc1JlZHVjZWRNb3Rpb24oKTogYm9vbGVhbiB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm4gZmFsc2U7XG4gICAgcmV0dXJuIHdpbmRvdy5tYXRjaE1lZGlhKCcocHJlZmVycy1yZWR1Y2VkLW1vdGlvbjogcmVkdWNlKScpLm1hdGNoZXM7XG4gIH1cblxuICAvKipcbiAgICogQXBwbHkgbW90aW9uIHByZWZlcmVuY2VzIHRvIGVsZW1lbnRcbiAgICovXG4gIGFwcGx5TW90aW9uUHJlZmVyZW5jZXMoZWxlbWVudDogSFRNTEVsZW1lbnQpOiB2b2lkIHtcbiAgICBpZiAodGhpcy5wcmVmZXJzUmVkdWNlZE1vdGlvbigpKSB7XG4gICAgICBlbGVtZW50LnN0eWxlLmFuaW1hdGlvbiA9ICdub25lJztcbiAgICAgIGVsZW1lbnQuc3R5bGUudHJhbnNpdGlvbiA9ICdub25lJztcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogR2V0IHNhZmUgYW5pbWF0aW9uIGR1cmF0aW9uXG4gICAqL1xuICBnZXRTYWZlQW5pbWF0aW9uRHVyYXRpb24oZGVmYXVsdER1cmF0aW9uOiBudW1iZXIpOiBudW1iZXIge1xuICAgIHJldHVybiB0aGlzLnByZWZlcnNSZWR1Y2VkTW90aW9uKCkgPyAwIDogZGVmYXVsdER1cmF0aW9uO1xuICB9XG59XG5cbi8vIEdsb2JhbCBpbnN0YW5jZXNcbmV4cG9ydCBjb25zdCBzY3JlZW5SZWFkZXJBbm5vdW5jZXIgPSBuZXcgU2NyZWVuUmVhZGVyQW5ub3VuY2VyKCk7XG5leHBvcnQgY29uc3QgZm9jdXNNYW5hZ2VyID0gbmV3IEZvY3VzTWFuYWdlcigpO1xuZXhwb3J0IGNvbnN0IGtleWJvYXJkTmF2aWdhdGlvbiA9IG5ldyBLZXlib2FyZE5hdmlnYXRpb24oKTtcbmV4cG9ydCBjb25zdCBjb2xvckNvbnRyYXN0ID0gbmV3IENvbG9yQ29udHJhc3QoKTtcbmV4cG9ydCBjb25zdCBtb3Rpb25QcmVmZXJlbmNlcyA9IG5ldyBNb3Rpb25QcmVmZXJlbmNlcygpO1xuXG4vKipcbiAqIEluaXRpYWxpemUgYWNjZXNzaWJpbGl0eSBmZWF0dXJlc1xuICovXG5leHBvcnQgZnVuY3Rpb24gaW5pdGlhbGl6ZUExMXkoY29uZmlnOiBQYXJ0aWFsPEExMXlDb25maWc+ID0ge30pOiB2b2lkIHtcbiAgY29uc3QgbWVyZ2VkQ29uZmlnID0geyAuLi5kZWZhdWx0QTExeUNvbmZpZywgLi4uY29uZmlnIH07XG5cbiAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm47XG5cbiAgLy8gU2V0dXAga2V5Ym9hcmQgbmF2aWdhdGlvblxuICBpZiAobWVyZ2VkQ29uZmlnLmVuYWJsZUtleWJvYXJkTmF2aWdhdGlvbikge1xuICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ2tleWRvd24nLCAoZXZlbnQpID0+IHtcbiAgICAgIGtleWJvYXJkTmF2aWdhdGlvbi5oYW5kbGVLZXlEb3duKGV2ZW50KTtcbiAgICB9KTtcbiAgfVxuXG4gIC8vIFNldHVwIHNraXAgbGlua3NcbiAgc2V0dXBTa2lwTGlua3MoKTtcblxuICAvLyBTZXR1cCBmb2N1cyBpbmRpY2F0b3JzXG4gIHNldHVwRm9jdXNJbmRpY2F0b3JzKCk7XG5cbiAgLy8gU2V0dXAgcmVkdWNlZCBtb3Rpb25cbiAgaWYgKG1lcmdlZENvbmZpZy5lbmFibGVSZWR1Y2VkTW90aW9uKSB7XG4gICAgc2V0dXBSZWR1Y2VkTW90aW9uKCk7XG4gIH1cbn1cblxuLyoqXG4gKiBTZXR1cCBza2lwIGxpbmtzIGZvciBrZXlib2FyZCBuYXZpZ2F0aW9uXG4gKi9cbmZ1bmN0aW9uIHNldHVwU2tpcExpbmtzKCk6IHZvaWQge1xuICBjb25zdCBza2lwTGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKTtcbiAgc2tpcExpbmsuaHJlZiA9ICcjbWFpbi1jb250ZW50JztcbiAgc2tpcExpbmsudGV4dENvbnRlbnQgPSAnU2tpcCB0byBtYWluIGNvbnRlbnQnO1xuICBza2lwTGluay5jbGFzc05hbWUgPSAnc3Itb25seSBmb2N1czpub3Qtc3Itb25seSBmb2N1czphYnNvbHV0ZSBmb2N1czp0b3AtNCBmb2N1czpsZWZ0LTQgZm9jdXM6ei01MCBmb2N1czpweC00IGZvY3VzOnB5LTIgZm9jdXM6YmctcHJpbWFyeS02MDAgZm9jdXM6dGV4dC13aGl0ZSBmb2N1czpyb3VuZGVkJztcbiAgXG4gIGRvY3VtZW50LmJvZHkuaW5zZXJ0QmVmb3JlKHNraXBMaW5rLCBkb2N1bWVudC5ib2R5LmZpcnN0Q2hpbGQpO1xufVxuXG4vKipcbiAqIFNldHVwIGZvY3VzIGluZGljYXRvcnNcbiAqL1xuZnVuY3Rpb24gc2V0dXBGb2N1c0luZGljYXRvcnMoKTogdm9pZCB7XG4gIGNvbnN0IHN0eWxlID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnc3R5bGUnKTtcbiAgc3R5bGUudGV4dENvbnRlbnQgPSBgXG4gICAgLmZvY3VzLXZpc2libGUge1xuICAgICAgb3V0bGluZTogMnB4IHNvbGlkICMzYjgyZjY7XG4gICAgICBvdXRsaW5lLW9mZnNldDogMnB4O1xuICAgIH1cbiAgICBcbiAgICAuZm9jdXMtdmlzaWJsZTpub3QoLmZvY3VzLXZpc2libGUtZm9yY2UpIHtcbiAgICAgIG91dGxpbmU6IG5vbmU7XG4gICAgfVxuICAgIFxuICAgIC5mb2N1cy12aXNpYmxlLWZvcmNlIHtcbiAgICAgIG91dGxpbmU6IDJweCBzb2xpZCAjM2I4MmY2ICFpbXBvcnRhbnQ7XG4gICAgICBvdXRsaW5lLW9mZnNldDogMnB4ICFpbXBvcnRhbnQ7XG4gICAgfVxuICBgO1xuICBkb2N1bWVudC5oZWFkLmFwcGVuZENoaWxkKHN0eWxlKTtcbn1cblxuLyoqXG4gKiBTZXR1cCByZWR1Y2VkIG1vdGlvbiBwcmVmZXJlbmNlc1xuICovXG5mdW5jdGlvbiBzZXR1cFJlZHVjZWRNb3Rpb24oKTogdm9pZCB7XG4gIGlmIChtb3Rpb25QcmVmZXJlbmNlcy5wcmVmZXJzUmVkdWNlZE1vdGlvbigpKSB7XG4gICAgY29uc3Qgc3R5bGUgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdzdHlsZScpO1xuICAgIHN0eWxlLnRleHRDb250ZW50ID0gYFxuICAgICAgKiwgKjo6YmVmb3JlLCAqOjphZnRlciB7XG4gICAgICAgIGFuaW1hdGlvbi1kdXJhdGlvbjogMC4wMW1zICFpbXBvcnRhbnQ7XG4gICAgICAgIGFuaW1hdGlvbi1pdGVyYXRpb24tY291bnQ6IDEgIWltcG9ydGFudDtcbiAgICAgICAgdHJhbnNpdGlvbi1kdXJhdGlvbjogMC4wMW1zICFpbXBvcnRhbnQ7XG4gICAgICAgIHNjcm9sbC1iZWhhdmlvcjogYXV0byAhaW1wb3J0YW50O1xuICAgICAgfVxuICAgIGA7XG4gICAgZG9jdW1lbnQuaGVhZC5hcHBlbmRDaGlsZChzdHlsZSk7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJkZWZhdWx0QTExeUNvbmZpZyIsImFubm91bmNlUGFnZUNoYW5nZXMiLCJhbm5vdW5jZUZvcm1FcnJvcnMiLCJhbm5vdW5jZVN0YXR1c1VwZGF0ZXMiLCJlbmFibGVLZXlib2FyZE5hdmlnYXRpb24iLCJlbmFibGVGb2N1c01hbmFnZW1lbnQiLCJlbmFibGVIaWdoQ29udHJhc3QiLCJlbmFibGVSZWR1Y2VkTW90aW9uIiwibWluaW11bVRvdWNoVGFyZ2V0IiwibWluaW11bUNvbG9yQ29udHJhc3QiLCJTY3JlZW5SZWFkZXJBbm5vdW5jZXIiLCJjcmVhdGVMaXZlUmVnaW9ucyIsImxpdmVSZWdpb24iLCJkb2N1bWVudCIsImNyZWF0ZUVsZW1lbnQiLCJzZXRBdHRyaWJ1dGUiLCJzdHlsZSIsImNzc1RleHQiLCJib2R5IiwiYXBwZW5kQ2hpbGQiLCJwb2xpdGVSZWdpb24iLCJhbm5vdW5jZSIsIm1lc3NhZ2UiLCJwcmlvcml0eSIsInRyaW0iLCJyZWdpb24iLCJ0ZXh0Q29udGVudCIsInNldFRpbWVvdXQiLCJhbm5vdW5jZVBhZ2VDaGFuZ2UiLCJwYWdlTmFtZSIsImVycm9ycyIsImxlbmd0aCIsImpvaW4iLCJhbm5vdW5jZVN0YXR1cyIsInN0YXR1cyIsInR5cGUiLCJwcmVmaXgiLCJzdWNjZXNzIiwiZXJyb3IiLCJ3YXJuaW5nIiwiaW5mbyIsImRlc3Ryb3kiLCJyZW1vdmVDaGlsZCIsImNvbnN0cnVjdG9yIiwiRm9jdXNNYW5hZ2VyIiwic2V0Rm9jdXMiLCJlbGVtZW50IiwidGFyZ2V0IiwicXVlcnlTZWxlY3RvciIsImFjdGl2ZUVsZW1lbnQiLCJmb2N1c1N0YWNrIiwicHVzaCIsImZvY3VzIiwic2NyZWVuUmVhZGVyQW5ub3VuY2VyIiwicmVzdG9yZUZvY3VzIiwicHJldmlvdXNFbGVtZW50IiwicG9wIiwiY29udGFpbnMiLCJ0cmFwRm9jdXMiLCJjb250YWluZXIiLCJ0cmFwU3RhY2siLCJzZXR1cEZvY3VzVHJhcCIsInJlbGVhc2VGb2N1c1RyYXAiLCJyZW1vdmVGb2N1c1RyYXAiLCJmb2N1c2FibGVFbGVtZW50cyIsImdldEZvY3VzYWJsZUVsZW1lbnRzIiwiZmlyc3RFbGVtZW50IiwibGFzdEVsZW1lbnQiLCJoYW5kbGVLZXlEb3duIiwiZXZlbnQiLCJrZXkiLCJzaGlmdEtleSIsInByZXZlbnREZWZhdWx0IiwiYWRkRXZlbnRMaXN0ZW5lciIsImdldEF0dHJpYnV0ZSIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJyZW1vdmVBdHRyaWJ1dGUiLCJmb2N1c2FibGVTZWxlY3RvcnMiLCJBcnJheSIsImZyb20iLCJxdWVyeVNlbGVjdG9yQWxsIiwiZmlsdGVyIiwiZWwiLCJvZmZzZXRXaWR0aCIsIm9mZnNldEhlaWdodCIsImhpZGRlbiIsImlzRm9jdXNhYmxlIiwiaW5jbHVkZXMiLCJLZXlib2FyZE5hdmlnYXRpb24iLCJhZGRTaG9ydGN1dCIsImhhbmRsZXIiLCJvcHRpb25zIiwic2hvcnRjdXRLZXkiLCJjcmVhdGVTaG9ydGN1dEtleSIsImtleUhhbmRsZXJzIiwic2V0IiwicmVtb3ZlU2hvcnRjdXQiLCJkZWxldGUiLCJjdHJsIiwiY3RybEtleSIsImFsdCIsImFsdEtleSIsInNoaWZ0IiwibWV0YSIsIm1ldGFLZXkiLCJnZXQiLCJtb2RpZmllcnMiLCJ0b0xvd2VyQ2FzZSIsInNldHVwQXJyb3dOYXZpZ2F0aW9uIiwiZGlyZWN0aW9uIiwiY3VycmVudEluZGV4IiwiaW5kZXhPZiIsIm5leHRJbmRleCIsIk1hdGgiLCJtYXgiLCJtaW4iLCJNYXAiLCJDb2xvckNvbnRyYXN0IiwiZ2V0UmVsYXRpdmVMdW1pbmFuY2UiLCJjb2xvciIsInJnYiIsImhleFRvUmdiIiwiciIsImciLCJiIiwibWFwIiwiYyIsInBvdyIsImhleCIsInJlc3VsdCIsImV4ZWMiLCJwYXJzZUludCIsImdldENvbnRyYXN0UmF0aW8iLCJjb2xvcjEiLCJjb2xvcjIiLCJsdW0xIiwibHVtMiIsImxpZ2h0ZXIiLCJkYXJrZXIiLCJtZWV0c1dDQUciLCJsZXZlbCIsInNpemUiLCJyYXRpbyIsInJlcXVpcmVtZW50cyIsIkFBIiwibm9ybWFsIiwibGFyZ2UiLCJBQUEiLCJzdWdnZXN0QWNjZXNzaWJsZUNvbG9yIiwiZm9yZWdyb3VuZCIsImJhY2tncm91bmQiLCJ0YXJnZXRSYXRpbyIsImN1cnJlbnRSYXRpbyIsInN0ZXAiLCJyZ2JUb0hleCIsInRvU3RyaW5nIiwic2xpY2UiLCJNb3Rpb25QcmVmZXJlbmNlcyIsInByZWZlcnNSZWR1Y2VkTW90aW9uIiwid2luZG93IiwibWF0Y2hNZWRpYSIsIm1hdGNoZXMiLCJhcHBseU1vdGlvblByZWZlcmVuY2VzIiwiYW5pbWF0aW9uIiwidHJhbnNpdGlvbiIsImdldFNhZmVBbmltYXRpb25EdXJhdGlvbiIsImRlZmF1bHREdXJhdGlvbiIsImZvY3VzTWFuYWdlciIsImtleWJvYXJkTmF2aWdhdGlvbiIsImNvbG9yQ29udHJhc3QiLCJtb3Rpb25QcmVmZXJlbmNlcyIsImluaXRpYWxpemVBMTF5IiwiY29uZmlnIiwibWVyZ2VkQ29uZmlnIiwic2V0dXBTa2lwTGlua3MiLCJzZXR1cEZvY3VzSW5kaWNhdG9ycyIsInNldHVwUmVkdWNlZE1vdGlvbiIsInNraXBMaW5rIiwiaHJlZiIsImNsYXNzTmFtZSIsImluc2VydEJlZm9yZSIsImZpcnN0Q2hpbGQiLCJoZWFkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/accessibility/a11y-utils.ts\n"));

/***/ })

});