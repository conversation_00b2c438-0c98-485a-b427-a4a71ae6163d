{"version": 3, "file": "api-key.guard.js", "sourceRoot": "", "sources": ["../../../../../../src/modules/auth/guards/api-key.guard.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAAkG;AAClG,wDAAmD;AACnD,uEAAkE;AAG3D,IAAM,WAAW,GAAjB,MAAM,WAAW;IACtB,YACU,aAA4B,EAC5B,kBAAsC;QADtC,kBAAa,GAAb,aAAa,CAAe;QAC5B,uBAAkB,GAAlB,kBAAkB,CAAoB;IAC7C,CAAC;IAEJ,KAAK,CAAC,WAAW,CAAC,OAAyB;QACzC,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QACpD,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAE3C,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,8BAAqB,CAAC,kBAAkB,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAGnE,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,eAAe,CACrD,UAAU,CAAC,cAAc,EACzB,UAAU,CAAC,WAAW,CACvB,CAAC;YAGF,OAAO,CAAC,MAAM,GAAG;gBACf,GAAG,UAAU;gBACb,OAAO;aACR,CAAC;YAEF,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,8BAAqB,CAAC,iBAAiB,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAEO,aAAa,CAAC,OAAY;QAEhC,MAAM,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC;QACjD,IAAI,UAAU,IAAI,UAAU,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;YACtD,OAAO,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC;QAGD,MAAM,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAClD,IAAI,YAAY,IAAI,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;YACnD,OAAO,YAAY,CAAC;QACtB,CAAC;QAGD,MAAM,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC;QAC1C,IAAI,WAAW,IAAI,WAAW,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;YACjD,OAAO,WAAW,CAAC;QACrB,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF,CAAA;AAxDY,kCAAW;sBAAX,WAAW;IADvB,IAAA,mBAAU,GAAE;qCAGc,+BAAa;QACR,yCAAkB;GAHrC,WAAW,CAwDvB"}