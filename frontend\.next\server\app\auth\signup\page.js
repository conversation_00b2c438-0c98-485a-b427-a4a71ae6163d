(()=>{var e={};e.id=271,e.ids=[271],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},92574:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d}),t(16220),t(69319),t(77406),t(12874);var s=t(27105),r=t(15265),n=t(90157),i=t.n(n),o=t(44665),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(a,l);let d=["",{children:["auth",{children:["signup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,16220)),"C:\\laragon\\www\\max\\trae\\kilo-teset\\augsyanapseAI\\frontend\\app\\auth\\signup\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,69319)),"C:\\laragon\\www\\max\\trae\\kilo-teset\\augsyanapseAI\\frontend\\app\\auth\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,77406)),"C:\\laragon\\www\\max\\trae\\kilo-teset\\augsyanapseAI\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,12874,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\laragon\\www\\max\\trae\\kilo-teset\\augsyanapseAI\\frontend\\app\\auth\\signup\\page.tsx"],m="/auth/signup/page",p={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/auth/signup/page",pathname:"/auth/signup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},64845:(e,a,t)=>{Promise.resolve().then(t.bind(t,81036))},81036:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>h});var s=t(19899),r=t(5507),n=t(76153),i=t(54175),o=t(15950),l=t(95650),d=t(37513),c=t(20382),m=t(80046),p=t(50572),u=t(98015),g=t(22455);let x=d.Ry({firstName:d.Z_().min(1,"First name is required"),lastName:d.Z_().min(1,"Last name is required"),email:d.Z_().email("Please enter a valid email address"),password:d.Z_().min(8,"Password must be at least 8 characters").regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,"Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"),confirmPassword:d.Z_(),organizationId:d.Z_().optional(),acceptTerms:d.O7().refine(e=>!0===e,{message:"You must accept the terms and conditions"})}).refine(e=>e.password===e.confirmPassword,{message:"Passwords do not match",path:["confirmPassword"]});function h(){let e;let a=(0,n.useRouter)(),{register:t,isLoading:d,error:h,clearError:y}=(0,p.a)(),[f,w]=(0,r.useState)(!1),[j,v]=(0,r.useState)(!1),{register:b,handleSubmit:N,formState:{errors:P},setError:I,watch:_}=(0,o.cI)({resolver:(0,l.F)(x),defaultValues:{firstName:"",lastName:"",email:"",password:"",confirmPassword:"",organizationId:"",acceptTerms:!1}}),C=_("password"),k=(e=0,C.length>=8&&e++,/[a-z]/.test(C)&&e++,/[A-Z]/.test(C)&&e++,/\d/.test(C)&&e++,/[@$!%*?&]/.test(C)&&e++,e),z=async e=>{y();try{await t({firstName:e.firstName,lastName:e.lastName,email:e.email,password:e.password,organizationId:e.organizationId||void 0})&&a.push("/dashboard")}catch(e){e.field&&I(e.field,{type:"manual",message:e.message})}};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[s.jsx("h2",{className:"text-3xl font-bold tracking-tight text-gray-900",children:"Create your account"}),(0,s.jsxs)("p",{className:"mt-2 text-sm text-gray-600",children:["Already have an account?"," ",s.jsx(i.default,{href:"/auth/signin",className:"font-medium text-primary-600 hover:text-primary-500",children:"Sign in here"})]})]}),h&&!h.field&&s.jsx("div",{className:"rounded-md bg-error-50 p-4",children:s.jsx("div",{className:"text-sm text-error-700",children:h.message})}),(0,s.jsxs)("form",{className:"space-y-6",onSubmit:N(z),children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[s.jsx(m.I,{label:"First name",type:"text",autoComplete:"given-name",placeholder:"Enter your first name",error:P.firstName?.message,...b("firstName")}),s.jsx(m.I,{label:"Last name",type:"text",autoComplete:"family-name",placeholder:"Enter your last name",error:P.lastName?.message,...b("lastName")})]}),s.jsx(m.I,{label:"Email address",type:"email",autoComplete:"email",placeholder:"Enter your email",error:P.email?.message,...b("email")}),(0,s.jsxs)("div",{children:[s.jsx(m.I,{label:"Password",type:f?"text":"password",autoComplete:"new-password",placeholder:"Create a password",error:P.password?.message,rightIcon:s.jsx("button",{type:"button",className:"text-gray-400 hover:text-gray-600",onClick:()=>w(!f),children:f?s.jsx(u.Z,{className:"h-5 w-5"}):s.jsx(g.Z,{className:"h-5 w-5"})}),...b("password")}),C&&s.jsx("div",{className:"mt-2",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:"flex-1 bg-gray-200 rounded-full h-2",children:s.jsx("div",{className:`h-2 rounded-full transition-all duration-300 ${["bg-error-500","bg-warning-500","bg-yellow-500","bg-blue-500","bg-success-500"][k-1]||"bg-gray-200"}`,style:{width:`${k/5*100}%`}})}),s.jsx("span",{className:"text-xs text-gray-600",children:["Very Weak","Weak","Fair","Good","Strong"][k-1]||"Very Weak"})]})})]}),s.jsx(m.I,{label:"Confirm password",type:j?"text":"password",autoComplete:"new-password",placeholder:"Confirm your password",error:P.confirmPassword?.message,rightIcon:s.jsx("button",{type:"button",className:"text-gray-400 hover:text-gray-600",onClick:()=>v(!j),children:j?s.jsx(u.Z,{className:"h-5 w-5"}):s.jsx(g.Z,{className:"h-5 w-5"})}),...b("confirmPassword")}),s.jsx(m.I,{label:"Organization ID (Optional)",type:"text",placeholder:"Enter organization ID",helperText:"Leave blank to create a new organization",error:P.organizationId?.message,...b("organizationId")}),(0,s.jsxs)("div",{className:"flex items-start",children:[s.jsx("div",{className:"flex items-center h-5",children:s.jsx("input",{id:"acceptTerms",type:"checkbox",className:"focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300 rounded",...b("acceptTerms")})}),(0,s.jsxs)("div",{className:"ml-3 text-sm",children:[(0,s.jsxs)("label",{htmlFor:"acceptTerms",className:"text-gray-700",children:["I agree to the"," ",s.jsx(i.default,{href:"/terms",className:"text-primary-600 hover:text-primary-500",children:"Terms of Service"})," ","and"," ",s.jsx(i.default,{href:"/privacy",className:"text-primary-600 hover:text-primary-500",children:"Privacy Policy"})]}),P.acceptTerms&&s.jsx("p",{className:"text-error-600 text-xs mt-1",children:P.acceptTerms.message})]})]}),s.jsx(c.z,{type:"submit",className:"w-full",loading:d,disabled:d,children:"Create account"})]})]})}},50572:(e,a,t)=>{"use strict";t.d(a,{a:()=>i});var s=t(60878),r=t(76153),n=t(5507);function i(){let{data:e,status:a}=(0,s.useSession)(),t=(0,r.useRouter)(),[i,o]=(0,n.useState)(!1),[l,d]=(0,n.useState)(null),c=async e=>{o(!0),d(null);try{let a=await (0,s.signIn)("credentials",{email:e.email,password:e.password,organizationId:e.organizationId,redirect:!1});if(a?.error)return d("Invalid credentials"),!1;return t.push("/dashboard"),!0}catch(e){return d("Login failed"),!1}finally{o(!1)}},m=async e=>{o(!0),d(null);try{let a=await fetch("http://localhost:3000/api/v1/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!a.ok){let e=await a.json();return d(e.message||"Registration failed"),!1}return await c({email:e.email,password:e.password,organizationId:e.organizationId})}catch(e){return d("Registration failed"),!1}finally{o(!1)}},p=async()=>{o(!0);try{await (0,s.signOut)({redirect:!1}),t.push("/auth/signin")}finally{o(!1)}};return{user:e?.user,accessToken:e?.accessToken,isAuthenticated:!!e,isLoading:"loading"===a||i,error:l,login:c,register:m,logout:p,clearError:()=>d(null)}}},16220:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>s});let s=(0,t(2772).createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\app\auth\signup\page.tsx#default`)}};var a=require("../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),s=a.X(0,[942,544,695,188,975],()=>t(92574));module.exports=s})();