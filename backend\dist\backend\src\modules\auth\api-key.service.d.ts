import { PrismaService } from '@modules/prisma/prisma.service';
import { LoggerService } from '@services/logger/logger.service';
export interface CreateApiKeyDto {
    name: string;
    description?: string;
    permissions: string[];
    expiresAt?: Date;
    organizationId: string;
    createdBy: string;
}
export interface ApiKeyResponse {
    id: string;
    name: string;
    description?: string;
    permissions: string[];
    key: string;
    keyPrefix: string;
    expiresAt?: Date;
    isActive: boolean;
    lastUsedAt?: Date;
    createdAt: Date;
}
export declare class ApiKeyService {
    private prismaService;
    private logger;
    constructor(prismaService: PrismaService, logger: LoggerService);
    createApiKey(createDto: CreateApiKeyDto): Promise<ApiKeyResponse>;
    validateApiKey(apiKey: string): Promise<any>;
    listApiKeys(organizationId: string, userId: string): Promise<Omit<ApiKeyResponse, 'key'>[]>;
    revokeApiKey(apiKeyId: string, organizationId: string, userId: string): Promise<void>;
    getApiKeyStats(organizationId: string): Promise<any>;
}
