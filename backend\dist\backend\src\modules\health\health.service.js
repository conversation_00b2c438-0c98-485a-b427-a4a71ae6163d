"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealthService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const redis_service_1 = require("../redis/redis.service");
let HealthService = class HealthService {
    constructor(prismaService, redisService) {
        this.prismaService = prismaService;
        this.redisService = redisService;
    }
    async getDetailedHealth() {
        const checks = await Promise.allSettled([
            this.checkDatabase(),
            this.checkRedis(),
            this.checkMemory(),
            this.checkDisk(),
        ]);
        const [database, redis, memory, disk] = checks.map(result => result.status === 'fulfilled' ? result.value : { status: 'error', error: result.reason });
        const overallStatus = [database, redis, memory, disk].every(check => check.status === 'healthy')
            ? 'healthy'
            : 'unhealthy';
        return {
            status: overallStatus,
            timestamp: new Date().toISOString(),
            services: {
                database,
                redis,
                memory,
                disk,
            },
        };
    }
    async checkDatabase() {
        try {
            const startTime = Date.now();
            await this.prismaService.$queryRaw `SELECT 1`;
            const responseTime = Date.now() - startTime;
            return {
                status: 'healthy',
                responseTime: `${responseTime}ms`,
            };
        }
        catch (error) {
            return {
                status: 'unhealthy',
                error: error.message,
            };
        }
    }
    async checkRedis() {
        try {
            const startTime = Date.now();
            const client = this.redisService.getClient();
            await client.ping();
            const responseTime = Date.now() - startTime;
            return {
                status: 'healthy',
                responseTime: `${responseTime}ms`,
            };
        }
        catch (error) {
            return {
                status: 'unhealthy',
                error: error.message,
            };
        }
    }
    async checkMemory() {
        try {
            const memUsage = process.memoryUsage();
            const totalMem = memUsage.heapTotal;
            const usedMem = memUsage.heapUsed;
            const memoryUsagePercent = (usedMem / totalMem) * 100;
            return {
                status: memoryUsagePercent < 90 ? 'healthy' : 'degraded',
                usage: `${memoryUsagePercent.toFixed(2)}%`,
                heapUsed: `${Math.round(usedMem / 1024 / 1024)}MB`,
                heapTotal: `${Math.round(totalMem / 1024 / 1024)}MB`,
            };
        }
        catch (error) {
            return {
                status: 'unhealthy',
                error: error.message,
            };
        }
    }
    async checkDisk() {
        try {
            const stats = await Promise.resolve().then(() => require('fs')).then(fs => fs.promises.stat('.'));
            return {
                status: 'healthy',
                lastModified: stats.mtime.toISOString(),
            };
        }
        catch (error) {
            return {
                status: 'unhealthy',
                error: error.message,
            };
        }
    }
};
exports.HealthService = HealthService;
exports.HealthService = HealthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        redis_service_1.RedisService])
], HealthService);
//# sourceMappingURL=health.service.js.map