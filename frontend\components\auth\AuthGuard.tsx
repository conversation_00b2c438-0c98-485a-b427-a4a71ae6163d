'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect, ReactNode } from 'react';
import { User } from '@/lib/auth';

interface AuthGuardProps {
  children: ReactNode;
  requireAuth?: boolean;
  requiredRole?: User['role'];
  requiredPermissions?: string[];
  organizationRequired?: boolean;
  fallback?: ReactNode;
  redirectTo?: string;
}

export function AuthGuard({
  children,
  requireAuth = true,
  requiredRole,
  requiredPermissions = [],
  organizationRequired = false,
  fallback,
  redirectTo = '/auth/signin',
}: AuthGuardProps) {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === 'loading') return; // Still loading

    if (requireAuth && status === 'unauthenticated') {
      router.push(redirectTo);
      return;
    }

    if (requireAuth && session?.user) {
      const user = session.user as User;

      // Check if organization is required
      if (organizationRequired && !user.organizationId) {
        router.push('/onboarding/organization');
        return;
      }

      // Check role requirements
      if (requiredRole && user.role !== requiredRole) {
        // Check role hierarchy
        const roleHierarchy = {
          viewer: 0,
          developer: 1,
          admin: 2,
          owner: 3,
        };

        const userRoleLevel = roleHierarchy[user.role] || 0;
        const requiredRoleLevel = roleHierarchy[requiredRole] || 0;

        if (userRoleLevel < requiredRoleLevel) {
          router.push('/unauthorized');
          return;
        }
      }

      // Check permission requirements
      if (requiredPermissions.length > 0) {
        const hasAllPermissions = requiredPermissions.every(permission =>
          user.permissions?.includes(permission)
        );

        if (!hasAllPermissions) {
          router.push('/unauthorized');
          return;
        }
      }

      // Check if email is verified
      if (!user.emailVerified) {
        router.push('/auth/verify-email');
        return;
      }
    }
  }, [
    session,
    status,
    requireAuth,
    requiredRole,
    requiredPermissions,
    organizationRequired,
    router,
    redirectTo,
  ]);

  // Show loading state
  if (status === 'loading') {
    return fallback || (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  // Show fallback if not authenticated and auth is required
  if (requireAuth && status === 'unauthenticated') {
    return fallback || null;
  }

  // Show fallback if authenticated but doesn't meet requirements
  if (requireAuth && session?.user) {
    const user = session.user as User;

    if (organizationRequired && !user.organizationId) {
      return fallback || null;
    }

    if (requiredRole) {
      const roleHierarchy = {
        viewer: 0,
        developer: 1,
        admin: 2,
        owner: 3,
      };

      const userRoleLevel = roleHierarchy[user.role] || 0;
      const requiredRoleLevel = roleHierarchy[requiredRole] || 0;

      if (userRoleLevel < requiredRoleLevel) {
        return fallback || null;
      }
    }

    if (requiredPermissions.length > 0) {
      const hasAllPermissions = requiredPermissions.every(permission =>
        user.permissions?.includes(permission)
      );

      if (!hasAllPermissions) {
        return fallback || null;
      }
    }

    if (!user.emailVerified) {
      return fallback || null;
    }
  }

  return <>{children}</>;
}

// Higher-order component version
export function withAuthGuard<P extends object>(
  Component: React.ComponentType<P>,
  guardProps: Omit<AuthGuardProps, 'children'>
) {
  return function AuthGuardedComponent(props: P) {
    return (
      <AuthGuard {...guardProps}>
        <Component {...props} />
      </AuthGuard>
    );
  };
}

// Hook for checking permissions
export function usePermissions() {
  const { data: session } = useSession();
  const user = session?.user as User;

  const hasPermission = (permission: string): boolean => {
    return user?.permissions?.includes(permission) || false;
  };

  const hasAnyPermission = (permissions: string[]): boolean => {
    return permissions.some(permission => hasPermission(permission));
  };

  const hasAllPermissions = (permissions: string[]): boolean => {
    return permissions.every(permission => hasPermission(permission));
  };

  const hasRole = (role: User['role']): boolean => {
    return user?.role === role;
  };

  const hasMinimumRole = (role: User['role']): boolean => {
    const roleHierarchy = {
      viewer: 0,
      developer: 1,
      admin: 2,
      owner: 3,
    };

    const userRoleLevel = roleHierarchy[user?.role || 'viewer'] || 0;
    const requiredRoleLevel = roleHierarchy[role] || 0;

    return userRoleLevel >= requiredRoleLevel;
  };

  const isOwner = (): boolean => {
    return user?.role === 'owner' || user?.organizationRole === 'owner';
  };

  const isAdmin = (): boolean => {
    return hasMinimumRole('admin') || user?.organizationRole === 'admin';
  };

  return {
    user,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    hasMinimumRole,
    isOwner,
    isAdmin,
    permissions: user?.permissions || [],
    role: user?.role,
    organizationRole: user?.organizationRole,
  };
}

// Component for conditional rendering based on permissions
interface PermissionGateProps {
  children: ReactNode;
  permission?: string;
  permissions?: string[];
  role?: User['role'];
  minimumRole?: User['role'];
  requireAll?: boolean;
  fallback?: ReactNode;
}

export function PermissionGate({
  children,
  permission,
  permissions = [],
  role,
  minimumRole,
  requireAll = true,
  fallback = null,
}: PermissionGateProps) {
  const {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    hasMinimumRole,
  } = usePermissions();

  let hasAccess = true;

  // Check single permission
  if (permission) {
    hasAccess = hasAccess && hasPermission(permission);
  }

  // Check multiple permissions
  if (permissions.length > 0) {
    hasAccess = hasAccess && (requireAll 
      ? hasAllPermissions(permissions)
      : hasAnyPermission(permissions)
    );
  }

  // Check specific role
  if (role) {
    hasAccess = hasAccess && hasRole(role);
  }

  // Check minimum role
  if (minimumRole) {
    hasAccess = hasAccess && hasMinimumRole(minimumRole);
  }

  return hasAccess ? <>{children}</> : <>{fallback}</>;
}
