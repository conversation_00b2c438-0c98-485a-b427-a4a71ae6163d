import { AuthService, AuthTokens } from './auth.service';
import { PrismaService } from '@modules/prisma/prisma.service';
import { UserRole } from '@prisma/client';
declare class LoginRequestDto {
    email: string;
    password: string;
    organizationId: string;
}
declare class RegisterRequestDto {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    organizationId: string;
    role?: UserRole;
}
declare class RefreshTokenDto {
    refreshToken: string;
}
declare class UserProfileDto {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: UserRole;
    organizationId: string;
    isActive: boolean;
}
export declare class AuthController {
    private authService;
    private prismaService;
    constructor(authService: AuthService, prismaService: PrismaService);
    login(loginDto: LoginRequestDto): Promise<AuthTokens>;
    register(registerDto: RegisterRequestDto): Promise<AuthTokens>;
    refreshToken(refreshTokenDto: RefreshTokenDto): Promise<AuthTokens>;
    logout(req: any, body?: {
        refreshToken?: string;
    }): Promise<{
        message: string;
    }>;
    getProfile(req: any): Promise<UserProfileDto>;
    verifyToken(body: {
        token: string;
    }): Promise<{
        valid: boolean;
        payload?: any;
    }>;
}
export {};
