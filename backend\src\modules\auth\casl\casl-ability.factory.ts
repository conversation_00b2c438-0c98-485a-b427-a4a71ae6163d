import { Injectable } from '@nestjs/common';
import { Ability, AbilityBuilder, AbilityClass, ExtractSubjectType, InferSubjects } from '@casl/ability';
import { UserRole } from '@prisma/client';

// Define all possible subjects for CASL
type Subjects = InferSubjects<
  | 'User'
  | 'Organization'
  | 'Agent'
  | 'Tool'
  | 'Workflow'
  | 'Template'
  | 'Provider'
  | 'Widget'
  | 'Analytics'
  | 'Billing'
  | 'Session'
  | 'Notification'
  | 'Document'
  | 'Sandbox'
  | 'all'
>;

// Define possible actions
export type Action = 'manage' | 'create' | 'read' | 'update' | 'delete' | 'execute' | 'publish';

// Define the AppAbility type
export type AppAbility = Ability<[Action, Subjects]>;

export interface UserContext {
  userId: string;
  organizationId: string;
  role: UserRole;
  email: string;
}

@Injectable()
export class CaslAbilityFactory {
  createForUser(user: UserContext): AppAbility {
    const { can, cannot, build } = new AbilityBuilder<Ability<[Action, Subjects]>>(
      Ability as AbilityClass<AppAbility>,
    );

    // Define permissions based on user role
    switch (user.role) {
      case UserRole.SUPER_ADMIN:
        // Super admin can do everything
        can('manage', 'all');
        break;

      case UserRole.ORG_ADMIN:
        // Organization admin permissions
        can('manage', 'User', { organizationId: user.organizationId });
        can('manage', 'Agent', { organizationId: user.organizationId });
        can('manage', 'Tool', { organizationId: user.organizationId });
        can('manage', 'Workflow', { organizationId: user.organizationId });
        can('manage', 'Template', { organizationId: user.organizationId });
        can('manage', 'Widget', { organizationId: user.organizationId });
        can('manage', 'Session', { organizationId: user.organizationId });
        can('manage', 'Notification', { organizationId: user.organizationId });
        can('manage', 'Document', { organizationId: user.organizationId });
        can('manage', 'Sandbox', { organizationId: user.organizationId });
        can('read', 'Provider', { organizationId: user.organizationId });
        can('read', 'Analytics', { organizationId: user.organizationId });
        can('read', 'Billing', { organizationId: user.organizationId });
        
        // Cannot manage organization itself or billing
        cannot('update', 'Organization');
        cannot('delete', 'Organization');
        cannot('manage', 'Billing');
        break;

      case UserRole.DEVELOPER:
        // Developer permissions
        can('read', 'User', { organizationId: user.organizationId });
        can('manage', 'Agent', { organizationId: user.organizationId, createdBy: user.userId });
        can('manage', 'Tool', { organizationId: user.organizationId, createdBy: user.userId });
        can('manage', 'Workflow', { organizationId: user.organizationId, createdBy: user.userId });
        can('manage', 'Template', { organizationId: user.organizationId, createdBy: user.userId });
        can('manage', 'Widget', { organizationId: user.organizationId, createdBy: user.userId });
        can('manage', 'Session', { organizationId: user.organizationId, userId: user.userId });
        can('manage', 'Document', { organizationId: user.organizationId, createdBy: user.userId });
        can('manage', 'Sandbox', { organizationId: user.organizationId, createdBy: user.userId });
        can('read', 'Provider', { organizationId: user.organizationId });
        can('read', 'Analytics', { organizationId: user.organizationId });
        can('read', 'Notification', { organizationId: user.organizationId });
        
        // Can read others' resources but not modify
        can('read', 'Agent', { organizationId: user.organizationId });
        can('read', 'Tool', { organizationId: user.organizationId });
        can('read', 'Workflow', { organizationId: user.organizationId });
        can('read', 'Template', { organizationId: user.organizationId });
        can('read', 'Widget', { organizationId: user.organizationId });
        can('execute', 'Agent', { organizationId: user.organizationId });
        can('execute', 'Tool', { organizationId: user.organizationId });
        can('execute', 'Workflow', { organizationId: user.organizationId });
        break;

      case UserRole.VIEWER:
        // Viewer permissions - read only
        can('read', 'Agent', { organizationId: user.organizationId });
        can('read', 'Tool', { organizationId: user.organizationId });
        can('read', 'Workflow', { organizationId: user.organizationId });
        can('read', 'Template', { organizationId: user.organizationId });
        can('read', 'Widget', { organizationId: user.organizationId });
        can('read', 'Provider', { organizationId: user.organizationId });
        can('read', 'Analytics', { organizationId: user.organizationId });
        can('read', 'Document', { organizationId: user.organizationId });
        can('read', 'Notification', { organizationId: user.organizationId });
        can('read', 'Session', { organizationId: user.organizationId, userId: user.userId });
        can('read', 'Sandbox', { organizationId: user.organizationId });
        
        // Can execute but not modify
        can('execute', 'Agent', { organizationId: user.organizationId });
        can('execute', 'Tool', { organizationId: user.organizationId });
        can('execute', 'Workflow', { organizationId: user.organizationId });
        break;

      default:
        // No permissions for unknown roles
        break;
    }

    // Common restrictions for all users
    cannot('delete', 'Organization'); // Only super admin can delete organizations
    cannot('manage', 'User', { role: UserRole.SUPER_ADMIN }); // Cannot manage super admins

    return build({
      detectSubjectType: (item) =>
        item.constructor as ExtractSubjectType<Subjects>,
    });
  }

  /**
   * Create ability for API key access
   */
  createForApiKey(organizationId: string, permissions: string[]): AppAbility {
    const { can, build } = new AbilityBuilder<Ability<[Action, Subjects]>>(
      Ability as AbilityClass<AppAbility>,
    );

    // Grant permissions based on API key scope
    permissions.forEach(permission => {
      const [action, subject] = permission.split(':');
      if (action && subject) {
        can(action as Action, subject as any, { organizationId });
      }
    });

    return build({
      detectSubjectType: (item) =>
        item.constructor as ExtractSubjectType<Subjects>,
    });
  }
}
