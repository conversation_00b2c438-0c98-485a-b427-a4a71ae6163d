import { Injectable } from '@nestjs/common';
import { Ability, AbilityBuilder, AbilityClass, ExtractSubjectType, InferSubjects } from '@casl/ability';
import { UserRole } from '@prisma/client';

// Define all possible subjects for CASL
type Subjects = InferSubjects<
  | 'User'
  | 'Organization'
  | 'Agent'
  | 'Tool'
  | 'Workflow'
  | 'Template'
  | 'Provider'
  | 'Widget'
  | 'Analytics'
  | 'Billing'
  | 'Session'
  | 'Notification'
  | 'Document'
  | 'Sandbox'
  | 'all'
>;

// Define possible actions
export type Action = 'manage' | 'create' | 'read' | 'update' | 'delete' | 'execute' | 'publish';

// Define the AppAbility type
export type AppAbility = Ability<[Action, Subjects]>;

export interface UserContext {
  userId: string;
  organizationId: string;
  role: UserRole;
  email: string;
}

@Injectable()
export class CaslAbilityFactory {
  createForUser(user: UserContext): AppAbility {
    const { can, cannot, build } = new AbilityBuilder<Ability<[Action, Subjects]>>(
      Ability as AbilityClass<AppAbility>,
    );

    // Define permissions based on user role
    switch (user.role) {
      case UserRole.SUPER_ADMIN:
        // Super admin can do everything
        can('manage', 'all');
        break;

      case UserRole.ORG_ADMIN:
        // Organization admin permissions - simplified without conditions
        can('manage', 'User');
        can('manage', 'Agent');
        can('manage', 'Tool');
        can('manage', 'Workflow');
        can('manage', 'Template');
        can('manage', 'Widget');
        can('manage', 'Session');
        can('manage', 'Notification');
        can('manage', 'Document');
        can('manage', 'Sandbox');
        can('read', 'Provider');
        can('read', 'Analytics');
        can('read', 'Billing');
        
        // Cannot manage organization itself or billing
        cannot('update', 'Organization');
        cannot('delete', 'Organization');
        cannot('manage', 'Billing');
        break;

      case UserRole.DEVELOPER:
        // Developer permissions - simplified
        can('read', 'User');
        can('manage', 'Agent');
        can('manage', 'Tool');
        can('manage', 'Workflow');
        can('manage', 'Template');
        can('manage', 'Widget');
        can('manage', 'Session');
        can('manage', 'Document');
        can('manage', 'Sandbox');
        can('read', 'Provider');
        can('read', 'Analytics');
        can('read', 'Notification');
        can('execute', 'Agent');
        can('execute', 'Tool');
        can('execute', 'Workflow');
        break;

      case UserRole.VIEWER:
        // Viewer permissions - read only
        can('read', 'Agent');
        can('read', 'Tool');
        can('read', 'Workflow');
        can('read', 'Template');
        can('read', 'Widget');
        can('read', 'Provider');
        can('read', 'Analytics');
        can('read', 'Document');
        can('read', 'Notification');
        can('read', 'Session');
        can('read', 'Sandbox');
        can('execute', 'Agent');
        can('execute', 'Tool');
        can('execute', 'Workflow');
        break;

      default:
        // No permissions for unknown roles
        break;
    }

    // Common restrictions for all users
    cannot('delete', 'Organization'); // Only super admin can delete organizations
    cannot('manage', 'User', { role: UserRole.SUPER_ADMIN }); // Cannot manage super admins

    return build({
      detectSubjectType: (item) =>
        item.constructor?.name as ExtractSubjectType<Subjects>,
    });
  }

  /**
   * Create ability for API key access
   */
  createForApiKey(organizationId: string, permissions: string[]): AppAbility {
    const { can, build } = new AbilityBuilder<Ability<[Action, Subjects]>>(
      Ability as AbilityClass<AppAbility>,
    );

    // Grant permissions based on API key scope
    permissions.forEach(permission => {
      const [action, subject] = permission.split(':');
      if (action && subject) {
        can(action as Action, subject as any);
      }
    });

    return build({
      detectSubjectType: (item) =>
        item.constructor?.name as ExtractSubjectType<Subjects>,
    });
  }
}
