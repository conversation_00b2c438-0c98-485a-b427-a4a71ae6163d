-- SynapseAI Database Initialization Script
-- Production-ready PostgreSQL setup with optimizations

-- Create extensions for AI workloads
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";
CREATE EXTENSION IF NOT EXISTS "btree_gist";

-- Create custom types for SynapseAI
CREATE TYPE user_role AS ENUM ('SUPER_ADMIN', 'ORG_ADMIN', 'DEVELOPER', 'VIEWER');
CREATE TYPE execution_status AS ENUM ('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED');
CREATE TYPE approval_status AS ENUM ('PENDING', 'APPROVED', 'REJECTED', 'EXPIRED');
CREATE TYPE notification_channel AS ENUM ('EMAIL', 'SMS', 'PUSH', 'IN_APP', 'WEBHOOK');

-- Create database users with appropriate permissions
DO $$
BEGIN
    -- Application user for normal operations
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'synapseai_app') THEN
        CREATE ROLE synapseai_app WITH LOGIN PASSWORD 'secure_app_password';
    END IF;
    
    -- Read-only user for analytics and reporting
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'synapseai_readonly') THEN
        CREATE ROLE synapseai_readonly WITH LOGIN PASSWORD 'secure_readonly_password';
    END IF;
    
    -- Backup user for maintenance operations
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'synapseai_backup') THEN
        CREATE ROLE synapseai_backup WITH LOGIN PASSWORD 'secure_backup_password';
    END IF;
END
$$;

-- Grant appropriate permissions
GRANT CONNECT ON DATABASE synapseai_dev TO synapseai_app;
GRANT CONNECT ON DATABASE synapseai_dev TO synapseai_readonly;
GRANT CONNECT ON DATABASE synapseai_dev TO synapseai_backup;

-- Create schemas for better organization
CREATE SCHEMA IF NOT EXISTS core;        -- Core platform tables
CREATE SCHEMA IF NOT EXISTS execution;   -- Execution-related tables
CREATE SCHEMA IF NOT EXISTS analytics;   -- Analytics and metrics
CREATE SCHEMA IF NOT EXISTS audit;       -- Audit logs and compliance

-- Grant schema permissions
GRANT USAGE ON SCHEMA core TO synapseai_app, synapseai_readonly;
GRANT USAGE ON SCHEMA execution TO synapseai_app, synapseai_readonly;
GRANT USAGE ON SCHEMA analytics TO synapseai_app, synapseai_readonly;
GRANT USAGE ON SCHEMA audit TO synapseai_app, synapseai_readonly;

GRANT CREATE ON SCHEMA core TO synapseai_app;
GRANT CREATE ON SCHEMA execution TO synapseai_app;
GRANT CREATE ON SCHEMA analytics TO synapseai_app;
GRANT CREATE ON SCHEMA audit TO synapseai_app;

-- Performance optimization functions
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function for generating organization-scoped UUIDs
CREATE OR REPLACE FUNCTION generate_org_scoped_id(org_id UUID)
RETURNS TEXT AS $$
BEGIN
    RETURN CONCAT(
        SUBSTRING(org_id::TEXT FROM 1 FOR 8),
        '_',
        SUBSTRING(gen_random_uuid()::TEXT FROM 1 FOR 8)
    );
END;
$$ LANGUAGE plpgsql;

-- Function for soft delete
CREATE OR REPLACE FUNCTION soft_delete_record()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE pg_class SET deleted_at = CURRENT_TIMESTAMP 
    WHERE oid = TG_RELID AND OLD.id = NEW.id;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create indexes for common query patterns
-- These will be applied to tables created by Prisma migrations

-- Text search optimization
CREATE OR REPLACE FUNCTION create_text_search_indexes()
RETURNS VOID AS $$
BEGIN
    -- Will be called after table creation
    RAISE NOTICE 'Text search indexes will be created after table migration';
END;
$$ LANGUAGE plpgsql;

-- Performance monitoring view
CREATE OR REPLACE VIEW performance_stats AS
SELECT 
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation,
    most_common_vals,
    most_common_freqs
FROM pg_stats 
WHERE schemaname IN ('core', 'execution', 'analytics', 'audit')
ORDER BY schemaname, tablename, attname;

-- Grant view permissions
GRANT SELECT ON performance_stats TO synapseai_readonly;

-- Create maintenance procedures
CREATE OR REPLACE FUNCTION maintenance_vacuum_analyze()
RETURNS VOID AS $$
DECLARE
    table_record RECORD;
BEGIN
    FOR table_record IN 
        SELECT schemaname, tablename 
        FROM pg_tables 
        WHERE schemaname IN ('core', 'execution', 'analytics', 'audit')
    LOOP
        EXECUTE format('VACUUM ANALYZE %I.%I', table_record.schemaname, table_record.tablename);
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Create backup verification function
CREATE OR REPLACE FUNCTION verify_backup_integrity()
RETURNS TABLE(schema_name TEXT, table_name TEXT, row_count BIGINT) AS $$
DECLARE
    table_record RECORD;
    row_count BIGINT;
BEGIN
    FOR table_record IN 
        SELECT schemaname, tablename 
        FROM pg_tables 
        WHERE schemaname IN ('core', 'execution', 'analytics', 'audit')
    LOOP
        EXECUTE format('SELECT COUNT(*) FROM %I.%I', table_record.schemaname, table_record.tablename) INTO row_count;
        schema_name := table_record.schemaname;
        table_name := table_record.tablename;
        RETURN NEXT;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Grant function permissions
GRANT EXECUTE ON FUNCTION maintenance_vacuum_analyze() TO synapseai_backup;
GRANT EXECUTE ON FUNCTION verify_backup_integrity() TO synapseai_backup;

-- Log successful initialization
INSERT INTO pg_stat_statements_info (dealloc) VALUES (0);
RAISE NOTICE 'SynapseAI database initialization completed successfully';
