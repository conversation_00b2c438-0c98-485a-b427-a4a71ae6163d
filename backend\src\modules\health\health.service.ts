import { Injectable } from '@nestjs/common';
import { PrismaService } from '@modules/prisma/prisma.service';
import { RedisService } from '@modules/redis/redis.service';

@Injectable()
export class HealthService {
  constructor(
    private prismaService: PrismaService,
    private redisService: RedisService,
  ) {}

  async getDetailedHealth() {
    const checks = await Promise.allSettled([
      this.checkDatabase(),
      this.checkRedis(),
      this.checkMemory(),
      this.checkDisk(),
    ]);

    const [database, redis, memory, disk] = checks.map(result => 
      result.status === 'fulfilled' ? result.value : { status: 'error', error: result.reason }
    );

    const overallStatus = [database, redis, memory, disk].every(check => check.status === 'healthy')
      ? 'healthy'
      : 'unhealthy';

    return {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      services: {
        database,
        redis,
        memory,
        disk,
      },
    };
  }

  private async checkDatabase() {
    try {
      const startTime = Date.now();
      await this.prismaService.$queryRaw`SELECT 1`;
      const responseTime = Date.now() - startTime;

      return {
        status: 'healthy',
        responseTime: `${responseTime}ms`,
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
      };
    }
  }

  private async checkRedis() {
    try {
      const startTime = Date.now();
      const client = this.redisService.getClient();
      await client.ping();
      const responseTime = Date.now() - startTime;

      return {
        status: 'healthy',
        responseTime: `${responseTime}ms`,
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
      };
    }
  }

  private async checkMemory() {
    try {
      const memUsage = process.memoryUsage();
      const totalMem = memUsage.heapTotal;
      const usedMem = memUsage.heapUsed;
      const memoryUsagePercent = (usedMem / totalMem) * 100;

      return {
        status: memoryUsagePercent < 90 ? 'healthy' : 'degraded',
        usage: `${memoryUsagePercent.toFixed(2)}%`,
        heapUsed: `${Math.round(usedMem / 1024 / 1024)}MB`,
        heapTotal: `${Math.round(totalMem / 1024 / 1024)}MB`,
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
      };
    }
  }

  private async checkDisk() {
    try {
      // Basic disk check - in production, you might want to use a more sophisticated check
      const stats = await import('fs').then(fs => fs.promises.stat('.'));
      
      return {
        status: 'healthy',
        lastModified: stats.mtime.toISOString(),
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
      };
    }
  }
}
