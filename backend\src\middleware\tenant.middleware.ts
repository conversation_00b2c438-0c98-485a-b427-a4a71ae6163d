import { Injectable, NestMiddleware, UnauthorizedException, ForbiddenException } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '@modules/prisma/prisma.service';
import { LoggerService } from '@services/logger/logger.service';

export interface TenantRequest extends Request {
  organizationId?: string;
  userId?: string;
  userRole?: string;
  tenant?: {
    organizationId: string;
    userId?: string;
    userRole?: string;
    permissions?: string[];
  };
}

@Injectable()
export class TenantMiddleware implements NestMiddleware {
  constructor(
    private jwtService: JwtService,
    private prismaService: PrismaService,
    private logger: LoggerService,
  ) {}

  async use(req: TenantRequest, res: Response, next: NextFunction) {
    try {
      // Skip tenant validation for public endpoints
      if (this.isPublicEndpoint(req.path)) {
        return next();
      }

      // Extract organization ID from various sources
      const organizationId = this.extractOrganizationId(req);
      
      if (!organizationId) {
        throw new UnauthorizedException('Organization ID is required');
      }

      // Validate organization exists and is active
      const organization = await this.prismaService.organization.findFirst({
        where: {
          id: organizationId,
          isActive: true,
        },
      });

      if (!organization) {
        throw new ForbiddenException('Invalid or inactive organization');
      }

      // Extract and validate user from JWT token
      const token = this.extractToken(req);
      let user = null;
      
      if (token) {
        try {
          const payload = this.jwtService.verify(token);
          
          // Validate user belongs to the organization
          user = await this.prismaService.user.findFirst({
            where: {
              id: payload.userId,
              organizationId,
              isActive: true,
            },
            include: {
              organization: true,
            },
          });

          if (!user) {
            throw new ForbiddenException('User does not belong to this organization');
          }

          // Validate token organization matches request organization
          if (payload.organizationId !== organizationId) {
            throw new ForbiddenException('Token organization mismatch');
          }

        } catch (error) {
          if (error instanceof ForbiddenException) {
            throw error;
          }
          throw new UnauthorizedException('Invalid or expired token');
        }
      }

      // Set tenant context on request
      req.organizationId = organizationId;
      req.userId = user?.id;
      req.userRole = user?.role;
      req.tenant = {
        organizationId,
        userId: user?.id,
        userRole: user?.role,
        permissions: await this.getUserPermissions(user?.id, organizationId),
      };

      // Log tenant access for audit
      this.logger.audit('TENANT_ACCESS', 'success', {
        organizationId,
        userId: user?.id,
        resource: 'tenant_access',
        details: {
          userRole: user?.role,
          endpoint: req.path,
          method: req.method,
          userAgent: req.get('User-Agent'),
          ipAddress: req.ip,
        },
      });

      next();
    } catch (error) {
      this.logger.security('TENANT_ACCESS_DENIED', 'high', {
        organizationId: req.organizationId,
        details: {
          endpoint: req.path,
          method: req.method,
          error: error.message,
          userAgent: req.get('User-Agent'),
          ipAddress: req.ip,
        },
      });

      throw error;
    }
  }

  private extractOrganizationId(req: TenantRequest): string | null {
    // Priority order for organization ID extraction:
    // 1. Header (X-Organization-Id)
    // 2. Query parameter (organizationId)
    // 3. Body parameter (organizationId)
    // 4. URL parameter (organizationId)
    // 5. Subdomain (for multi-tenant subdomains)

    // 1. Header
    const headerOrgId = req.get('X-Organization-Id');
    if (headerOrgId) return headerOrgId;

    // 2. Query parameter
    const queryOrgId = req.query.organizationId as string;
    if (queryOrgId) return queryOrgId;

    // 3. Body parameter
    const bodyOrgId = req.body?.organizationId;
    if (bodyOrgId) return bodyOrgId;

    // 4. URL parameter
    const paramOrgId = req.params?.organizationId;
    if (paramOrgId) return paramOrgId;

    // 5. Subdomain extraction (e.g., acme.synapseai.com -> acme)
    const host = req.get('Host');
    if (host && host.includes('.')) {
      const subdomain = host.split('.')[0];
      if (subdomain && subdomain !== 'www' && subdomain !== 'api') {
        return subdomain;
      }
    }

    return null;
  }

  private extractToken(req: Request): string | null {
    const authHeader = req.get('Authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    // Also check for token in cookies for widget authentication
    const cookieToken = req.cookies?.['auth-token'];
    if (cookieToken) {
      return cookieToken;
    }

    return null;
  }

  private async getUserPermissions(userId?: string, organizationId?: string): Promise<string[]> {
    if (!userId || !organizationId) {
      return [];
    }

    try {
      const user = await this.prismaService.user.findFirst({
        where: { id: userId, organizationId },
        include: {
          organization: {
            include: {
              roles: true,
            },
          },
        },
      });

      if (!user) {
        return [];
      }

      // Get role-based permissions
      const rolePermissions = user.organization.roles
        .filter(role => role.name === user.role)
        .flatMap(role => role.permissions as string[]);

      // Add default permissions based on user role
      const defaultPermissions = this.getDefaultPermissions(user.role);

      return [...new Set([...rolePermissions, ...defaultPermissions])];
    } catch (error) {
      this.logger.error('Failed to get user permissions:', error);
      return [];
    }
  }

  private getDefaultPermissions(role: string): string[] {
    const permissionMap = {
      SUPER_ADMIN: ['*'], // All permissions
      ORG_ADMIN: [
        'organization:read',
        'organization:write',
        'users:read',
        'users:write',
        'agents:read',
        'agents:write',
        'tools:read',
        'tools:write',
        'workflows:read',
        'workflows:write',
        'analytics:read',
        'billing:read',
      ],
      DEVELOPER: [
        'agents:read',
        'agents:write',
        'tools:read',
        'tools:write',
        'workflows:read',
        'workflows:write',
        'sandboxes:read',
        'sandboxes:write',
      ],
      VIEWER: [
        'agents:read',
        'tools:read',
        'workflows:read',
        'analytics:read',
      ],
    };

    return permissionMap[role] || [];
  }

  private isPublicEndpoint(path: string): boolean {
    const publicPaths = [
      '/health',
      '/health/ready',
      '/health/live',
      '/api/v1/auth/login',
      '/api/v1/auth/register',
      '/api/v1/auth/refresh',
      '/api/v1/auth/forgot-password',
      '/api/v1/auth/reset-password',
      '/api/docs',
      '/metrics',
      '/widgets/', // Public widget endpoints
    ];

    return publicPaths.some(publicPath => path.startsWith(publicPath));
  }
}
