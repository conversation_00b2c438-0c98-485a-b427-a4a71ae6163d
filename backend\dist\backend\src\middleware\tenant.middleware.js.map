{"version": 3, "file": "tenant.middleware.js", "sourceRoot": "", "sources": ["../../../../src/middleware/tenant.middleware.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAAuG;AAEvG,qCAAyC;AACzC,qEAA+D;AAC/D,sEAAgE;AAezD,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IAC3B,YACU,UAAsB,EACtB,aAA4B,EAC5B,MAAqB;QAFrB,eAAU,GAAV,UAAU,CAAY;QACtB,kBAAa,GAAb,aAAa,CAAe;QAC5B,WAAM,GAAN,MAAM,CAAe;IAC5B,CAAC;IAEJ,KAAK,CAAC,GAAG,CAAC,GAAkB,EAAE,GAAa,EAAE,IAAkB;QAC7D,IAAI,CAAC;YAEH,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBACpC,OAAO,IAAI,EAAE,CAAC;YAChB,CAAC;YAGD,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC;YAEvD,IAAI,CAAC,cAAc,EAAE,CAAC;gBACpB,MAAM,IAAI,8BAAqB,CAAC,6BAA6B,CAAC,CAAC;YACjE,CAAC;YAGD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,SAAS,CAAC;gBACnE,KAAK,EAAE;oBACL,EAAE,EAAE,cAAc;oBAClB,QAAQ,EAAE,IAAI;iBACf;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,MAAM,IAAI,2BAAkB,CAAC,kCAAkC,CAAC,CAAC;YACnE,CAAC;YAGD,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;YACrC,IAAI,IAAI,GAAG,IAAI,CAAC;YAEhB,IAAI,KAAK,EAAE,CAAC;gBACV,IAAI,CAAC;oBACH,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;oBAG9C,IAAI,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC;wBAC7C,KAAK,EAAE;4BACL,EAAE,EAAE,OAAO,CAAC,MAAM;4BAClB,cAAc;4BACd,QAAQ,EAAE,IAAI;yBACf;wBACD,OAAO,EAAE;4BACP,YAAY,EAAE,IAAI;yBACnB;qBACF,CAAC,CAAC;oBAEH,IAAI,CAAC,IAAI,EAAE,CAAC;wBACV,MAAM,IAAI,2BAAkB,CAAC,2CAA2C,CAAC,CAAC;oBAC5E,CAAC;oBAGD,IAAI,OAAO,CAAC,cAAc,KAAK,cAAc,EAAE,CAAC;wBAC9C,MAAM,IAAI,2BAAkB,CAAC,6BAA6B,CAAC,CAAC;oBAC9D,CAAC;gBAEH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,KAAK,YAAY,2BAAkB,EAAE,CAAC;wBACxC,MAAM,KAAK,CAAC;oBACd,CAAC;oBACD,MAAM,IAAI,8BAAqB,CAAC,0BAA0B,CAAC,CAAC;gBAC9D,CAAC;YACH,CAAC;YAGD,GAAG,CAAC,cAAc,GAAG,cAAc,CAAC;YACpC,GAAG,CAAC,MAAM,GAAG,IAAI,EAAE,EAAE,CAAC;YACtB,GAAG,CAAC,QAAQ,GAAG,IAAI,EAAE,IAAI,CAAC;YAC1B,GAAG,CAAC,MAAM,GAAG;gBACX,cAAc;gBACd,MAAM,EAAE,IAAI,EAAE,EAAE;gBAChB,QAAQ,EAAE,IAAI,EAAE,IAAI;gBACpB,WAAW,EAAE,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,EAAE,EAAE,cAAc,CAAC;aACrE,CAAC;YAGF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE,SAAS,EAAE;gBAC5C,cAAc;gBACd,MAAM,EAAE,IAAI,EAAE,EAAE;gBAChB,QAAQ,EAAE,eAAe;gBACzB,OAAO,EAAE;oBACP,QAAQ,EAAE,IAAI,EAAE,IAAI;oBACpB,QAAQ,EAAE,GAAG,CAAC,IAAI;oBAClB,MAAM,EAAE,GAAG,CAAC,MAAM;oBAClB,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;oBAChC,SAAS,EAAE,GAAG,CAAC,EAAE;iBAClB;aACF,CAAC,CAAC;YAEH,IAAI,EAAE,CAAC;QACT,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,sBAAsB,EAAE,MAAM,EAAE;gBACnD,cAAc,EAAE,GAAG,CAAC,cAAc;gBAClC,OAAO,EAAE;oBACP,QAAQ,EAAE,GAAG,CAAC,IAAI;oBAClB,MAAM,EAAE,GAAG,CAAC,MAAM;oBAClB,KAAK,EAAE,KAAK,CAAC,OAAO;oBACpB,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;oBAChC,SAAS,EAAE,GAAG,CAAC,EAAE;iBAClB;aACF,CAAC,CAAC;YAEH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,qBAAqB,CAAC,GAAkB;QAS9C,MAAM,WAAW,GAAG,GAAG,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;QACjD,IAAI,WAAW;YAAE,OAAO,WAAW,CAAC;QAGpC,MAAM,UAAU,GAAG,GAAG,CAAC,KAAK,CAAC,cAAwB,CAAC;QACtD,IAAI,UAAU;YAAE,OAAO,UAAU,CAAC;QAGlC,MAAM,SAAS,GAAG,GAAG,CAAC,IAAI,EAAE,cAAc,CAAC;QAC3C,IAAI,SAAS;YAAE,OAAO,SAAS,CAAC;QAGhC,MAAM,UAAU,GAAG,GAAG,CAAC,MAAM,EAAE,cAAc,CAAC;QAC9C,IAAI,UAAU;YAAE,OAAO,UAAU,CAAC;QAGlC,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC7B,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC/B,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC,IAAI,SAAS,IAAI,SAAS,KAAK,KAAK,IAAI,SAAS,KAAK,KAAK,EAAE,CAAC;gBAC5D,OAAO,SAAS,CAAC;YACnB,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,YAAY,CAAC,GAAY;QAC/B,MAAM,UAAU,GAAG,GAAG,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QAC5C,IAAI,UAAU,IAAI,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACnD,OAAO,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC;QAGD,MAAM,WAAW,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC,YAAY,CAAC,CAAC;QAChD,IAAI,WAAW,EAAE,CAAC;YAChB,OAAO,WAAW,CAAC;QACrB,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,MAAe,EAAE,cAAuB;QACvE,IAAI,CAAC,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;YAC/B,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC;gBACnD,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,cAAc,EAAE;gBACrC,OAAO,EAAE;oBACP,YAAY,EAAE;wBACZ,OAAO,EAAE;4BACP,KAAK,EAAE,IAAI;yBACZ;qBACF;iBACF;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,OAAO,EAAE,CAAC;YACZ,CAAC;YAGD,MAAM,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK;iBAC5C,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC;iBACvC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,WAAuB,CAAC,CAAC;YAGjD,MAAM,kBAAkB,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEjE,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,eAAe,EAAE,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;QACnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YAC5D,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAEO,qBAAqB,CAAC,IAAY;QACxC,MAAM,aAAa,GAAG;YACpB,WAAW,EAAE,CAAC,GAAG,CAAC;YAClB,SAAS,EAAE;gBACT,mBAAmB;gBACnB,oBAAoB;gBACpB,YAAY;gBACZ,aAAa;gBACb,aAAa;gBACb,cAAc;gBACd,YAAY;gBACZ,aAAa;gBACb,gBAAgB;gBAChB,iBAAiB;gBACjB,gBAAgB;gBAChB,cAAc;aACf;YACD,SAAS,EAAE;gBACT,aAAa;gBACb,cAAc;gBACd,YAAY;gBACZ,aAAa;gBACb,gBAAgB;gBAChB,iBAAiB;gBACjB,gBAAgB;gBAChB,iBAAiB;aAClB;YACD,MAAM,EAAE;gBACN,aAAa;gBACb,YAAY;gBACZ,gBAAgB;gBAChB,gBAAgB;aACjB;SACF,CAAC;QAEF,OAAO,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;IACnC,CAAC;IAEO,gBAAgB,CAAC,IAAY;QACnC,MAAM,WAAW,GAAG;YAClB,SAAS;YACT,eAAe;YACf,cAAc;YACd,oBAAoB;YACpB,uBAAuB;YACvB,sBAAsB;YACtB,8BAA8B;YAC9B,6BAA6B;YAC7B,WAAW;YACX,UAAU;YACV,WAAW;SACZ,CAAC;QAEF,OAAO,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;IACrE,CAAC;CACF,CAAA;AA9PY,4CAAgB;2BAAhB,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;qCAGW,gBAAU;QACP,8BAAa;QACpB,8BAAa;GAJpB,gBAAgB,CA8P5B"}