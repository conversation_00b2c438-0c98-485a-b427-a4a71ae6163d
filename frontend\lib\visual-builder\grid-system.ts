export interface GridConfig {
  enabled: boolean;
  size: number;
  snapToGrid: boolean;
  showGrid: boolean;
  color: string;
  opacity: number;
  style: 'dots' | 'lines' | 'crosses';
  subdivisions?: number;
}

export interface SnapResult {
  position: { x: number; y: number };
  snapped: boolean;
  snapLines?: Array<{
    type: 'vertical' | 'horizontal';
    position: number;
    color: string;
  }>;
}

export interface AlignmentGuide {
  type: 'vertical' | 'horizontal' | 'center-vertical' | 'center-horizontal';
  position: number;
  nodes: string[];
  color: string;
  temporary: boolean;
}

class GridSystem {
  private config: GridConfig;
  private alignmentGuides: AlignmentGuide[] = [];
  private snapThreshold: number = 10;

  constructor(config: Partial<GridConfig> = {}) {
    this.config = {
      enabled: true,
      size: 20,
      snapToGrid: true,
      showGrid: true,
      color: '#e5e7eb',
      opacity: 0.5,
      style: 'dots',
      subdivisions: 4,
      ...config,
    };
  }

  /**
   * Update grid configuration
   */
  updateConfig(config: Partial<GridConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Get current grid configuration
   */
  getConfig(): GridConfig {
    return { ...this.config };
  }

  /**
   * Snap position to grid
   */
  snapToGrid(position: { x: number; y: number }): SnapResult {
    if (!this.config.enabled || !this.config.snapToGrid) {
      return { position, snapped: false };
    }

    const { size } = this.config;
    const snappedPosition = {
      x: Math.round(position.x / size) * size,
      y: Math.round(position.y / size) * size,
    };

    const snapped = snappedPosition.x !== position.x || snappedPosition.y !== position.y;

    return {
      position: snappedPosition,
      snapped,
      snapLines: snapped ? this.generateSnapLines(snappedPosition) : undefined,
    };
  }

  /**
   * Snap to alignment guides (other nodes)
   */
  snapToAlignment(
    position: { x: number; y: number },
    size: { width: number; height: number },
    excludeNodes: string[] = [],
    allNodes: Array<{ id: string; position: { x: number; y: number }; size: { width: number; height: number } }> = []
  ): SnapResult {
    if (!this.config.enabled) {
      return { position, snapped: false };
    }

    let snappedPosition = { ...position };
    let snapped = false;
    const snapLines: SnapResult['snapLines'] = [];

    // Calculate node edges and centers
    const nodeLeft = position.x;
    const nodeRight = position.x + size.width;
    const nodeTop = position.y;
    const nodeBottom = position.y + size.height;
    const nodeCenterX = position.x + size.width / 2;
    const nodeCenterY = position.y + size.height / 2;

    // Check alignment with other nodes
    for (const otherNode of allNodes) {
      if (excludeNodes.includes(otherNode.id)) continue;

      const otherLeft = otherNode.position.x;
      const otherRight = otherNode.position.x + otherNode.size.width;
      const otherTop = otherNode.position.y;
      const otherBottom = otherNode.position.y + otherNode.size.height;
      const otherCenterX = otherNode.position.x + otherNode.size.width / 2;
      const otherCenterY = otherNode.position.y + otherNode.size.height / 2;

      // Vertical alignment
      if (Math.abs(nodeLeft - otherLeft) < this.snapThreshold) {
        snappedPosition.x = otherLeft;
        snapped = true;
        snapLines.push({ type: 'vertical', position: otherLeft, color: '#3b82f6' });
      } else if (Math.abs(nodeRight - otherRight) < this.snapThreshold) {
        snappedPosition.x = otherRight - size.width;
        snapped = true;
        snapLines.push({ type: 'vertical', position: otherRight, color: '#3b82f6' });
      } else if (Math.abs(nodeCenterX - otherCenterX) < this.snapThreshold) {
        snappedPosition.x = otherCenterX - size.width / 2;
        snapped = true;
        snapLines.push({ type: 'vertical', position: otherCenterX, color: '#10b981' });
      }

      // Horizontal alignment
      if (Math.abs(nodeTop - otherTop) < this.snapThreshold) {
        snappedPosition.y = otherTop;
        snapped = true;
        snapLines.push({ type: 'horizontal', position: otherTop, color: '#3b82f6' });
      } else if (Math.abs(nodeBottom - otherBottom) < this.snapThreshold) {
        snappedPosition.y = otherBottom - size.height;
        snapped = true;
        snapLines.push({ type: 'horizontal', position: otherBottom, color: '#3b82f6' });
      } else if (Math.abs(nodeCenterY - otherCenterY) < this.snapThreshold) {
        snappedPosition.y = otherCenterY - size.height / 2;
        snapped = true;
        snapLines.push({ type: 'horizontal', position: otherCenterY, color: '#10b981' });
      }
    }

    return {
      position: snappedPosition,
      snapped,
      snapLines: snapLines.length > 0 ? snapLines : undefined,
    };
  }

  /**
   * Generate alignment guides for multiple selected nodes
   */
  generateAlignmentGuides(
    selectedNodes: Array<{ id: string; position: { x: number; y: number }; size: { width: number; height: number } }>
  ): AlignmentGuide[] {
    if (selectedNodes.length < 2) return [];

    const guides: AlignmentGuide[] = [];

    // Group nodes by alignment
    const leftAligned = this.groupByAlignment(selectedNodes, 'left');
    const rightAligned = this.groupByAlignment(selectedNodes, 'right');
    const topAligned = this.groupByAlignment(selectedNodes, 'top');
    const bottomAligned = this.groupByAlignment(selectedNodes, 'bottom');
    const centerXAligned = this.groupByAlignment(selectedNodes, 'centerX');
    const centerYAligned = this.groupByAlignment(selectedNodes, 'centerY');

    // Create guides for aligned groups
    leftAligned.forEach(group => {
      if (group.nodes.length >= 2) {
        guides.push({
          type: 'vertical',
          position: group.position,
          nodes: group.nodes,
          color: '#3b82f6',
          temporary: false,
        });
      }
    });

    rightAligned.forEach(group => {
      if (group.nodes.length >= 2) {
        guides.push({
          type: 'vertical',
          position: group.position,
          nodes: group.nodes,
          color: '#3b82f6',
          temporary: false,
        });
      }
    });

    topAligned.forEach(group => {
      if (group.nodes.length >= 2) {
        guides.push({
          type: 'horizontal',
          position: group.position,
          nodes: group.nodes,
          color: '#3b82f6',
          temporary: false,
        });
      }
    });

    bottomAligned.forEach(group => {
      if (group.nodes.length >= 2) {
        guides.push({
          type: 'horizontal',
          position: group.position,
          nodes: group.nodes,
          color: '#3b82f6',
          temporary: false,
        });
      }
    });

    centerXAligned.forEach(group => {
      if (group.nodes.length >= 2) {
        guides.push({
          type: 'center-vertical',
          position: group.position,
          nodes: group.nodes,
          color: '#10b981',
          temporary: false,
        });
      }
    });

    centerYAligned.forEach(group => {
      if (group.nodes.length >= 2) {
        guides.push({
          type: 'center-horizontal',
          position: group.position,
          nodes: group.nodes,
          color: '#10b981',
          temporary: false,
        });
      }
    });

    return guides;
  }

  /**
   * Render grid on canvas
   */
  renderGrid(
    ctx: CanvasRenderingContext2D,
    viewport: { x: number; y: number; zoom: number },
    canvasSize: { width: number; height: number }
  ): void {
    if (!this.config.enabled || !this.config.showGrid) return;

    const { size, color, opacity, style, subdivisions } = this.config;
    const { x, y, zoom } = viewport;

    ctx.save();
    ctx.globalAlpha = opacity;
    ctx.strokeStyle = color;
    ctx.fillStyle = color;

    // Calculate visible grid bounds
    const startX = Math.floor(-x / (size * zoom)) * size;
    const startY = Math.floor(-y / (size * zoom)) * size;
    const endX = startX + Math.ceil(canvasSize.width / (size * zoom)) * size;
    const endY = startY + Math.ceil(canvasSize.height / (size * zoom)) * size;

    switch (style) {
      case 'lines':
        this.renderGridLines(ctx, startX, startY, endX, endY, size);
        break;
      case 'dots':
        this.renderGridDots(ctx, startX, startY, endX, endY, size);
        break;
      case 'crosses':
        this.renderGridCrosses(ctx, startX, startY, endX, endY, size);
        break;
    }

    // Render subdivisions if enabled
    if (subdivisions && subdivisions > 1) {
      ctx.globalAlpha = opacity * 0.3;
      const subSize = size / subdivisions;
      
      switch (style) {
        case 'lines':
          this.renderGridLines(ctx, startX, startY, endX, endY, subSize);
          break;
        case 'dots':
          this.renderGridDots(ctx, startX, startY, endX, endY, subSize);
          break;
      }
    }

    ctx.restore();
  }

  /**
   * Render alignment guides
   */
  renderAlignmentGuides(
    ctx: CanvasRenderingContext2D,
    viewport: { x: number; y: number; zoom: number },
    canvasSize: { width: number; height: number }
  ): void {
    if (!this.config.enabled) return;

    ctx.save();
    ctx.setLineDash([5, 5]);
    ctx.lineWidth = 1;

    this.alignmentGuides.forEach(guide => {
      ctx.strokeStyle = guide.color;
      ctx.globalAlpha = guide.temporary ? 0.8 : 0.6;

      ctx.beginPath();
      if (guide.type === 'vertical' || guide.type === 'center-vertical') {
        ctx.moveTo(guide.position, -viewport.y / viewport.zoom);
        ctx.lineTo(guide.position, (-viewport.y + canvasSize.height) / viewport.zoom);
      } else {
        ctx.moveTo(-viewport.x / viewport.zoom, guide.position);
        ctx.lineTo((-viewport.x + canvasSize.width) / viewport.zoom, guide.position);
      }
      ctx.stroke();
    });

    ctx.restore();
  }

  /**
   * Set temporary alignment guides (during drag)
   */
  setTemporaryGuides(guides: AlignmentGuide[]): void {
    // Remove existing temporary guides
    this.alignmentGuides = this.alignmentGuides.filter(g => !g.temporary);
    
    // Add new temporary guides
    this.alignmentGuides.push(...guides.map(g => ({ ...g, temporary: true })));
  }

  /**
   * Clear temporary alignment guides
   */
  clearTemporaryGuides(): void {
    this.alignmentGuides = this.alignmentGuides.filter(g => !g.temporary);
  }

  /**
   * Set snap threshold
   */
  setSnapThreshold(threshold: number): void {
    this.snapThreshold = threshold;
  }

  /**
   * Private helper methods
   */
  private generateSnapLines(position: { x: number; y: number }): SnapResult['snapLines'] {
    return [
      { type: 'vertical', position: position.x, color: '#ef4444' },
      { type: 'horizontal', position: position.y, color: '#ef4444' },
    ];
  }

  private groupByAlignment(
    nodes: Array<{ id: string; position: { x: number; y: number }; size: { width: number; height: number } }>,
    alignment: 'left' | 'right' | 'top' | 'bottom' | 'centerX' | 'centerY'
  ): Array<{ position: number; nodes: string[] }> {
    const groups = new Map<number, string[]>();

    nodes.forEach(node => {
      let position: number;
      
      switch (alignment) {
        case 'left':
          position = node.position.x;
          break;
        case 'right':
          position = node.position.x + node.size.width;
          break;
        case 'top':
          position = node.position.y;
          break;
        case 'bottom':
          position = node.position.y + node.size.height;
          break;
        case 'centerX':
          position = node.position.x + node.size.width / 2;
          break;
        case 'centerY':
          position = node.position.y + node.size.height / 2;
          break;
      }

      // Group nodes with similar positions (within threshold)
      let foundGroup = false;
      for (const [groupPos, groupNodes] of groups.entries()) {
        if (Math.abs(position - groupPos) < this.snapThreshold) {
          groupNodes.push(node.id);
          foundGroup = true;
          break;
        }
      }

      if (!foundGroup) {
        groups.set(position, [node.id]);
      }
    });

    return Array.from(groups.entries()).map(([position, nodes]) => ({
      position,
      nodes,
    }));
  }

  private renderGridLines(
    ctx: CanvasRenderingContext2D,
    startX: number,
    startY: number,
    endX: number,
    endY: number,
    size: number
  ): void {
    ctx.lineWidth = 1;
    ctx.beginPath();

    // Vertical lines
    for (let x = startX; x <= endX; x += size) {
      ctx.moveTo(x, startY);
      ctx.lineTo(x, endY);
    }

    // Horizontal lines
    for (let y = startY; y <= endY; y += size) {
      ctx.moveTo(startX, y);
      ctx.lineTo(endX, y);
    }

    ctx.stroke();
  }

  private renderGridDots(
    ctx: CanvasRenderingContext2D,
    startX: number,
    startY: number,
    endX: number,
    endY: number,
    size: number
  ): void {
    const dotSize = 1;

    for (let x = startX; x <= endX; x += size) {
      for (let y = startY; y <= endY; y += size) {
        ctx.beginPath();
        ctx.arc(x, y, dotSize, 0, 2 * Math.PI);
        ctx.fill();
      }
    }
  }

  private renderGridCrosses(
    ctx: CanvasRenderingContext2D,
    startX: number,
    startY: number,
    endX: number,
    endY: number,
    size: number
  ): void {
    const crossSize = 3;
    ctx.lineWidth = 1;

    for (let x = startX; x <= endX; x += size) {
      for (let y = startY; y <= endY; y += size) {
        ctx.beginPath();
        ctx.moveTo(x - crossSize, y);
        ctx.lineTo(x + crossSize, y);
        ctx.moveTo(x, y - crossSize);
        ctx.lineTo(x, y + crossSize);
        ctx.stroke();
      }
    }
  }
}

export { GridSystem };
