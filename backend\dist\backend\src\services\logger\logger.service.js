"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var LoggerService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoggerService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const winston = require("winston");
const winston_daily_rotate_file_1 = require("winston-daily-rotate-file");
let LoggerService = LoggerService_1 = class LoggerService {
    constructor(configService) {
        this.configService = configService;
        this.context = 'Application';
        this.createLogger();
    }
    createLogger() {
        const environment = this.configService.get('NODE_ENV', 'development');
        const logLevel = this.configService.get('LOG_LEVEL', 'info');
        const logDir = this.configService.get('LOG_DIR', './logs');
        const customFormat = winston.format.combine(winston.format.timestamp({
            format: 'YYYY-MM-DD HH:mm:ss.SSS',
        }), winston.format.errors({ stack: true }), winston.format.json(), winston.format.printf((info) => {
            const { timestamp, level, message, context, ...meta } = info;
            const logEntry = {
                timestamp,
                level: level.toUpperCase(),
                context: context || this.context,
                message,
                ...meta,
            };
            logEntry['service'] = 'synapseai-backend';
            logEntry['environment'] = environment;
            logEntry['version'] = this.configService.get('APP_VERSION', '1.0.0');
            return JSON.stringify(logEntry);
        }));
        const consoleFormat = winston.format.combine(winston.format.colorize(), winston.format.timestamp({
            format: 'HH:mm:ss.SSS',
        }), winston.format.printf((info) => {
            const { timestamp, level, message, context, ...meta } = info;
            const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
            return `${timestamp} [${level}] [${context || this.context}] ${message} ${metaStr}`;
        }));
        const transports = [];
        if (environment === 'development') {
            transports.push(new winston.transports.Console({
                level: logLevel,
                format: consoleFormat,
            }));
        }
        else {
            transports.push(new winston.transports.Console({
                level: logLevel,
                format: customFormat,
            }));
        }
        if (environment === 'production') {
            transports.push(new winston_daily_rotate_file_1.default({
                filename: `${logDir}/application-%DATE%.log`,
                datePattern: 'YYYY-MM-DD',
                maxSize: '100m',
                maxFiles: '30d',
                level: logLevel,
                format: customFormat,
            }));
            transports.push(new winston_daily_rotate_file_1.default({
                filename: `${logDir}/error-%DATE%.log`,
                datePattern: 'YYYY-MM-DD',
                maxSize: '100m',
                maxFiles: '30d',
                level: 'error',
                format: customFormat,
            }));
            transports.push(new winston_daily_rotate_file_1.default({
                filename: `${logDir}/audit-%DATE%.log`,
                datePattern: 'YYYY-MM-DD',
                maxSize: '100m',
                maxFiles: '365d',
                level: 'info',
                format: customFormat,
            }));
            transports.push(new winston_daily_rotate_file_1.default({
                filename: `${logDir}/performance-%DATE%.log`,
                datePattern: 'YYYY-MM-DD',
                maxSize: '100m',
                maxFiles: '7d',
                level: 'debug',
                format: customFormat,
            }));
        }
        this.logger = winston.createLogger({
            level: logLevel,
            format: customFormat,
            transports,
            exitOnError: false,
            silent: environment === 'test',
        });
        this.logger.exceptions.handle(new winston.transports.File({
            filename: `${logDir}/exceptions.log`,
            format: customFormat,
        }));
        this.logger.rejections.handle(new winston.transports.File({
            filename: `${logDir}/rejections.log`,
            format: customFormat,
        }));
    }
    setContext(context) {
        this.context = context;
    }
    log(message, context) {
        this.logger.info(message, this.formatContext(context));
    }
    error(message, trace, context) {
        const logContext = this.formatContext(context);
        if (trace instanceof Error) {
            logContext.error = {
                name: trace.name,
                message: trace.message,
                stack: trace.stack,
            };
        }
        else if (trace) {
            logContext.trace = trace;
        }
        this.logger.error(message, logContext);
    }
    warn(message, context) {
        this.logger.warn(message, this.formatContext(context));
    }
    debug(message, context) {
        this.logger.debug(message, this.formatContext(context));
    }
    verbose(message, context) {
        this.logger.verbose(message, this.formatContext(context));
    }
    audit(action, result, context) {
        const auditLog = {
            action,
            result,
            timestamp: new Date().toISOString(),
            ...context,
        };
        this.logger.info('AUDIT_EVENT', {
            context: 'Audit',
            audit: auditLog,
        });
    }
    performance(operation, duration, context) {
        const performanceLog = {
            operation,
            duration,
            timestamp: new Date().toISOString(),
            ...context,
        };
        this.logger.debug('PERFORMANCE_METRIC', {
            context: 'Performance',
            performance: performanceLog,
        });
    }
    security(event, severity, context) {
        const securityLog = {
            event,
            severity,
            timestamp: new Date().toISOString(),
            ...context,
        };
        this.logger.warn('SECURITY_EVENT', {
            context: 'Security',
            security: securityLog,
        });
    }
    business(event, context) {
        const businessLog = {
            event,
            timestamp: new Date().toISOString(),
            ...context,
        };
        this.logger.info('BUSINESS_EVENT', {
            context: 'Business',
            business: businessLog,
        });
    }
    formatContext(context) {
        if (typeof context === 'string') {
            return { context };
        }
        if (context && typeof context === 'object') {
            return {
                context: this.context,
                ...context,
            };
        }
        return { context: this.context };
    }
    getWinstonLogger() {
        return this.logger;
    }
    child(defaultContext) {
        const childLogger = new LoggerService_1(this.configService);
        const originalLogger = childLogger.logger;
        childLogger.logger = originalLogger.child(defaultContext);
        return childLogger;
    }
};
exports.LoggerService = LoggerService;
exports.LoggerService = LoggerService = LoggerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], LoggerService);
//# sourceMappingURL=logger.service.js.map