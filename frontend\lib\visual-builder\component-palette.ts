import { CanvasNode } from './canvas';
import { apiClient } from '@/lib/api';

export interface ComponentTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  type: CanvasNode['type'];
  icon: string;
  tags: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  popularity: number;
  template: Omit<CanvasNode, 'id' | 'position'>;
  examples?: Array<{
    title: string;
    description: string;
    config: Record<string, any>;
  }>;
  documentation?: {
    overview: string;
    inputs: Array<{
      name: string;
      type: string;
      description: string;
      required: boolean;
      example?: any;
    }>;
    outputs: Array<{
      name: string;
      type: string;
      description: string;
      example?: any;
    }>;
    configuration: Array<{
      field: string;
      type: string;
      description: string;
      default?: any;
      options?: any[];
    }>;
  };
}

export interface PaletteSuggestion {
  template: ComponentTemplate;
  confidence: number;
  reasoning: string;
  context: {
    position?: { x: number; y: number };
    connectTo?: string[];
    replaces?: string;
  };
}

export interface PaletteContext {
  currentNodes: CanvasNode[];
  selectedNodes: string[];
  lastAction?: 'add' | 'delete' | 'connect' | 'move';
  userLevel: 'beginner' | 'intermediate' | 'advanced';
  projectType?: string;
  recentComponents: string[];
  searchQuery?: string;
}

class ComponentPalette {
  private templates: ComponentTemplate[] = [];
  private categories: Map<string, ComponentTemplate[]> = new Map();
  private searchIndex: Map<string, ComponentTemplate[]> = new Map();
  private userPreferences: Record<string, any> = {};

  constructor() {
    this.initializeTemplates();
    this.buildSearchIndex();
  }

  /**
   * Get all available component templates
   */
  getTemplates(filter?: {
    category?: string;
    difficulty?: string[];
    tags?: string[];
    search?: string;
  }): ComponentTemplate[] {
    let filtered = [...this.templates];

    if (filter?.category) {
      filtered = filtered.filter(t => t.category === filter.category);
    }

    if (filter?.difficulty) {
      filtered = filtered.filter(t => filter.difficulty!.includes(t.difficulty));
    }

    if (filter?.tags) {
      filtered = filtered.filter(t => 
        filter.tags!.some(tag => t.tags.includes(tag))
      );
    }

    if (filter?.search) {
      filtered = this.searchTemplates(filter.search);
    }

    // Sort by popularity and difficulty
    return filtered.sort((a, b) => {
      const difficultyOrder = { beginner: 1, intermediate: 2, advanced: 3 };
      const aDiff = difficultyOrder[a.difficulty];
      const bDiff = difficultyOrder[b.difficulty];
      
      if (aDiff !== bDiff) return aDiff - bDiff;
      return b.popularity - a.popularity;
    });
  }

  /**
   * Get component categories
   */
  getCategories(): Array<{ name: string; count: number; icon: string }> {
    const categoryInfo = [
      { name: 'AI Agents', icon: '🤖', key: 'agents' },
      { name: 'Tools', icon: '🔧', key: 'tools' },
      { name: 'Logic', icon: '🧠', key: 'logic' },
      { name: 'Data', icon: '📊', key: 'data' },
      { name: 'Communication', icon: '💬', key: 'communication' },
      { name: 'Integration', icon: '🔗', key: 'integration' },
      { name: 'Triggers', icon: '⚡', key: 'triggers' },
      { name: 'Actions', icon: '🎯', key: 'actions' },
    ];

    return categoryInfo.map(cat => ({
      name: cat.name,
      icon: cat.icon,
      count: this.categories.get(cat.key)?.length || 0,
    }));
  }

  /**
   * Get intelligent suggestions based on context
   */
  async getSuggestions(context: PaletteContext): Promise<PaletteSuggestion[]> {
    try {
      // Try AI-powered suggestions first
      const aiSuggestions = await this.getAISuggestions(context);
      if (aiSuggestions.length > 0) {
        return aiSuggestions;
      }
    } catch (error) {
      console.warn('AI suggestions failed, using rule-based fallback:', error);
    }

    // Fallback to rule-based suggestions
    return this.getRuleBasedSuggestions(context);
  }

  /**
   * Get template by ID
   */
  getTemplate(id: string): ComponentTemplate | null {
    return this.templates.find(t => t.id === id) || null;
  }

  /**
   * Create node from template
   */
  createNodeFromTemplate(
    templateId: string,
    position: { x: number; y: number },
    customData?: Record<string, any>
  ): Omit<CanvasNode, 'id'> | null {
    const template = this.getTemplate(templateId);
    if (!template) return null;

    return {
      ...template.template,
      position,
      data: { ...template.template.data, ...customData },
    };
  }

  /**
   * Search templates
   */
  searchTemplates(query: string): ComponentTemplate[] {
    const normalizedQuery = query.toLowerCase().trim();
    if (!normalizedQuery) return this.templates;

    const results = new Set<ComponentTemplate>();

    // Search in names
    this.templates.forEach(template => {
      if (template.name.toLowerCase().includes(normalizedQuery)) {
        results.add(template);
      }
    });

    // Search in descriptions
    this.templates.forEach(template => {
      if (template.description.toLowerCase().includes(normalizedQuery)) {
        results.add(template);
      }
    });

    // Search in tags
    this.templates.forEach(template => {
      if (template.tags.some(tag => tag.toLowerCase().includes(normalizedQuery))) {
        results.add(template);
      }
    });

    // Search in search index
    for (const [term, templates] of this.searchIndex.entries()) {
      if (term.includes(normalizedQuery)) {
        templates.forEach(template => results.add(template));
      }
    }

    return Array.from(results);
  }

  /**
   * Update user preferences
   */
  updateUserPreferences(preferences: Record<string, any>): void {
    this.userPreferences = { ...this.userPreferences, ...preferences };
  }

  /**
   * Get popular templates
   */
  getPopularTemplates(limit: number = 10): ComponentTemplate[] {
    return [...this.templates]
      .sort((a, b) => b.popularity - a.popularity)
      .slice(0, limit);
  }

  /**
   * Get recent templates
   */
  getRecentTemplates(recentIds: string[]): ComponentTemplate[] {
    return recentIds
      .map(id => this.getTemplate(id))
      .filter(Boolean) as ComponentTemplate[];
  }

  /**
   * Initialize component templates
   */
  private initializeTemplates(): void {
    this.templates = [
      // AI Agents
      {
        id: 'gpt-agent',
        name: 'GPT Agent',
        description: 'Conversational AI agent powered by GPT models',
        category: 'agents',
        type: 'agent',
        icon: '🤖',
        tags: ['ai', 'conversation', 'gpt', 'openai'],
        difficulty: 'beginner',
        popularity: 95,
        template: {
          type: 'agent',
          size: { width: 180, height: 100 },
          data: {
            model: 'gpt-4',
            temperature: 0.7,
            maxTokens: 1000,
            systemPrompt: 'You are a helpful AI assistant.',
          },
          inputs: [
            { id: 'input', name: 'User Input', type: 'string', required: true },
            { id: 'context', name: 'Context', type: 'object', required: false },
          ],
          outputs: [
            { id: 'response', name: 'Response', type: 'string' },
            { id: 'metadata', name: 'Metadata', type: 'object' },
          ],
          metadata: {
            label: 'GPT Agent',
            category: 'agents',
            icon: '🤖',
          },
        },
      },
      
      {
        id: 'claude-agent',
        name: 'Claude Agent',
        description: 'Advanced AI agent using Anthropic Claude',
        category: 'agents',
        type: 'agent',
        icon: '🧠',
        tags: ['ai', 'claude', 'anthropic', 'reasoning'],
        difficulty: 'intermediate',
        popularity: 85,
        template: {
          type: 'agent',
          size: { width: 180, height: 100 },
          data: {
            model: 'claude-3-sonnet',
            temperature: 0.7,
            maxTokens: 1000,
            systemPrompt: 'You are Claude, an AI assistant created by Anthropic.',
          },
          inputs: [
            { id: 'input', name: 'User Input', type: 'string', required: true },
            { id: 'context', name: 'Context', type: 'object', required: false },
          ],
          outputs: [
            { id: 'response', name: 'Response', type: 'string' },
            { id: 'reasoning', name: 'Reasoning', type: 'string' },
          ],
          metadata: {
            label: 'Claude Agent',
            category: 'agents',
            icon: '🧠',
          },
        },
      },

      // Tools
      {
        id: 'web-scraper',
        name: 'Web Scraper',
        description: 'Extract data from web pages',
        category: 'tools',
        type: 'tool',
        icon: '🕷️',
        tags: ['web', 'scraping', 'data', 'extraction'],
        difficulty: 'intermediate',
        popularity: 75,
        template: {
          type: 'tool',
          size: { width: 160, height: 80 },
          data: {
            url: '',
            selector: '',
            timeout: 30000,
            retries: 3,
          },
          inputs: [
            { id: 'url', name: 'URL', type: 'string', required: true },
            { id: 'selector', name: 'CSS Selector', type: 'string', required: false },
          ],
          outputs: [
            { id: 'data', name: 'Extracted Data', type: 'object' },
            { id: 'error', name: 'Error', type: 'string' },
          ],
          metadata: {
            label: 'Web Scraper',
            category: 'tools',
            icon: '🕷️',
          },
        },
      },

      {
        id: 'api-call',
        name: 'API Call',
        description: 'Make HTTP requests to external APIs',
        category: 'tools',
        type: 'tool',
        icon: '🌐',
        tags: ['api', 'http', 'rest', 'integration'],
        difficulty: 'beginner',
        popularity: 90,
        template: {
          type: 'tool',
          size: { width: 160, height: 80 },
          data: {
            method: 'GET',
            url: '',
            headers: {},
            timeout: 30000,
          },
          inputs: [
            { id: 'url', name: 'URL', type: 'string', required: true },
            { id: 'params', name: 'Parameters', type: 'object', required: false },
          ],
          outputs: [
            { id: 'response', name: 'Response', type: 'object' },
            { id: 'status', name: 'Status Code', type: 'number' },
          ],
          metadata: {
            label: 'API Call',
            category: 'tools',
            icon: '🌐',
          },
        },
      },

      // Logic Components
      {
        id: 'condition',
        name: 'Condition',
        description: 'Branch workflow based on conditions',
        category: 'logic',
        type: 'condition',
        icon: '🔀',
        tags: ['logic', 'condition', 'branch', 'if'],
        difficulty: 'beginner',
        popularity: 80,
        template: {
          type: 'condition',
          size: { width: 140, height: 80 },
          data: {
            operator: 'equals',
            value: '',
          },
          inputs: [
            { id: 'input', name: 'Input Value', type: 'any', required: true },
            { id: 'compare', name: 'Compare To', type: 'any', required: false },
          ],
          outputs: [
            { id: 'true', name: 'True', type: 'any' },
            { id: 'false', name: 'False', type: 'any' },
          ],
          metadata: {
            label: 'Condition',
            category: 'logic',
            icon: '🔀',
          },
        },
      },

      // Triggers
      {
        id: 'webhook-trigger',
        name: 'Webhook Trigger',
        description: 'Start workflow from webhook events',
        category: 'triggers',
        type: 'trigger',
        icon: '🎣',
        tags: ['webhook', 'trigger', 'http', 'event'],
        difficulty: 'intermediate',
        popularity: 70,
        template: {
          type: 'trigger',
          size: { width: 160, height: 80 },
          data: {
            path: '/webhook',
            method: 'POST',
            authentication: 'none',
          },
          inputs: [],
          outputs: [
            { id: 'payload', name: 'Webhook Payload', type: 'object' },
            { id: 'headers', name: 'Headers', type: 'object' },
          ],
          metadata: {
            label: 'Webhook Trigger',
            category: 'triggers',
            icon: '🎣',
          },
        },
      },
    ];

    // Build categories map
    this.templates.forEach(template => {
      if (!this.categories.has(template.category)) {
        this.categories.set(template.category, []);
      }
      this.categories.get(template.category)!.push(template);
    });
  }

  /**
   * Build search index
   */
  private buildSearchIndex(): void {
    this.templates.forEach(template => {
      // Index by name words
      template.name.toLowerCase().split(' ').forEach(word => {
        if (!this.searchIndex.has(word)) {
          this.searchIndex.set(word, []);
        }
        this.searchIndex.get(word)!.push(template);
      });

      // Index by tags
      template.tags.forEach(tag => {
        if (!this.searchIndex.has(tag)) {
          this.searchIndex.set(tag, []);
        }
        this.searchIndex.get(tag)!.push(template);
      });

      // Index by category
      if (!this.searchIndex.has(template.category)) {
        this.searchIndex.set(template.category, []);
      }
      this.searchIndex.get(template.category)!.push(template);
    });
  }

  /**
   * Get AI-powered suggestions
   */
  private async getAISuggestions(context: PaletteContext): Promise<PaletteSuggestion[]> {
    const response = await apiClient.post('/api/v1/ai/component-suggestions', {
      context,
      availableTemplates: this.templates.map(t => ({
        id: t.id,
        name: t.name,
        category: t.category,
        tags: t.tags,
      })),
    });

    return response.data.suggestions.map((suggestion: any) => ({
      template: this.getTemplate(suggestion.templateId)!,
      confidence: suggestion.confidence,
      reasoning: suggestion.reasoning,
      context: suggestion.context,
    }));
  }

  /**
   * Get rule-based suggestions
   */
  private getRuleBasedSuggestions(context: PaletteContext): PaletteSuggestion[] {
    const suggestions: PaletteSuggestion[] = [];

    // Suggest based on user level
    if (context.userLevel === 'beginner') {
      const beginnerTemplates = this.templates.filter(t => t.difficulty === 'beginner');
      beginnerTemplates.slice(0, 3).forEach(template => {
        suggestions.push({
          template,
          confidence: 0.7,
          reasoning: 'Recommended for beginners',
          context: {},
        });
      });
    }

    // Suggest based on recent components
    if (context.recentComponents.length > 0) {
      const recentTemplate = this.getTemplate(context.recentComponents[0]);
      if (recentTemplate) {
        // Suggest complementary components
        const complementary = this.getComplementaryTemplates(recentTemplate);
        complementary.forEach(template => {
          suggestions.push({
            template,
            confidence: 0.6,
            reasoning: `Works well with ${recentTemplate.name}`,
            context: {},
          });
        });
      }
    }

    // Suggest popular components
    if (suggestions.length < 5) {
      const popular = this.getPopularTemplates(5);
      popular.forEach(template => {
        if (!suggestions.some(s => s.template.id === template.id)) {
          suggestions.push({
            template,
            confidence: 0.5,
            reasoning: 'Popular component',
            context: {},
          });
        }
      });
    }

    return suggestions.slice(0, 10);
  }

  /**
   * Get complementary templates
   */
  private getComplementaryTemplates(template: ComponentTemplate): ComponentTemplate[] {
    const complementaryMap: Record<string, string[]> = {
      'gpt-agent': ['api-call', 'condition', 'webhook-trigger'],
      'api-call': ['gpt-agent', 'condition'],
      'web-scraper': ['gpt-agent', 'api-call'],
      'condition': ['gpt-agent', 'api-call'],
    };

    const complementaryIds = complementaryMap[template.id] || [];
    return complementaryIds
      .map(id => this.getTemplate(id))
      .filter(Boolean) as ComponentTemplate[];
  }
}

export const componentPalette = new ComponentPalette();
