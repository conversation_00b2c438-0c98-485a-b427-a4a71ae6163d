import { CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { PrismaService } from '@modules/prisma/prisma.service';
export declare class PermissionsGuard implements CanActivate {
    private reflector;
    private prismaService;
    constructor(reflector: Reflector, prismaService: PrismaService);
    canActivate(context: ExecutionContext): Promise<boolean>;
    private getUserPermissions;
    private getDefaultPermissions;
}
