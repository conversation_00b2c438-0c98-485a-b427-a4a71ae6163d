import { apiClient } from '@/lib/api';

export interface SmartDefault {
  field: string;
  value: any;
  confidence: number;
  reasoning: string;
  source: 'ai' | 'analytics' | 'best_practice' | 'user_preference' | 'organization';
  conditions?: Array<{
    field: string;
    operator: 'equals' | 'contains' | 'greater_than' | 'less_than';
    value: any;
  }>;
  metadata?: Record<string, any>;
}

export interface ConfigurationLevel {
  id: string;
  name: string;
  description: string;
  fields: string[];
  dependencies?: string[];
  userLevel: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime: string;
  importance: 'essential' | 'recommended' | 'optional' | 'advanced';
}

export interface ProgressiveDisclosureConfig {
  levels: ConfigurationLevel[];
  currentLevel: number;
  completedFields: Set<string>;
  skippedFields: Set<string>;
  userPreferences: {
    showAdvanced: boolean;
    autoProgress: boolean;
    skipOptional: boolean;
  };
}

export interface SmartDefaultsContext {
  configType: 'agent' | 'tool' | 'workflow' | 'template';
  userLevel: 'beginner' | 'intermediate' | 'advanced';
  organizationId?: string;
  industryContext?: string;
  usageIntent?: string;
  existingConfigs?: Record<string, any>[];
  userPreferences?: Record<string, any>;
  environmentContext?: Record<string, any>;
}

class SmartDefaultsEngine {
  private defaultsCache: Map<string, SmartDefault[]> = new Map();
  private configurationLevels: Map<string, ConfigurationLevel[]> = new Map();

  /**
   * Get smart defaults for a configuration type
   */
  async getSmartDefaults(
    context: SmartDefaultsContext
  ): Promise<SmartDefault[]> {
    const cacheKey = this.getCacheKey(context);
    
    // Check cache first
    if (this.defaultsCache.has(cacheKey)) {
      return this.defaultsCache.get(cacheKey)!;
    }

    try {
      // Try AI-powered defaults
      const aiDefaults = await this.getAIDefaults(context);
      if (aiDefaults.length > 0) {
        this.defaultsCache.set(cacheKey, aiDefaults);
        return aiDefaults;
      }
    } catch (error) {
      console.warn('AI defaults failed, using rule-based fallback:', error);
    }

    // Fallback to rule-based defaults
    const ruleDefaults = this.getRuleBasedDefaults(context);
    this.defaultsCache.set(cacheKey, ruleDefaults);
    return ruleDefaults;
  }

  /**
   * Get progressive disclosure configuration
   */
  getProgressiveDisclosure(
    configType: string,
    userLevel: 'beginner' | 'intermediate' | 'advanced'
  ): ProgressiveDisclosureConfig {
    const levels = this.getConfigurationLevels(configType, userLevel);
    
    return {
      levels,
      currentLevel: 0,
      completedFields: new Set(),
      skippedFields: new Set(),
      userPreferences: {
        showAdvanced: userLevel === 'advanced',
        autoProgress: userLevel === 'beginner',
        skipOptional: userLevel === 'advanced',
      },
    };
  }

  /**
   * Apply smart defaults to configuration
   */
  applyDefaults(
    baseConfig: Record<string, any>,
    defaults: SmartDefault[]
  ): Record<string, any> {
    const config = { ...baseConfig };

    for (const defaultValue of defaults) {
      // Check if field already has a value
      if (this.hasValue(config, defaultValue.field)) {
        continue;
      }

      // Check conditions
      if (defaultValue.conditions && !this.evaluateConditions(config, defaultValue.conditions)) {
        continue;
      }

      // Apply default
      this.setNestedValue(config, defaultValue.field, defaultValue.value);
    }

    return config;
  }

  /**
   * Get next configuration level
   */
  getNextLevel(
    disclosure: ProgressiveDisclosureConfig,
    currentConfig: Record<string, any>
  ): ConfigurationLevel | null {
    const currentLevel = disclosure.levels[disclosure.currentLevel];
    if (!currentLevel) return null;

    // Check if current level is complete
    const requiredFields = currentLevel.fields.filter(field => 
      currentLevel.importance === 'essential' || 
      (currentLevel.importance === 'recommended' && !disclosure.userPreferences.skipOptional)
    );

    const completedRequired = requiredFields.filter(field => 
      disclosure.completedFields.has(field) || this.hasValue(currentConfig, field)
    );

    if (completedRequired.length === requiredFields.length) {
      const nextIndex = disclosure.currentLevel + 1;
      return disclosure.levels[nextIndex] || null;
    }

    return currentLevel;
  }

  /**
   * Validate field completion
   */
  validateFieldCompletion(
    field: string,
    value: any,
    level: ConfigurationLevel
  ): {
    isValid: boolean;
    isComplete: boolean;
    suggestions: string[];
  } {
    const isValid = value !== undefined && value !== null && value !== '';
    const isComplete = isValid && this.isFieldComplete(field, value, level);
    const suggestions: string[] = [];

    if (!isValid) {
      suggestions.push(`Please provide a value for ${field}`);
    } else if (!isComplete) {
      suggestions.push(`Consider providing more details for ${field}`);
    }

    return { isValid, isComplete, suggestions };
  }

  /**
   * Get field recommendations based on context
   */
  async getFieldRecommendations(
    field: string,
    context: SmartDefaultsContext,
    currentValue?: any
  ): Promise<Array<{
    value: any;
    label: string;
    description: string;
    confidence: number;
    isDefault: boolean;
  }>> {
    try {
      const response = await apiClient.post('/api/v1/ai/field-recommendations', {
        field,
        context,
        currentValue,
      });
      return response.data.recommendations;
    } catch (error) {
      return this.getRuleBasedRecommendations(field, context, currentValue);
    }
  }

  /**
   * Get configuration templates based on intent
   */
  async getConfigTemplates(
    context: SmartDefaultsContext
  ): Promise<Array<{
    id: string;
    name: string;
    description: string;
    config: Record<string, any>;
    tags: string[];
    popularity: number;
    difficulty: 'beginner' | 'intermediate' | 'advanced';
  }>> {
    try {
      const response = await apiClient.post('/api/v1/ai/config-templates', context);
      return response.data.templates;
    } catch (error) {
      return this.getStaticTemplates(context.configType);
    }
  }

  /**
   * AI-powered defaults from backend
   */
  private async getAIDefaults(context: SmartDefaultsContext): Promise<SmartDefault[]> {
    const response = await apiClient.post('/api/v1/ai/smart-defaults', context);
    return response.data.defaults;
  }

  /**
   * Rule-based defaults fallback
   */
  private getRuleBasedDefaults(context: SmartDefaultsContext): SmartDefault[] {
    const defaults: SmartDefault[] = [];

    switch (context.configType) {
      case 'agent':
        defaults.push(...this.getAgentDefaults(context));
        break;
      case 'tool':
        defaults.push(...this.getToolDefaults(context));
        break;
      case 'workflow':
        defaults.push(...this.getWorkflowDefaults(context));
        break;
    }

    return defaults;
  }

  /**
   * Agent-specific defaults
   */
  private getAgentDefaults(context: SmartDefaultsContext): SmartDefault[] {
    const defaults: SmartDefault[] = [
      {
        field: 'name',
        value: 'My AI Agent',
        confidence: 0.6,
        reasoning: 'Generic default name for new agents',
        source: 'best_practice',
      },
      {
        field: 'model',
        value: context.userLevel === 'beginner' ? 'gpt-3.5-turbo' : 'gpt-4',
        confidence: 0.8,
        reasoning: 'Model selection based on user experience level',
        source: 'best_practice',
      },
      {
        field: 'temperature',
        value: 0.7,
        confidence: 0.9,
        reasoning: 'Balanced temperature for most use cases',
        source: 'best_practice',
      },
      {
        field: 'maxTokens',
        value: context.userLevel === 'beginner' ? 500 : 1000,
        confidence: 0.8,
        reasoning: 'Token limit based on user experience',
        source: 'best_practice',
      },
      {
        field: 'systemPrompt',
        value: 'You are a helpful AI assistant. Be concise, accurate, and professional in your responses.',
        confidence: 0.7,
        reasoning: 'Standard system prompt for general-purpose agents',
        source: 'best_practice',
      },
    ];

    // Industry-specific defaults
    if (context.industryContext) {
      defaults.push(...this.getIndustryDefaults(context.industryContext));
    }

    return defaults;
  }

  /**
   * Tool-specific defaults
   */
  private getToolDefaults(context: SmartDefaultsContext): SmartDefault[] {
    return [
      {
        field: 'name',
        value: 'New Tool',
        confidence: 0.6,
        reasoning: 'Generic default name for new tools',
        source: 'best_practice',
      },
      {
        field: 'timeout',
        value: 30000,
        confidence: 0.9,
        reasoning: 'Standard timeout for most API calls',
        source: 'best_practice',
      },
      {
        field: 'retries',
        value: 3,
        confidence: 0.8,
        reasoning: 'Reasonable retry count for reliability',
        source: 'best_practice',
      },
    ];
  }

  /**
   * Workflow-specific defaults
   */
  private getWorkflowDefaults(context: SmartDefaultsContext): SmartDefault[] {
    return [
      {
        field: 'name',
        value: 'New Workflow',
        confidence: 0.6,
        reasoning: 'Generic default name for new workflows',
        source: 'best_practice',
      },
      {
        field: 'errorHandling.retries',
        value: 3,
        confidence: 0.8,
        reasoning: 'Standard retry count for workflow steps',
        source: 'best_practice',
      },
      {
        field: 'errorHandling.fallback',
        value: 'continue',
        confidence: 0.7,
        reasoning: 'Continue execution on non-critical errors',
        source: 'best_practice',
      },
    ];
  }

  /**
   * Industry-specific defaults
   */
  private getIndustryDefaults(industry: string): SmartDefault[] {
    const industryDefaults: Record<string, SmartDefault[]> = {
      healthcare: [
        {
          field: 'systemPrompt',
          value: 'You are a healthcare AI assistant. Always remind users to consult with healthcare professionals for medical advice.',
          confidence: 0.9,
          reasoning: 'Healthcare compliance and safety',
          source: 'best_practice',
        },
      ],
      finance: [
        {
          field: 'systemPrompt',
          value: 'You are a financial AI assistant. Provide general information only and remind users to consult financial advisors.',
          confidence: 0.9,
          reasoning: 'Financial compliance and disclaimers',
          source: 'best_practice',
        },
      ],
    };

    return industryDefaults[industry] || [];
  }

  /**
   * Get configuration levels for progressive disclosure
   */
  private getConfigurationLevels(
    configType: string,
    userLevel: 'beginner' | 'intermediate' | 'advanced'
  ): ConfigurationLevel[] {
    const cacheKey = `${configType}-${userLevel}`;
    
    if (this.configurationLevels.has(cacheKey)) {
      return this.configurationLevels.get(cacheKey)!;
    }

    let levels: ConfigurationLevel[] = [];

    switch (configType) {
      case 'agent':
        levels = this.getAgentLevels(userLevel);
        break;
      case 'tool':
        levels = this.getToolLevels(userLevel);
        break;
      case 'workflow':
        levels = this.getWorkflowLevels(userLevel);
        break;
    }

    this.configurationLevels.set(cacheKey, levels);
    return levels;
  }

  /**
   * Agent configuration levels
   */
  private getAgentLevels(userLevel: string): ConfigurationLevel[] {
    const levels: ConfigurationLevel[] = [
      {
        id: 'basic',
        name: 'Basic Information',
        description: 'Essential details to get your agent started',
        fields: ['name', 'description'],
        userLevel: 'beginner',
        estimatedTime: '2 minutes',
        importance: 'essential',
      },
      {
        id: 'behavior',
        name: 'Agent Behavior',
        description: 'Configure how your agent responds and behaves',
        fields: ['systemPrompt', 'model', 'temperature'],
        dependencies: ['basic'],
        userLevel: 'beginner',
        estimatedTime: '5 minutes',
        importance: 'essential',
      },
      {
        id: 'limits',
        name: 'Response Limits',
        description: 'Set boundaries for agent responses',
        fields: ['maxTokens', 'responseFormat'],
        dependencies: ['behavior'],
        userLevel: 'intermediate',
        estimatedTime: '3 minutes',
        importance: 'recommended',
      },
      {
        id: 'advanced',
        name: 'Advanced Settings',
        description: 'Fine-tune advanced parameters',
        fields: ['topP', 'frequencyPenalty', 'presencePenalty', 'stopSequences'],
        dependencies: ['limits'],
        userLevel: 'advanced',
        estimatedTime: '10 minutes',
        importance: 'optional',
      },
    ];

    // Filter levels based on user level
    return levels.filter(level => {
      const levelOrder = { beginner: 1, intermediate: 2, advanced: 3 };
      const userOrder = levelOrder[userLevel as keyof typeof levelOrder];
      const requiredOrder = levelOrder[level.userLevel as keyof typeof levelOrder];
      return requiredOrder <= userOrder;
    });
  }

  /**
   * Tool configuration levels
   */
  private getToolLevels(userLevel: string): ConfigurationLevel[] {
    return [
      {
        id: 'basic',
        name: 'Basic Information',
        description: 'Essential tool details',
        fields: ['name', 'description', 'type'],
        userLevel: 'beginner',
        estimatedTime: '2 minutes',
        importance: 'essential',
      },
      {
        id: 'configuration',
        name: 'Tool Configuration',
        description: 'Configure tool parameters',
        fields: ['endpoint', 'method', 'headers'],
        dependencies: ['basic'],
        userLevel: 'intermediate',
        estimatedTime: '5 minutes',
        importance: 'essential',
      },
      {
        id: 'reliability',
        name: 'Reliability Settings',
        description: 'Configure error handling and retries',
        fields: ['timeout', 'retries', 'errorHandling'],
        dependencies: ['configuration'],
        userLevel: 'intermediate',
        estimatedTime: '3 minutes',
        importance: 'recommended',
      },
    ];
  }

  /**
   * Workflow configuration levels
   */
  private getWorkflowLevels(userLevel: string): ConfigurationLevel[] {
    return [
      {
        id: 'basic',
        name: 'Basic Information',
        description: 'Essential workflow details',
        fields: ['name', 'description'],
        userLevel: 'beginner',
        estimatedTime: '2 minutes',
        importance: 'essential',
      },
      {
        id: 'steps',
        name: 'Workflow Steps',
        description: 'Define the workflow steps',
        fields: ['steps', 'triggers'],
        dependencies: ['basic'],
        userLevel: 'intermediate',
        estimatedTime: '10 minutes',
        importance: 'essential',
      },
      {
        id: 'advanced',
        name: 'Advanced Settings',
        description: 'Error handling and optimization',
        fields: ['errorHandling', 'parallelExecution', 'scheduling'],
        dependencies: ['steps'],
        userLevel: 'advanced',
        estimatedTime: '8 minutes',
        importance: 'recommended',
      },
    ];
  }

  /**
   * Utility methods
   */
  private getCacheKey(context: SmartDefaultsContext): string {
    return `${context.configType}-${context.userLevel}-${context.organizationId || 'default'}`;
  }

  private hasValue(obj: any, path: string): boolean {
    const keys = path.split('.');
    let current = obj;
    
    for (const key of keys) {
      if (current == null || !(key in current)) {
        return false;
      }
      current = current[key];
    }
    
    return current !== undefined && current !== null && current !== '';
  }

  private setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    let current = obj;
    
    for (let i = 0; i < keys.length - 1; i++) {
      if (!(keys[i] in current)) {
        current[keys[i]] = {};
      }
      current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]] = value;
  }

  private evaluateConditions(
    config: Record<string, any>,
    conditions: SmartDefault['conditions']
  ): boolean {
    if (!conditions) return true;

    return conditions.every(condition => {
      const value = this.getNestedValue(config, condition.field);
      
      switch (condition.operator) {
        case 'equals':
          return value === condition.value;
        case 'contains':
          return String(value).includes(String(condition.value));
        case 'greater_than':
          return Number(value) > Number(condition.value);
        case 'less_than':
          return Number(value) < Number(condition.value);
        default:
          return false;
      }
    });
  }

  private getNestedValue(obj: any, path: string): any {
    const keys = path.split('.');
    let current = obj;
    
    for (const key of keys) {
      if (current == null || !(key in current)) {
        return undefined;
      }
      current = current[key];
    }
    
    return current;
  }

  private isFieldComplete(field: string, value: any, level: ConfigurationLevel): boolean {
    // Field-specific completion logic
    if (field === 'description') {
      return typeof value === 'string' && value.length >= 20;
    }
    
    if (field === 'systemPrompt') {
      return typeof value === 'string' && value.length >= 50;
    }
    
    return value !== undefined && value !== null && value !== '';
  }

  private getRuleBasedRecommendations(
    field: string,
    context: SmartDefaultsContext,
    currentValue?: any
  ): any[] {
    // Simple rule-based recommendations
    return [];
  }

  private getStaticTemplates(configType: string): any[] {
    // Static template fallbacks
    return [];
  }
}

export const smartDefaultsEngine = new SmartDefaultsEngine();
