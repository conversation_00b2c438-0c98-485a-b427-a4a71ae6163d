"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RedisService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const ioredis_1 = require("ioredis");
let RedisService = RedisService_1 = class RedisService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(RedisService_1.name);
    }
    async onModuleInit() {
        try {
            await this.initializeClients();
            this.logger.log('Successfully connected to Redis');
        }
        catch (error) {
            this.logger.error('Failed to connect to Redis:', error);
            throw error;
        }
    }
    async onModuleDestroy() {
        try {
            await Promise.all([
                this.client?.quit(),
                this.pubClient?.quit(),
                this.subClient?.quit(),
            ]);
            this.logger.log('Successfully disconnected from Redis');
        }
        catch (error) {
            this.logger.error('Error disconnecting from Redis:', error);
        }
    }
    async initializeClients() {
        const redisConfig = {
            host: this.configService.get('REDIS_HOST', 'localhost'),
            port: this.configService.get('REDIS_PORT', 6379),
            password: this.configService.get('REDIS_PASSWORD'),
            db: 0,
            retryDelayOnFailover: 100,
            enableReadyCheck: true,
            maxRetriesPerRequest: 3,
            lazyConnect: true,
            keepAlive: 30000,
            connectTimeout: 10000,
            commandTimeout: 5000,
        };
        const clusterNodes = this.configService.get('REDIS_CLUSTER_NODES');
        if (clusterNodes) {
            const nodes = clusterNodes.split(',').map(node => {
                const [host, port] = node.split(':');
                return { host, port: parseInt(port) };
            });
            this.client = new ioredis_1.default.Cluster(nodes, {
                redisOptions: redisConfig,
                enableOfflineQueue: false,
            });
            this.pubClient = new ioredis_1.default.Cluster(nodes, {
                redisOptions: redisConfig,
                enableOfflineQueue: false,
            });
            this.subClient = new ioredis_1.default.Cluster(nodes, {
                redisOptions: redisConfig,
                enableOfflineQueue: false,
            });
        }
        else {
            this.client = new ioredis_1.default(redisConfig);
            this.pubClient = new ioredis_1.default(redisConfig);
            this.subClient = new ioredis_1.default(redisConfig);
        }
        this.setupEventListeners();
        await Promise.all([
            this.client.connect(),
            this.pubClient.connect(),
            this.subClient.connect(),
        ]);
    }
    setupEventListeners() {
        const clients = [
            { client: this.client, name: 'main' },
            { client: this.pubClient, name: 'pub' },
            { client: this.subClient, name: 'sub' },
        ];
        clients.forEach(({ client, name }) => {
            client.on('connect', () => {
                this.logger.log(`Redis ${name} client connected`);
            });
            client.on('ready', () => {
                this.logger.log(`Redis ${name} client ready`);
            });
            client.on('error', (error) => {
                this.logger.error(`Redis ${name} client error:`, error);
            });
            client.on('close', () => {
                this.logger.warn(`Redis ${name} client connection closed`);
            });
            client.on('reconnecting', () => {
                this.logger.log(`Redis ${name} client reconnecting`);
            });
        });
    }
    getClient() {
        return this.client;
    }
    getPubSubClients() {
        return {
            pub: this.pubClient,
            sub: this.subClient,
        };
    }
    async createSession(organizationId, userId, agentId, context = {}) {
        const sessionId = `session:${organizationId}:${Date.now()}:${Math.random().toString(36).substr(2, 9)}`;
        const sessionData = {
            organizationId,
            userId,
            agentId,
            context,
            createdAt: new Date().toISOString(),
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        };
        const namespacedKey = this.getNamespacedKey(sessionId, organizationId);
        await this.client.setex(namespacedKey, 24 * 60 * 60, JSON.stringify(sessionData));
        return sessionId;
    }
    async getSession(sessionId, organizationId) {
        const namespacedKey = organizationId ?
            this.getNamespacedKey(sessionId, organizationId) :
            sessionId;
        const data = await this.client.get(namespacedKey);
        return data ? JSON.parse(data) : null;
    }
    async updateSession(sessionId, context, organizationId) {
        const session = await this.getSession(sessionId, organizationId);
        if (session) {
            session.context = { ...session.context, ...context };
            session.updatedAt = new Date().toISOString();
            const namespacedKey = organizationId ?
                this.getNamespacedKey(sessionId, organizationId) :
                sessionId;
            await this.client.setex(namespacedKey, 24 * 60 * 60, JSON.stringify(session));
        }
        return session;
    }
    async deleteSession(sessionId, organizationId) {
        const namespacedKey = organizationId ?
            this.getNamespacedKey(sessionId, organizationId) :
            sessionId;
        await this.client.del(namespacedKey);
    }
    async getOrganizationSessions(organizationId) {
        const pattern = this.getNamespacedKey('session:*', organizationId);
        const keys = await this.client.keys(pattern);
        if (keys.length === 0) {
            return [];
        }
        const sessions = await this.client.mget(keys);
        return sessions
            .filter(session => session !== null)
            .map(session => JSON.parse(session));
    }
    async cleanupExpiredSessions(organizationId) {
        const sessions = await this.getOrganizationSessions(organizationId);
        const now = new Date();
        for (const session of sessions) {
            if (new Date(session.expiresAt) < now) {
                await this.deleteSession(session.sessionId, organizationId);
            }
        }
    }
    async set(key, value, ttl = 3600, organizationId) {
        const namespacedKey = organizationId ?
            this.getNamespacedKey(key, organizationId) :
            key;
        await this.client.setex(namespacedKey, ttl, JSON.stringify(value));
    }
    async get(key, organizationId) {
        const namespacedKey = organizationId ?
            this.getNamespacedKey(key, organizationId) :
            key;
        const data = await this.client.get(namespacedKey);
        return data ? JSON.parse(data) : null;
    }
    async del(key, organizationId) {
        const namespacedKey = organizationId ?
            this.getNamespacedKey(key, organizationId) :
            key;
        await this.client.del(namespacedKey);
    }
    async exists(key, organizationId) {
        const namespacedKey = organizationId ?
            this.getNamespacedKey(key, organizationId) :
            key;
        return await this.client.exists(namespacedKey);
    }
    async getOrganizationKeys(organizationId, pattern = '*') {
        const namespacedPattern = this.getNamespacedKey(pattern, organizationId);
        return await this.client.keys(namespacedPattern);
    }
    async deleteOrganizationData(organizationId) {
        const keys = await this.getOrganizationKeys(organizationId);
        if (keys.length > 0) {
            await this.client.del(...keys);
        }
        return keys.length;
    }
    getNamespacedKey(key, organizationId) {
        return `org:${organizationId}:${key}`;
    }
    async keys(pattern) {
        return this.client.keys(pattern);
    }
    async setex(key, ttl, value, organizationId) {
        return this.set(key, value, ttl, organizationId);
    }
    async isHealthy() {
        try {
            const result = await this.client.ping();
            return result === 'PONG';
        }
        catch (error) {
            this.logger.error('Redis health check failed:', error);
            return false;
        }
    }
    async publish(channel, message) {
        await this.pubClient.publish(channel, JSON.stringify(message));
    }
    async subscribe(channel, callback) {
        await this.subClient.subscribe(channel);
        this.subClient.on('message', (receivedChannel, message) => {
            if (receivedChannel === channel) {
                try {
                    const parsedMessage = JSON.parse(message);
                    callback(parsedMessage);
                }
                catch (error) {
                    this.logger.error('Error parsing pub/sub message:', error);
                }
            }
        });
    }
    async unsubscribe(channel) {
        await this.subClient.unsubscribe(channel);
    }
};
exports.RedisService = RedisService;
exports.RedisService = RedisService = RedisService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], RedisService);
//# sourceMappingURL=redis.service.js.map