"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const config_1 = require("@nestjs/config");
const microservices_1 = require("@nestjs/microservices");
const helmet_1 = require("helmet");
const compression_1 = require("compression");
const app_module_1 = require("./app.module");
const logger_service_1 = require("./services/logger/logger.service");
const sentry_service_1 = require("./services/monitoring/sentry.service");
async function bootstrap() {
    const sentryService = new sentry_service_1.SentryService();
    sentryService.init();
    const app = await core_1.NestFactory.create(app_module_1.AppModule, {
        logger: ['log', 'error', 'warn', 'debug', 'verbose'],
    });
    const configService = app.get(config_1.ConfigService);
    const logger = new common_1.Logger('Bootstrap');
    const loggerService = app.get(logger_service_1.LoggerService);
    app.use((0, helmet_1.default)({
        contentSecurityPolicy: {
            directives: {
                defaultSrc: ["'self'"],
                styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
                scriptSrc: ["'self'", "'unsafe-eval'"],
                imgSrc: ["'self'", "data:", "https:"],
                fontSrc: ["'self'", "https://fonts.gstatic.com"],
                connectSrc: ["'self'", "https://api.openai.com", "https://api.anthropic.com"],
                frameSrc: ["'none'"],
                objectSrc: ["'none'"],
                baseUri: ["'self'"],
                formAction: ["'self'"],
            },
        },
        crossOriginEmbedderPolicy: false,
        hsts: {
            maxAge: 31536000,
            includeSubDomains: true,
            preload: true,
        },
        noSniff: true,
        xssFilter: true,
        referrerPolicy: { policy: "strict-origin-when-cross-origin" },
    }));
    app.use((0, compression_1.default)());
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
        transformOptions: {
            enableImplicitConversion: true,
        },
    }));
    app.enableCors({
        origin: configService.get('CORS_ORIGINS', '*').split(','),
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-Organization-Id'],
    });
    const config = new swagger_1.DocumentBuilder()
        .setTitle('SynapseAI API')
        .setDescription('Universal AI Orchestration Platform API')
        .setVersion('1.0.0')
        .addBearerAuth()
        .addTag('Authentication', 'User authentication and authorization')
        .addTag('Organizations', 'Organization management')
        .addTag('Health', 'Health check endpoints')
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, config);
    swagger_1.SwaggerModule.setup('api/docs', app, document, {
        swaggerOptions: {
            persistAuthorization: true,
        },
    });
    const microserviceOptions = {
        transport: microservices_1.Transport.REDIS,
        options: {
            host: configService.get('REDIS_HOST', 'localhost'),
            port: configService.get('REDIS_PORT', 6379),
            password: configService.get('REDIS_PASSWORD'),
            retryAttempts: 5,
            retryDelay: 3000,
        },
    };
    app.connectMicroservice(microserviceOptions);
    await app.startAllMicroservices();
    logger.log('Microservices started successfully');
    const port = configService.get('PORT', 3001);
    const host = configService.get('HOST', '0.0.0.0');
    await app.listen(port, host);
    logger.log(`SynapseAI Backend running on http://${host}:${port}`);
    logger.log(`API Documentation: http://${host}:${port}/api/docs`);
    logger.log(`Environment: ${configService.get('NODE_ENV', 'development')}`);
    loggerService.log('Application bootstrap completed successfully', 'Bootstrap');
}
bootstrap().catch((error) => {
    const logger = new common_1.Logger('Bootstrap');
    logger.error('Failed to start application', error);
    process.exit(1);
});
//# sourceMappingURL=main.js.map