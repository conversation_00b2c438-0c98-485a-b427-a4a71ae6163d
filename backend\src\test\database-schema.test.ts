import { Test, TestingModule } from '@nestjs/testing';
import { PrismaService } from '@modules/prisma/prisma.service';
import { RedisService } from '@modules/redis/redis.service';
import { TenantService } from '@services/tenant/tenant.service';
import { LoggerService } from '@services/logger/logger.service';
import { ConfigService } from '@nestjs/config';

describe('Database Schema and Multi-Tenancy', () => {
  let prismaService: PrismaService;
  let redisService: RedisService;
  let tenantService: TenantService;
  let module: TestingModule;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      providers: [
        PrismaService,
        RedisService,
        TenantService,
        LoggerService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              const config = {
                DATABASE_URL: 'postgresql://test:test@localhost:5432/test',
                REDIS_HOST: 'localhost',
                REDIS_PORT: 6379,
                NODE_ENV: 'test',
                LOG_LEVEL: 'error',
              };
              return config[key];
            }),
          },
        },
      ],
    }).compile();

    prismaService = module.get<PrismaService>(PrismaService);
    redisService = module.get<RedisService>(RedisService);
    tenantService = module.get<TenantService>(TenantService);
  });

  afterAll(async () => {
    await module.close();
  });

  describe('Prisma Schema Validation', () => {
    it('should have all required models defined', () => {
      // Test that Prisma client has all expected models
      expect(prismaService.organization).toBeDefined();
      expect(prismaService.user).toBeDefined();
      expect(prismaService.role).toBeDefined();
      expect(prismaService.session).toBeDefined();
      expect(prismaService.template).toBeDefined();
      expect(prismaService.templateVersion).toBeDefined();
      expect(prismaService.agent).toBeDefined();
      expect(prismaService.agentExecution).toBeDefined();
      expect(prismaService.tool).toBeDefined();
      expect(prismaService.toolExecution).toBeDefined();
      expect(prismaService.hybrid).toBeDefined();
      expect(prismaService.hybridExecution).toBeDefined();
      expect(prismaService.workflow).toBeDefined();
      expect(prismaService.workflowExecution).toBeDefined();
      expect(prismaService.provider).toBeDefined();
      expect(prismaService.providerUsage).toBeDefined();
      expect(prismaService.hITLRequest).toBeDefined();
      expect(prismaService.hITLDecision).toBeDefined();
      expect(prismaService.document).toBeDefined();
      expect(prismaService.documentChunk).toBeDefined();
      expect(prismaService.knowledgeSearch).toBeDefined();
      expect(prismaService.widget).toBeDefined();
      expect(prismaService.widgetExecution).toBeDefined();
      expect(prismaService.analytics).toBeDefined();
      expect(prismaService.metrics).toBeDefined();
      expect(prismaService.billing).toBeDefined();
      expect(prismaService.usageMeter).toBeDefined();
      expect(prismaService.quota).toBeDefined();
      expect(prismaService.notification).toBeDefined();
      expect(prismaService.notificationPreference).toBeDefined();
      expect(prismaService.sandbox).toBeDefined();
      expect(prismaService.testResult).toBeDefined();
    });

    it('should have organization-scoped query helper', () => {
      const orgId = 'test-org-id';
      const orgQueries = prismaService.forOrganization(orgId);
      
      expect(orgQueries).toBeDefined();
      expect(orgQueries.users).toBeDefined();
      expect(orgQueries.agents).toBeDefined();
      expect(orgQueries.tools).toBeDefined();
      expect(orgQueries.workflows).toBeDefined();
    });

    it('should validate organization access', async () => {
      const mockValidation = jest.spyOn(prismaService, 'validateOrganizationAccess');
      mockValidation.mockResolvedValue(true);

      const result = await prismaService.validateOrganizationAccess('user-id', 'org-id');
      expect(result).toBe(true);
      expect(mockValidation).toHaveBeenCalledWith('user-id', 'org-id');

      mockValidation.mockRestore();
    });
  });

  describe('Multi-Tenant Architecture', () => {
    it('should create tenant-aware client', () => {
      const context = {
        organizationId: 'test-org',
        userId: 'test-user',
        userRole: 'DEVELOPER' as const,
        permissions: ['agents:read', 'tools:write'],
      };

      const tenantClient = tenantService.createTenantClient(context);
      
      expect(tenantClient).toBeDefined();
      expect(tenantClient.organization).toBeDefined();
      expect(tenantClient.user).toBeDefined();
      expect(tenantClient.agent).toBeDefined();
      expect(tenantClient.tool).toBeDefined();
      expect(tenantClient.session).toBeDefined();
      expect(tenantClient.analytics).toBeDefined();
      expect(tenantClient.billing).toBeDefined();
    });

    it('should validate resource access', async () => {
      const context = {
        organizationId: 'test-org',
        userId: 'test-user',
        userRole: 'DEVELOPER' as const,
        permissions: ['agents:read', 'agents:write'],
      };

      const mockValidation = jest.spyOn(tenantService, 'validateResourceAccess');
      mockValidation.mockResolvedValue(true);

      const result = await tenantService.validateResourceAccess(
        context,
        'agents',
        'read',
        'agent-id'
      );

      expect(result).toBe(true);
      expect(mockValidation).toHaveBeenCalledWith(
        context,
        'agents',
        'read',
        'agent-id'
      );

      mockValidation.mockRestore();
    });

    it('should enforce organization boundary', async () => {
      const mockEnforcement = jest.spyOn(tenantService, 'enforceOrganizationBoundary');
      mockEnforcement.mockResolvedValue();

      await tenantService.enforceOrganizationBoundary('org-id', 'user-id');
      
      expect(mockEnforcement).toHaveBeenCalledWith('org-id', 'user-id');

      mockEnforcement.mockRestore();
    });
  });

  describe('Redis Namespacing', () => {
    it('should create namespaced session', async () => {
      const mockCreate = jest.spyOn(redisService, 'createSession');
      mockCreate.mockResolvedValue('session:org-id:123456:abc123');

      const sessionId = await redisService.createSession(
        'org-id',
        'user-id',
        'agent-id',
        { test: 'context' }
      );

      expect(sessionId).toContain('session:org-id:');
      expect(mockCreate).toHaveBeenCalledWith(
        'org-id',
        'user-id',
        'agent-id',
        { test: 'context' }
      );

      mockCreate.mockRestore();
    });

    it('should get organization sessions', async () => {
      const mockGetSessions = jest.spyOn(redisService, 'getOrganizationSessions');
      mockGetSessions.mockResolvedValue([
        {
          sessionId: 'session:org-id:123456:abc123',
          organizationId: 'org-id',
          userId: 'user-id',
          context: {},
          createdAt: new Date().toISOString(),
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        },
      ]);

      const sessions = await redisService.getOrganizationSessions('org-id');
      
      expect(sessions).toHaveLength(1);
      expect(sessions[0].organizationId).toBe('org-id');
      expect(mockGetSessions).toHaveBeenCalledWith('org-id');

      mockGetSessions.mockRestore();
    });

    it('should handle tenant-aware caching', async () => {
      const mockSet = jest.spyOn(redisService, 'set');
      const mockGet = jest.spyOn(redisService, 'get');
      
      mockSet.mockResolvedValue();
      mockGet.mockResolvedValue({ test: 'data' });

      await redisService.set('test-key', { test: 'data' }, 3600, 'org-id');
      const result = await redisService.get('test-key', 'org-id');

      expect(mockSet).toHaveBeenCalledWith('test-key', { test: 'data' }, 3600, 'org-id');
      expect(mockGet).toHaveBeenCalledWith('test-key', 'org-id');
      expect(result).toEqual({ test: 'data' });

      mockSet.mockRestore();
      mockGet.mockRestore();
    });

    it('should delete organization data', async () => {
      const mockDelete = jest.spyOn(redisService, 'deleteOrganizationData');
      mockDelete.mockResolvedValue(5); // 5 keys deleted

      const deletedCount = await redisService.deleteOrganizationData('org-id');
      
      expect(deletedCount).toBe(5);
      expect(mockDelete).toHaveBeenCalledWith('org-id');

      mockDelete.mockRestore();
    });
  });

  describe('Database Relationships', () => {
    it('should have proper foreign key relationships', () => {
      // This test verifies that the Prisma schema has proper relationships
      // In a real test environment, you would test actual database constraints
      
      // Test organization -> users relationship
      expect(prismaService.organization.findFirst).toBeDefined();
      expect(prismaService.user.findMany).toBeDefined();
      
      // Test user -> created entities relationships
      expect(prismaService.agent.findMany).toBeDefined();
      expect(prismaService.tool.findMany).toBeDefined();
      expect(prismaService.workflow.findMany).toBeDefined();
      
      // Test execution relationships
      expect(prismaService.agentExecution.findMany).toBeDefined();
      expect(prismaService.toolExecution.findMany).toBeDefined();
      expect(prismaService.workflowExecution.findMany).toBeDefined();
    });
  });

  describe('Database Indexing', () => {
    it('should have indexes on organization foreign keys', () => {
      // In a real test, you would query the database to verify indexes exist
      // For now, we verify the schema structure supports indexed queries
      
      expect(prismaService.user.findMany).toBeDefined();
      expect(prismaService.agent.findMany).toBeDefined();
      expect(prismaService.tool.findMany).toBeDefined();
      expect(prismaService.workflow.findMany).toBeDefined();
    });

    it('should support efficient tenant-scoped queries', () => {
      // Verify that tenant-scoped queries are properly structured
      const orgQueries = prismaService.forOrganization('test-org');
      
      expect(orgQueries.users).toBeDefined();
      expect(orgQueries.agents).toBeDefined();
      expect(orgQueries.tools).toBeDefined();
      expect(orgQueries.workflows).toBeDefined();
      expect(orgQueries.analytics).toBeDefined();
      expect(orgQueries.billing).toBeDefined();
    });
  });
});
