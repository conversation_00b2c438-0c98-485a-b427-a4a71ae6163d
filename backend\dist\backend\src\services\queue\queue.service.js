"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var QueueService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueueService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const bull_1 = require("@nestjs/bull");
const redis_service_1 = require("../../modules/redis/redis.service");
let QueueService = QueueService_1 = class QueueService {
    constructor(configService, redisService, agentQueue, toolQueue, workflowQueue, notificationQueue, analyticsQueue, billingQueue) {
        this.configService = configService;
        this.redisService = redisService;
        this.agentQueue = agentQueue;
        this.toolQueue = toolQueue;
        this.workflowQueue = workflowQueue;
        this.notificationQueue = notificationQueue;
        this.analyticsQueue = analyticsQueue;
        this.billingQueue = billingQueue;
        this.logger = new common_1.Logger(QueueService_1.name);
    }
    async onModuleInit() {
        await this.setupQueueProcessors();
        await this.setupQueueEvents();
        this.logger.log('Queue service initialized successfully');
    }
    async onModuleDestroy() {
        const queues = [
            this.agentQueue,
            this.toolQueue,
            this.workflowQueue,
            this.notificationQueue,
            this.analyticsQueue,
            this.billingQueue,
        ];
        await Promise.all(queues.map(queue => queue.close()));
        this.logger.log('Queue service shut down successfully');
    }
    async addAgentJob(jobData, options = {}) {
        const defaultOptions = {
            priority: jobData.priority || 0,
            attempts: 3,
            backoff: {
                type: 'exponential',
                delay: 2000,
            },
            removeOnComplete: 100,
            removeOnFail: 50,
        };
        return this.agentQueue.add('execute-agent', jobData, {
            ...defaultOptions,
            ...options,
        });
    }
    async addToolJob(jobData, options = {}) {
        const defaultOptions = {
            priority: jobData.priority || 0,
            attempts: 3,
            backoff: {
                type: 'exponential',
                delay: 1000,
            },
            removeOnComplete: 100,
            removeOnFail: 50,
        };
        return this.toolQueue.add('execute-tool', jobData, {
            ...defaultOptions,
            ...options,
        });
    }
    async addWorkflowJob(jobData, options = {}) {
        const defaultOptions = {
            priority: jobData.priority || 0,
            attempts: 5,
            backoff: {
                type: 'exponential',
                delay: 3000,
            },
            removeOnComplete: 50,
            removeOnFail: 25,
        };
        return this.workflowQueue.add('execute-workflow', jobData, {
            ...defaultOptions,
            ...options,
        });
    }
    async addNotificationJob(jobData, options = {}) {
        const defaultOptions = {
            priority: jobData.priority || 5,
            attempts: 3,
            backoff: {
                type: 'fixed',
                delay: 5000,
            },
            removeOnComplete: 200,
            removeOnFail: 100,
        };
        return this.notificationQueue.add('send-notification', jobData, {
            ...defaultOptions,
            ...options,
        });
    }
    async addAnalyticsJob(jobData, options = {}) {
        const defaultOptions = {
            priority: jobData.priority || -5,
            attempts: 2,
            backoff: {
                type: 'fixed',
                delay: 10000,
            },
            removeOnComplete: 500,
            removeOnFail: 100,
        };
        return this.analyticsQueue.add('process-analytics', jobData, {
            ...defaultOptions,
            ...options,
        });
    }
    async addBillingJob(jobData, options = {}) {
        const defaultOptions = {
            priority: jobData.priority || 10,
            attempts: 5,
            backoff: {
                type: 'exponential',
                delay: 2000,
            },
            removeOnComplete: 1000,
            removeOnFail: 200,
        };
        return this.billingQueue.add('process-billing', jobData, {
            ...defaultOptions,
            ...options,
        });
    }
    async getQueueStats() {
        const queues = [
            { name: 'agent-execution', queue: this.agentQueue },
            { name: 'tool-execution', queue: this.toolQueue },
            { name: 'workflow-execution', queue: this.workflowQueue },
            { name: 'notification', queue: this.notificationQueue },
            { name: 'analytics', queue: this.analyticsQueue },
            { name: 'billing', queue: this.billingQueue },
        ];
        const stats = await Promise.all(queues.map(async ({ name, queue }) => {
            const [waiting, active, completed, failed, delayed] = await Promise.all([
                queue.getWaiting(),
                queue.getActive(),
                queue.getCompleted(),
                queue.getFailed(),
                queue.getDelayed(),
            ]);
            return {
                name,
                waiting: waiting.length,
                active: active.length,
                completed: completed.length,
                failed: failed.length,
                delayed: delayed.length,
            };
        }));
        return stats;
    }
    async pauseQueue(queueName) {
        const queue = this.getQueueByName(queueName);
        if (queue) {
            await queue.pause();
            this.logger.log(`Queue ${queueName} paused`);
        }
    }
    async resumeQueue(queueName) {
        const queue = this.getQueueByName(queueName);
        if (queue) {
            await queue.resume();
            this.logger.log(`Queue ${queueName} resumed`);
        }
    }
    async cleanQueue(queueName, grace = 24 * 60 * 60 * 1000) {
        const queue = this.getQueueByName(queueName);
        if (queue) {
            await queue.clean(grace, 'completed');
            await queue.clean(grace, 'failed');
            this.logger.log(`Queue ${queueName} cleaned`);
        }
    }
    getQueueByName(name) {
        const queueMap = {
            'agent-execution': this.agentQueue,
            'tool-execution': this.toolQueue,
            'workflow-execution': this.workflowQueue,
            'notification': this.notificationQueue,
            'analytics': this.analyticsQueue,
            'billing': this.billingQueue,
        };
        return queueMap[name] || null;
    }
    async setupQueueProcessors() {
        this.logger.log('Queue processors setup completed');
    }
    async setupQueueEvents() {
        const queues = [
            { name: 'agent-execution', queue: this.agentQueue },
            { name: 'tool-execution', queue: this.toolQueue },
            { name: 'workflow-execution', queue: this.workflowQueue },
            { name: 'notification', queue: this.notificationQueue },
            { name: 'analytics', queue: this.analyticsQueue },
            { name: 'billing', queue: this.billingQueue },
        ];
        queues.forEach(({ name, queue }) => {
            queue.on('completed', (job, result) => {
                this.logger.log(`Job completed in ${name}: ${job.id}`);
            });
            queue.on('failed', (job, error) => {
                this.logger.error(`Job failed in ${name}: ${job.id}`, error);
            });
            queue.on('stalled', (job) => {
                this.logger.warn(`Job stalled in ${name}: ${job.id}`);
            });
            queue.on('progress', (job, progress) => {
                this.logger.debug(`Job progress in ${name}: ${job.id} - ${progress}%`);
            });
        });
    }
};
exports.QueueService = QueueService;
exports.QueueService = QueueService = QueueService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(2, (0, bull_1.InjectQueue)('agent-execution')),
    __param(3, (0, bull_1.InjectQueue)('tool-execution')),
    __param(4, (0, bull_1.InjectQueue)('workflow-execution')),
    __param(5, (0, bull_1.InjectQueue)('notification')),
    __param(6, (0, bull_1.InjectQueue)('analytics')),
    __param(7, (0, bull_1.InjectQueue)('billing')),
    __metadata("design:paramtypes", [config_1.ConfigService,
        redis_service_1.RedisService, Object, Object, Object, Object, Object, Object])
], QueueService);
//# sourceMappingURL=queue.service.js.map