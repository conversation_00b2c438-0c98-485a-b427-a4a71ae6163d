(()=>{var e={};e.id=48,e.ids=[48],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},22641:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},40522:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c}),t(95727),t(69319),t(77406),t(12874);var r=t(27105),a=t(15265),n=t(90157),o=t.n(n),i=t(44665),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(s,l);let c=["",{children:["auth",{children:["reset-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,95727)),"C:\\laragon\\www\\max\\trae\\kilo-teset\\augsyanapseAI\\frontend\\app\\auth\\reset-password\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,69319)),"C:\\laragon\\www\\max\\trae\\kilo-teset\\augsyanapseAI\\frontend\\app\\auth\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,77406)),"C:\\laragon\\www\\max\\trae\\kilo-teset\\augsyanapseAI\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,12874,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\laragon\\www\\max\\trae\\kilo-teset\\augsyanapseAI\\frontend\\app\\auth\\reset-password\\page.tsx"],u="/auth/reset-password/page",p={require:t,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/auth/reset-password/page",pathname:"/auth/reset-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},58322:(e,s,t)=>{Promise.resolve().then(t.bind(t,49327))},49327:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>w});var r=t(19899),a=t(5507),n=t(76153),o=t(54175),i=t(15950),l=t(95650),c=t(37513),d=t(20382),u=t(80046),p=t(21842),h=t(27843),x=t(98015),m=t(22455);let g=c.Ry({password:c.Z_().min(8,"Password must be at least 8 characters").regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,"Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"),confirmPassword:c.Z_()}).refine(e=>e.password===e.confirmPassword,{message:"Passwords do not match",path:["confirmPassword"]});function w(){let e;(0,n.useRouter)(),(0,n.useSearchParams)();let[s,t]=(0,a.useState)(!1),[c,w]=(0,a.useState)(!1),[y,f]=(0,a.useState)(null),[j,v]=(0,a.useState)(!1),[b,N]=(0,a.useState)(!1),[P,k]=(0,a.useState)(null),{register:q,handleSubmit:_,formState:{errors:C},watch:I}=(0,i.cI)({resolver:(0,l.F)(g),defaultValues:{password:"",confirmPassword:""}}),A=I("password"),S=(e=0,A.length>=8&&e++,/[a-z]/.test(A)&&e++,/[A-Z]/.test(A)&&e++,/\d/.test(A)&&e++,/[@$!%*?&]/.test(A)&&e++,e),z=async e=>{if(!P){f("Invalid reset token");return}t(!0),f(null);try{await p.x.post("/api/v1/auth/reset-password",{token:P,password:e.password}),w(!0)}catch(e){f(e.message||"An error occurred. Please try again.")}finally{t(!1)}};return c?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[r.jsx(h.Z,{className:"mx-auto h-12 w-12 text-success-500"}),r.jsx("h2",{className:"mt-4 text-3xl font-bold tracking-tight text-gray-900",children:"Password reset successful"}),r.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"Your password has been successfully reset. You can now sign in with your new password."})]}),r.jsx(o.default,{href:"/auth/signin",children:r.jsx(d.z,{className:"w-full",children:"Continue to sign in"})})]}):!P||y?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("h2",{className:"text-3xl font-bold tracking-tight text-gray-900",children:"Invalid reset link"}),r.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"This password reset link is invalid or has expired."})]}),r.jsx("div",{className:"rounded-md bg-error-50 p-4",children:r.jsx("div",{className:"text-sm text-error-700",children:y||"The reset link may have expired or been used already."})}),(0,r.jsxs)("div",{className:"flex flex-col space-y-3",children:[r.jsx(o.default,{href:"/auth/forgot-password",children:r.jsx(d.z,{className:"w-full",children:"Request new reset link"})}),r.jsx(o.default,{href:"/auth/signin",children:r.jsx(d.z,{variant:"ghost",className:"w-full",children:"Back to sign in"})})]})]}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[r.jsx("h2",{className:"text-3xl font-bold tracking-tight text-gray-900",children:"Reset your password"}),r.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"Enter your new password below."})]}),y&&r.jsx("div",{className:"rounded-md bg-error-50 p-4",children:r.jsx("div",{className:"text-sm text-error-700",children:y})}),(0,r.jsxs)("form",{className:"space-y-6",onSubmit:_(z),children:[(0,r.jsxs)("div",{children:[r.jsx(u.I,{label:"New password",type:j?"text":"password",autoComplete:"new-password",placeholder:"Enter your new password",error:C.password?.message,rightIcon:r.jsx("button",{type:"button",className:"text-gray-400 hover:text-gray-600",onClick:()=>v(!j),children:j?r.jsx(x.Z,{className:"h-5 w-5"}):r.jsx(m.Z,{className:"h-5 w-5"})}),...q("password")}),A&&r.jsx("div",{className:"mt-2",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("div",{className:"flex-1 bg-gray-200 rounded-full h-2",children:r.jsx("div",{className:`h-2 rounded-full transition-all duration-300 ${["bg-error-500","bg-warning-500","bg-yellow-500","bg-blue-500","bg-success-500"][S-1]||"bg-gray-200"}`,style:{width:`${S/5*100}%`}})}),r.jsx("span",{className:"text-xs text-gray-600",children:["Very Weak","Weak","Fair","Good","Strong"][S-1]||"Very Weak"})]})})]}),r.jsx(u.I,{label:"Confirm new password",type:b?"text":"password",autoComplete:"new-password",placeholder:"Confirm your new password",error:C.confirmPassword?.message,rightIcon:r.jsx("button",{type:"button",className:"text-gray-400 hover:text-gray-600",onClick:()=>N(!b),children:b?r.jsx(x.Z,{className:"h-5 w-5"}):r.jsx(m.Z,{className:"h-5 w-5"})}),...q("confirmPassword")}),r.jsx(d.z,{type:"submit",className:"w-full",loading:s,disabled:s,children:"Reset password"})]}),r.jsx("div",{className:"text-center",children:r.jsx(o.default,{href:"/auth/signin",className:"text-sm font-medium text-primary-600 hover:text-primary-500",children:"Back to sign in"})})]})}},21842:(e,s,t)=>{"use strict";t.d(s,{x:()=>o});var r=t(46294),a=t(60878);class n{constructor(){this.baseURL="http://localhost:3000",this.client=r.Z.create({baseURL:this.baseURL,timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors()}setupInterceptors(){this.client.interceptors.request.use(async e=>{let s=await (0,a.getSession)();return s?.accessToken&&(e.headers.Authorization=`Bearer ${s.accessToken}`),s?.user?.organizationId&&(e.headers["X-Organization-ID"]=s.user.organizationId),e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,async e=>{let s=e.config;if(e.response?.status===401&&!s._retry){s._retry=!0;try{let e=await (0,a.getSession)();if(e?.refreshToken){let t=await this.post("/api/v1/auth/refresh",{refreshToken:e.refreshToken});if(t.data.accessToken)return s.headers.Authorization=`Bearer ${t.data.accessToken}`,this.client(s)}}catch(e){return Promise.reject(e)}}return Promise.reject({message:e.response?.data?.message||e.message||"An unexpected error occurred",statusCode:e.response?.status||500,timestamp:new Date().toISOString(),path:e.config?.url})})}async get(e,s){return this.client.get(e,s)}async post(e,s,t){return this.client.post(e,s,t)}async put(e,s,t){return this.client.put(e,s,t)}async patch(e,s,t){return this.client.patch(e,s,t)}async delete(e,s){return this.client.delete(e,s)}async upload(e,s,t){let r=new FormData;return r.append("file",s),this.client.post(e,r,{headers:{"Content-Type":"multipart/form-data"},onUploadProgress:e=>{t&&e.total&&t(Math.round(100*e.loaded/e.total))}})}async healthCheck(){return this.get("/api/v1/health")}getBaseURL(){return this.baseURL}}let o=new n},95727:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(2772).createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\app\auth\reset-password\page.tsx#default`)}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[942,544,695,137,188,975],()=>t(40522));module.exports=r})();