import { ApiKeyService, ApiKeyResponse } from './api-key.service';
declare class CreateApiKeyRequestDto {
    name: string;
    description?: string;
    permissions: string[];
    expiresAt?: string;
}
declare class ApiKeyStatsDto {
    total: number;
    active: number;
    inactive: number;
}
export declare class ApiKeyController {
    private apiKeyService;
    constructor(apiKeyService: ApiKeyService);
    createApiKey(createDto: CreateApiKeyRequestDto, user: any): Promise<ApiKeyResponse>;
    listApiKeys(user: any): Promise<Omit<ApiKeyResponse, 'key'>[]>;
    revokeApiKey(apiKeyId: string, user: any): Promise<{
        message: string;
    }>;
    getApiKeyStats(user: any): Promise<ApiKeyStatsDto>;
}
export {};
