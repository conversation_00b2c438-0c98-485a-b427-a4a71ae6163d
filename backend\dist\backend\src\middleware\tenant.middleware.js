"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TenantMiddleware = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const prisma_service_1 = require("../modules/prisma/prisma.service");
const logger_service_1 = require("../services/logger/logger.service");
let TenantMiddleware = class TenantMiddleware {
    constructor(jwtService, prismaService, logger) {
        this.jwtService = jwtService;
        this.prismaService = prismaService;
        this.logger = logger;
    }
    async use(req, res, next) {
        try {
            if (this.isPublicEndpoint(req.path)) {
                return next();
            }
            const organizationId = this.extractOrganizationId(req);
            if (!organizationId) {
                throw new common_1.UnauthorizedException('Organization ID is required');
            }
            const organization = await this.prismaService.organization.findFirst({
                where: {
                    id: organizationId,
                    isActive: true,
                },
            });
            if (!organization) {
                throw new common_1.ForbiddenException('Invalid or inactive organization');
            }
            const token = this.extractToken(req);
            let user = null;
            if (token) {
                try {
                    const payload = this.jwtService.verify(token);
                    user = await this.prismaService.user.findFirst({
                        where: {
                            id: payload.userId,
                            organizationId,
                            isActive: true,
                        },
                        include: {
                            organization: true,
                        },
                    });
                    if (!user) {
                        throw new common_1.ForbiddenException('User does not belong to this organization');
                    }
                    if (payload.organizationId !== organizationId) {
                        throw new common_1.ForbiddenException('Token organization mismatch');
                    }
                }
                catch (error) {
                    if (error instanceof common_1.ForbiddenException) {
                        throw error;
                    }
                    throw new common_1.UnauthorizedException('Invalid or expired token');
                }
            }
            req.organizationId = organizationId;
            req.userId = user?.id;
            req.userRole = user?.role;
            req.tenant = {
                organizationId,
                userId: user?.id,
                userRole: user?.role,
                permissions: await this.getUserPermissions(user?.id, organizationId),
            };
            this.logger.audit('TENANT_ACCESS', 'success', {
                organizationId,
                userId: user?.id,
                resource: 'tenant_access',
                details: {
                    userRole: user?.role,
                    endpoint: req.path,
                    method: req.method,
                    userAgent: req.get('User-Agent'),
                    ipAddress: req.ip,
                },
            });
            next();
        }
        catch (error) {
            this.logger.security('TENANT_ACCESS_DENIED', 'high', {
                organizationId: req.organizationId,
                details: {
                    endpoint: req.path,
                    method: req.method,
                    error: error.message,
                    userAgent: req.get('User-Agent'),
                    ipAddress: req.ip,
                },
            });
            throw error;
        }
    }
    extractOrganizationId(req) {
        const headerOrgId = req.get('X-Organization-Id');
        if (headerOrgId)
            return headerOrgId;
        const queryOrgId = req.query.organizationId;
        if (queryOrgId)
            return queryOrgId;
        const bodyOrgId = req.body?.organizationId;
        if (bodyOrgId)
            return bodyOrgId;
        const paramOrgId = req.params?.organizationId;
        if (paramOrgId)
            return paramOrgId;
        const host = req.get('Host');
        if (host && host.includes('.')) {
            const subdomain = host.split('.')[0];
            if (subdomain && subdomain !== 'www' && subdomain !== 'api') {
                return subdomain;
            }
        }
        return null;
    }
    extractToken(req) {
        const authHeader = req.get('Authorization');
        if (authHeader && authHeader.startsWith('Bearer ')) {
            return authHeader.substring(7);
        }
        const cookieToken = req.cookies?.['auth-token'];
        if (cookieToken) {
            return cookieToken;
        }
        return null;
    }
    async getUserPermissions(userId, organizationId) {
        if (!userId || !organizationId) {
            return [];
        }
        try {
            const user = await this.prismaService.user.findFirst({
                where: { id: userId, organizationId },
                include: {
                    organization: {
                        include: {
                            roles: true,
                        },
                    },
                },
            });
            if (!user) {
                return [];
            }
            const rolePermissions = user.organization.roles
                .filter(role => role.name === user.role)
                .flatMap(role => role.permissions);
            const defaultPermissions = this.getDefaultPermissions(user.role);
            return [...new Set([...rolePermissions, ...defaultPermissions])];
        }
        catch (error) {
            this.logger.error('Failed to get user permissions:', error);
            return [];
        }
    }
    getDefaultPermissions(role) {
        const permissionMap = {
            SUPER_ADMIN: ['*'],
            ORG_ADMIN: [
                'organization:read',
                'organization:write',
                'users:read',
                'users:write',
                'agents:read',
                'agents:write',
                'tools:read',
                'tools:write',
                'workflows:read',
                'workflows:write',
                'analytics:read',
                'billing:read',
            ],
            DEVELOPER: [
                'agents:read',
                'agents:write',
                'tools:read',
                'tools:write',
                'workflows:read',
                'workflows:write',
                'sandboxes:read',
                'sandboxes:write',
            ],
            VIEWER: [
                'agents:read',
                'tools:read',
                'workflows:read',
                'analytics:read',
            ],
        };
        return permissionMap[role] || [];
    }
    isPublicEndpoint(path) {
        const publicPaths = [
            '/health',
            '/health/ready',
            '/health/live',
            '/api/v1/auth/login',
            '/api/v1/auth/register',
            '/api/v1/auth/refresh',
            '/api/v1/auth/forgot-password',
            '/api/v1/auth/reset-password',
            '/api/docs',
            '/metrics',
            '/widgets/',
        ];
        return publicPaths.some(publicPath => path.startsWith(publicPath));
    }
};
exports.TenantMiddleware = TenantMiddleware;
exports.TenantMiddleware = TenantMiddleware = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [jwt_1.JwtService,
        prisma_service_1.PrismaService,
        logger_service_1.LoggerService])
], TenantMiddleware);
//# sourceMappingURL=tenant.middleware.js.map