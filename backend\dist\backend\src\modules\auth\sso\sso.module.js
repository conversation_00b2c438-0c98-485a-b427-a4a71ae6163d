"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SsoModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const sso_controller_1 = require("./sso.controller");
const sso_service_1 = require("./sso.service");
const saml_strategy_1 = require("./strategies/saml.strategy");
const oidc_strategy_1 = require("./strategies/oidc.strategy");
const active_directory_strategy_1 = require("./strategies/active-directory.strategy");
const prisma_module_1 = require("../../prisma/prisma.module");
const logger_module_1 = require("../../../services/logger/logger.module");
let SsoModule = class SsoModule {
};
exports.SsoModule = SsoModule;
exports.SsoModule = SsoModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule,
            prisma_module_1.PrismaModule,
            logger_module_1.LoggerModule,
        ],
        controllers: [sso_controller_1.SsoController],
        providers: [
            sso_service_1.SsoService,
            saml_strategy_1.SamlStrategy,
            oidc_strategy_1.OidcStrategy,
            active_directory_strategy_1.ActiveDirectoryStrategy,
        ],
        exports: [sso_service_1.SsoService],
    })
], SsoModule);
//# sourceMappingURL=sso.module.js.map