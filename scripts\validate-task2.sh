#!/bin/bash

# SynapseAI Task 2 Validation Script
# Validates complete database schema and multi-tenancy implementation

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
BACKEND_DIR="$PROJECT_ROOT/backend"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Validation functions
validate_schema_file() {
    log "Validating Prisma schema file..."

    local schema_file="$BACKEND_DIR/prisma/schema.prisma"

    if [[ ! -f "$schema_file" ]]; then
        error "Prisma schema file not found: $schema_file"
        return 1
    fi

    # Check for all required models
    local required_models=(
        "Organization"
        "User"
        "Role"
        "Session"
        "Template"
        "TemplateVersion"
        "Agent"
        "AgentExecution"
        "Tool"
        "ToolExecution"
        "Hybrid"
        "HybridExecution"
        "Workflow"
        "WorkflowExecution"
        "Provider"
        "ProviderUsage"
        "HITLRequest"
        "HITLDecision"
        "Document"
        "DocumentChunk"
        "KnowledgeSearch"
        "Widget"
        "WidgetExecution"
        "Analytics"
        "Metrics"
        "Billing"
        "UsageMeter"
        "Quota"
        "Notification"
        "NotificationPreference"
        "Sandbox"
        "TestResult"
    )

    for model in "${required_models[@]}"; do
        if grep -q "model $model" "$schema_file"; then
            success "✓ Model $model found"
        else
            error "✗ Model $model missing"
            return 1
        fi
    done

    # Check for organizationId fields in tenant-scoped models
    local tenant_models=(
        "User"
        "Role"
        "Session"
        "Template"
        "Agent"
        "AgentExecution"
        "Tool"
        "ToolExecution"
        "Hybrid"
        "HybridExecution"
        "Workflow"
        "WorkflowExecution"
        "Provider"
        "ProviderUsage"
        "HITLRequest"
        "Document"
        "KnowledgeSearch"
        "Widget"
        "WidgetExecution"
        "Analytics"
        "Metrics"
        "Billing"
        "UsageMeter"
        "Quota"
        "Notification"
        "NotificationPreference"
        "Sandbox"
        "TestResult"
    )

    for model in "${tenant_models[@]}"; do
        if grep -A 20 "model $model" "$schema_file" | grep -q "organizationId"; then
            success "✓ Model $model has organizationId field"
        else
            error "✗ Model $model missing organizationId field"
            return 1
        fi
    done

    success "Schema file validation passed"
    return 0
}

validate_relationships() {
    log "Validating database relationships..."

    local schema_file="$BACKEND_DIR/prisma/schema.prisma"

    # Check for foreign key relationships
    if grep -q "@relation" "$schema_file"; then
        success "✓ Relationships defined in schema"
    else
        error "✗ No relationships found in schema"
        return 1
    fi

    # Check for cascade delete on organization
    if grep -q "onDelete: Cascade" "$schema_file"; then
        success "✓ Cascade delete relationships found"
    else
        warning "⚠ No cascade delete relationships found"
    fi

    success "Relationship validation passed"
    return 0
}

validate_indexing() {
    log "Validating database indexing..."

    local schema_file="$BACKEND_DIR/prisma/schema.prisma"

    # Check for indexes
    if grep -q "@@index" "$schema_file"; then
        success "✓ Database indexes defined"
    else
        error "✗ No database indexes found"
        return 1
    fi

    # Check for organizationId indexes
    if grep -q "@@index(\[organizationId\])" "$schema_file"; then
        success "✓ OrganizationId indexes found"
    else
        error "✗ OrganizationId indexes missing"
        return 1
    fi

    success "Indexing validation passed"
    return 0
}

validate_prisma_client() {
    log "Validating Prisma client generation..."

    cd "$BACKEND_DIR"

    if npx prisma generate > /dev/null 2>&1; then
        success "✓ Prisma client generated successfully"
    else
        error "✗ Prisma client generation failed"
        return 1
    fi

    # Check if client files exist
    if [[ -d "node_modules/.prisma/client" ]]; then
        success "✓ Prisma client files exist"
    else
        error "✗ Prisma client files not found"
        return 1
    fi

    success "Prisma client validation passed"
    return 0
}

validate_tenant_middleware() {
    log "Validating tenant middleware..."

    local middleware_file="$BACKEND_DIR/src/middleware/tenant.middleware.ts"

    if [[ ! -f "$middleware_file" ]]; then
        error "Tenant middleware file not found: $middleware_file"
        return 1
    fi

    # Check for key middleware functions
    local required_functions=(
        "extractOrganizationId"
        "extractToken"
        "getUserPermissions"
        "isPublicEndpoint"
    )

    for func in "${required_functions[@]}"; do
        if grep -q "$func" "$middleware_file"; then
            success "✓ Function $func found in middleware"
        else
            error "✗ Function $func missing in middleware"
            return 1
        fi
    done

    success "Tenant middleware validation passed"
    return 0
}

validate_tenant_service() {
    log "Validating tenant service..."

    local service_file="$BACKEND_DIR/src/services/tenant/tenant.service.ts"

    if [[ ! -f "$service_file" ]]; then
        error "Tenant service file not found: $service_file"
        return 1
    fi

    # Check for key service methods
    local required_methods=(
        "createTenantClient"
        "validateResourceAccess"
        "enforceOrganizationBoundary"
    )

    for method in "${required_methods[@]}"; do
        if grep -q "$method" "$service_file"; then
            success "✓ Method $method found in service"
        else
            error "✗ Method $method missing in service"
            return 1
        fi
    done

    success "Tenant service validation passed"
    return 0
}

validate_redis_namespacing() {
    log "Validating Redis namespacing..."

    local redis_file="$BACKEND_DIR/src/modules/redis/redis.service.ts"

    if [[ ! -f "$redis_file" ]]; then
        error "Redis service file not found: $redis_file"
        return 1
    fi

    # Check for namespacing functions
    local required_functions=(
        "getNamespacedKey"
        "getOrganizationSessions"
        "deleteOrganizationData"
    )

    for func in "${required_functions[@]}"; do
        if grep -q "$func" "$redis_file"; then
            success "✓ Function $func found in Redis service"
        else
            error "✗ Function $func missing in Redis service"
            return 1
        fi
    done

    success "Redis namespacing validation passed"
    return 0
}

validate_migration_scripts() {
    log "Validating migration scripts..."

    local migration_script="$PROJECT_ROOT/scripts/migrate-database.sh"
    local backup_script="$PROJECT_ROOT/scripts/backup/database-backup.sh"

    if [[ ! -f "$migration_script" ]]; then
        error "Migration script not found: $migration_script"
        return 1
    fi

    if [[ ! -f "$backup_script" ]]; then
        error "Database backup script not found: $backup_script"
        return 1
    fi

    # Check if scripts are executable
    if [[ -x "$migration_script" ]]; then
        success "✓ Migration script is executable"
    else
        error "✗ Migration script is not executable"
        return 1
    fi

    if [[ -x "$backup_script" ]]; then
        success "✓ Backup script is executable"
    else
        error "✗ Backup script is not executable"
        return 1
    fi

    success "Migration scripts validation passed"
    return 0
}

validate_seed_script() {
    log "Validating seed script..."

    local seed_file="$BACKEND_DIR/prisma/seed.ts"

    if [[ ! -f "$seed_file" ]]; then
        error "Seed script not found: $seed_file"
        return 1
    fi

    # Check for key seeding operations
    local required_operations=(
        "organization.upsert"
        "user.upsert"
        "role.upsert"
        "provider.upsert"
        "template.create"
    )

    for operation in "${required_operations[@]}"; do
        if grep -q "$operation" "$seed_file"; then
            success "✓ Operation $operation found in seed script"
        else
            error "✗ Operation $operation missing in seed script"
            return 1
        fi
    done

    success "Seed script validation passed"
    return 0
}

validate_tests() {
    log "Validating test files..."

    local test_file="$BACKEND_DIR/src/test/database-schema.test.ts"

    if [[ ! -f "$test_file" ]]; then
        error "Database schema test file not found: $test_file"
        return 1
    fi

    # Check for key test suites
    local required_tests=(
        "Prisma Schema Validation"
        "Multi-Tenant Architecture"
        "Redis Namespacing"
        "Database Relationships"
        "Database Indexing"
    )

    for test in "${required_tests[@]}"; do
        if grep -q "$test" "$test_file"; then
            success "✓ Test suite '$test' found"
        else
            error "✗ Test suite '$test' missing"
            return 1
        fi
    done

    success "Test validation passed"
    return 0
}

# Main validation function
main() {
    log "Starting Task 2 validation..."
    log "Validating complete database schema and multi-tenancy implementation"

    local validation_passed=true

    # Run all validations
    validate_schema_file || validation_passed=false
    validate_relationships || validation_passed=false
    validate_indexing || validation_passed=false
    validate_prisma_client || validation_passed=false
    validate_tenant_middleware || validation_passed=false
    validate_tenant_service || validation_passed=false
    validate_redis_namespacing || validation_passed=false
    validate_migration_scripts || validation_passed=false
    validate_seed_script || validation_passed=false
    validate_tests || validation_passed=false

    echo ""
    if [[ "$validation_passed" == true ]]; then
        success "🎉 All Task 2 validations passed!"
        success "✅ Complete database schema implemented"
        success "✅ Multi-tenant architecture configured"
        success "✅ Prisma ORM with type-safe access"
        success "✅ Redis namespacing for tenant isolation"
        success "✅ Migration and seeding scripts ready"
        success "✅ Backup and recovery procedures implemented"
        echo ""
        success "Task 2 is ready for production deployment!"
    else
        error "❌ Some validations failed. Please review the errors above."
        exit 1
    fi
}

# Run main function
main "$@"