"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealthController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const terminus_1 = require("@nestjs/terminus");
const prisma_health_1 = require("../prisma/prisma.health");
const redis_health_1 = require("../redis/redis.health");
let HealthController = class HealthController {
    constructor(health, prismaHealth, redisHealth) {
        this.health = health;
        this.prismaHealth = prismaHealth;
        this.redisHealth = redisHealth;
    }
    check() {
        return this.health.check([
            () => this.prismaHealth.isHealthy('database'),
            () => this.redisHealth.isHealthy('redis'),
        ]);
    }
    readiness() {
        return this.health.check([
            () => this.prismaHealth.isHealthy('database'),
            () => this.redisHealth.isHealthy('redis'),
        ]);
    }
    liveness() {
        return {
            status: 'ok',
            timestamp: new Date().toISOString(),
        };
    }
};
exports.HealthController = HealthController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get application health status' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Health check successful' }),
    (0, swagger_1.ApiResponse)({ status: 503, description: 'Service unavailable' }),
    (0, terminus_1.HealthCheck)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], HealthController.prototype, "check", null);
__decorate([
    (0, common_1.Get)('ready'),
    (0, swagger_1.ApiOperation)({ summary: 'Get application readiness status' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Application is ready' }),
    (0, swagger_1.ApiResponse)({ status: 503, description: 'Application is not ready' }),
    (0, terminus_1.HealthCheck)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], HealthController.prototype, "readiness", null);
__decorate([
    (0, common_1.Get)('live'),
    (0, swagger_1.ApiOperation)({ summary: 'Get application liveness status' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Application is alive' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Object)
], HealthController.prototype, "liveness", null);
exports.HealthController = HealthController = __decorate([
    (0, swagger_1.ApiTags)('Health'),
    (0, common_1.Controller)('health'),
    __metadata("design:paramtypes", [terminus_1.HealthCheckService,
        prisma_health_1.PrismaHealthIndicator,
        redis_health_1.RedisHealthIndicator])
], HealthController);
//# sourceMappingURL=health.controller.js.map