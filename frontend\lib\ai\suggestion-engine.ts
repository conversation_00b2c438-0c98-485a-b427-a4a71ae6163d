import { apiClient } from '@/lib/api';

export interface ConfigSuggestion {
  id: string;
  type: 'optimization' | 'completion' | 'validation' | 'best_practice' | 'security';
  priority: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  reasoning: string;
  confidence: number;
  impact: {
    performance?: number;
    security?: number;
    usability?: number;
    maintainability?: number;
  };
  action: {
    type: 'set_value' | 'add_field' | 'remove_field' | 'restructure' | 'template';
    target: string;
    value?: any;
    template?: Record<string, any>;
    validation?: (value: any) => boolean;
  };
  examples?: Array<{
    before: any;
    after: any;
    explanation: string;
  }>;
  relatedFields?: string[];
  tags: string[];
}

export interface SuggestionContext {
  configType: 'agent' | 'tool' | 'workflow' | 'template';
  currentConfig: Record<string, any>;
  userLevel: 'beginner' | 'intermediate' | 'advanced';
  organizationSettings?: Record<string, any>;
  industryContext?: string;
  usagePatterns?: Record<string, any>;
  performanceMetrics?: Record<string, any>;
  errorHistory?: Array<{
    field: string;
    error: string;
    timestamp: number;
  }>;
}

export interface SuggestionFilter {
  types?: string[];
  priorities?: string[];
  tags?: string[];
  minConfidence?: number;
  maxSuggestions?: number;
  excludeApplied?: boolean;
}

class SuggestionEngine {
  private appliedSuggestions: Set<string> = new Set();
  private dismissedSuggestions: Set<string> = new Set();
  private userPreferences: Record<string, any> = {};

  /**
   * Get AI-powered configuration suggestions
   */
  async getSuggestions(
    context: SuggestionContext,
    filter: SuggestionFilter = {}
  ): Promise<ConfigSuggestion[]> {
    try {
      // Try AI-powered suggestions first
      const aiSuggestions = await this.getAISuggestions(context, filter);
      if (aiSuggestions.length > 0) {
        return this.filterAndRankSuggestions(aiSuggestions, filter);
      }
    } catch (error) {
      console.warn('AI suggestions failed, using rule-based fallback:', error);
    }

    // Fallback to rule-based suggestions
    const ruleSuggestions = this.getRuleBasedSuggestions(context);
    return this.filterAndRankSuggestions(ruleSuggestions, filter);
  }

  /**
   * Get suggestions for a specific field
   */
  async getFieldSuggestions(
    field: string,
    currentValue: any,
    context: SuggestionContext
  ): Promise<ConfigSuggestion[]> {
    try {
      const response = await apiClient.post('/api/v1/ai/field-suggestions', {
        field,
        currentValue,
        context,
      });
      return response.data.suggestions;
    } catch (error) {
      return this.getRuleBasedFieldSuggestions(field, currentValue, context);
    }
  }

  /**
   * Validate configuration and get improvement suggestions
   */
  async validateAndSuggest(
    config: Record<string, any>,
    context: SuggestionContext
  ): Promise<{
    isValid: boolean;
    errors: Array<{
      field: string;
      message: string;
      severity: 'error' | 'warning' | 'info';
    }>;
    suggestions: ConfigSuggestion[];
  }> {
    try {
      const response = await apiClient.post('/api/v1/ai/validate-and-suggest', {
        config,
        context,
      });
      return response.data;
    } catch (error) {
      return this.getRuleBasedValidation(config, context);
    }
  }

  /**
   * Get optimization suggestions based on usage patterns
   */
  async getOptimizationSuggestions(
    context: SuggestionContext
  ): Promise<ConfigSuggestion[]> {
    const suggestions: ConfigSuggestion[] = [];

    // Performance optimizations
    if (context.performanceMetrics) {
      suggestions.push(...this.getPerformanceSuggestions(context));
    }

    // Security improvements
    suggestions.push(...this.getSecuritySuggestions(context));

    // Usability enhancements
    suggestions.push(...this.getUsabilitySuggestions(context));

    return suggestions;
  }

  /**
   * Get contextual suggestions based on similar configurations
   */
  async getSimilarConfigSuggestions(
    context: SuggestionContext
  ): Promise<ConfigSuggestion[]> {
    try {
      const response = await apiClient.post('/api/v1/ai/similar-configs', {
        context,
      });
      return response.data.suggestions;
    } catch (error) {
      return [];
    }
  }

  /**
   * Apply a suggestion to configuration
   */
  applySuggestion(
    suggestion: ConfigSuggestion,
    currentConfig: Record<string, any>
  ): Record<string, any> {
    const newConfig = { ...currentConfig };
    
    switch (suggestion.action.type) {
      case 'set_value':
        this.setNestedValue(newConfig, suggestion.action.target, suggestion.action.value);
        break;
      
      case 'add_field':
        this.setNestedValue(newConfig, suggestion.action.target, suggestion.action.value);
        break;
      
      case 'remove_field':
        this.removeNestedValue(newConfig, suggestion.action.target);
        break;
      
      case 'template':
        if (suggestion.action.template) {
          Object.assign(newConfig, suggestion.action.template);
        }
        break;
      
      case 'restructure':
        // Handle complex restructuring
        if (suggestion.action.template) {
          return this.restructureConfig(newConfig, suggestion.action.template);
        }
        break;
    }

    this.appliedSuggestions.add(suggestion.id);
    return newConfig;
  }

  /**
   * Dismiss a suggestion
   */
  dismissSuggestion(suggestionId: string): void {
    this.dismissedSuggestions.add(suggestionId);
  }

  /**
   * Get suggestion statistics
   */
  getStats(): {
    totalApplied: number;
    totalDismissed: number;
    successRate: number;
    topCategories: Array<{ category: string; count: number }>;
  } {
    return {
      totalApplied: this.appliedSuggestions.size,
      totalDismissed: this.dismissedSuggestions.size,
      successRate: this.appliedSuggestions.size / (this.appliedSuggestions.size + this.dismissedSuggestions.size) || 0,
      topCategories: [], // Would be populated from analytics
    };
  }

  /**
   * AI-powered suggestions from backend
   */
  private async getAISuggestions(
    context: SuggestionContext,
    filter: SuggestionFilter
  ): Promise<ConfigSuggestion[]> {
    const response = await apiClient.post('/api/v1/ai/config-suggestions', {
      context,
      filter,
      appliedSuggestions: Array.from(this.appliedSuggestions),
      dismissedSuggestions: Array.from(this.dismissedSuggestions),
    });
    return response.data.suggestions;
  }

  /**
   * Rule-based suggestions fallback
   */
  private getRuleBasedSuggestions(context: SuggestionContext): ConfigSuggestion[] {
    const suggestions: ConfigSuggestion[] = [];

    switch (context.configType) {
      case 'agent':
        suggestions.push(...this.getAgentSuggestions(context));
        break;
      case 'tool':
        suggestions.push(...this.getToolSuggestions(context));
        break;
      case 'workflow':
        suggestions.push(...this.getWorkflowSuggestions(context));
        break;
    }

    return suggestions;
  }

  /**
   * Agent-specific suggestions
   */
  private getAgentSuggestions(context: SuggestionContext): ConfigSuggestion[] {
    const suggestions: ConfigSuggestion[] = [];
    const config = context.currentConfig;

    // Model selection suggestions
    if (!config.model || config.model === 'gpt-3.5-turbo') {
      suggestions.push({
        id: 'agent-model-upgrade',
        type: 'optimization',
        priority: 'medium',
        title: 'Consider upgrading to GPT-4',
        description: 'GPT-4 provides better reasoning and more accurate responses',
        reasoning: 'GPT-4 has shown significant improvements in complex reasoning tasks',
        confidence: 0.8,
        impact: { performance: 0.3, usability: 0.2 },
        action: {
          type: 'set_value',
          target: 'model',
          value: 'gpt-4',
        },
        tags: ['performance', 'model'],
      });
    }

    // Temperature optimization
    if (config.temperature === undefined || config.temperature > 0.9) {
      suggestions.push({
        id: 'agent-temperature-optimize',
        type: 'optimization',
        priority: 'low',
        title: 'Optimize temperature setting',
        description: 'Lower temperature for more consistent responses',
        reasoning: 'High temperature can lead to unpredictable outputs',
        confidence: 0.7,
        impact: { performance: 0.1, usability: 0.2 },
        action: {
          type: 'set_value',
          target: 'temperature',
          value: 0.7,
        },
        tags: ['consistency', 'parameters'],
      });
    }

    // System prompt suggestions
    if (!config.systemPrompt || config.systemPrompt.length < 50) {
      suggestions.push({
        id: 'agent-system-prompt',
        type: 'completion',
        priority: 'high',
        title: 'Add detailed system prompt',
        description: 'A well-crafted system prompt improves agent behavior',
        reasoning: 'System prompts provide context and guidelines for the AI',
        confidence: 0.9,
        impact: { performance: 0.4, usability: 0.3 },
        action: {
          type: 'set_value',
          target: 'systemPrompt',
          value: 'You are a helpful AI assistant. Be concise, accurate, and professional in your responses.',
        },
        tags: ['prompt', 'behavior'],
      });
    }

    return suggestions;
  }

  /**
   * Tool-specific suggestions
   */
  private getToolSuggestions(context: SuggestionContext): ConfigSuggestion[] {
    const suggestions: ConfigSuggestion[] = [];
    const config = context.currentConfig;

    // Timeout configuration
    if (!config.timeout || config.timeout > 60000) {
      suggestions.push({
        id: 'tool-timeout-optimize',
        type: 'optimization',
        priority: 'medium',
        title: 'Optimize timeout setting',
        description: 'Set appropriate timeout to prevent hanging requests',
        reasoning: 'Long timeouts can impact user experience',
        confidence: 0.8,
        impact: { performance: 0.2, usability: 0.3 },
        action: {
          type: 'set_value',
          target: 'timeout',
          value: 30000,
        },
        tags: ['performance', 'reliability'],
      });
    }

    return suggestions;
  }

  /**
   * Workflow-specific suggestions
   */
  private getWorkflowSuggestions(context: SuggestionContext): ConfigSuggestion[] {
    const suggestions: ConfigSuggestion[] = [];
    const config = context.currentConfig;

    // Error handling
    if (!config.errorHandling) {
      suggestions.push({
        id: 'workflow-error-handling',
        type: 'best_practice',
        priority: 'high',
        title: 'Add error handling',
        description: 'Configure error handling for robust workflows',
        reasoning: 'Error handling prevents workflow failures',
        confidence: 0.9,
        impact: { maintainability: 0.4, usability: 0.3 },
        action: {
          type: 'add_field',
          target: 'errorHandling',
          value: {
            retries: 3,
            fallback: 'continue',
            notifications: true,
          },
        },
        tags: ['reliability', 'error-handling'],
      });
    }

    return suggestions;
  }

  /**
   * Performance-based suggestions
   */
  private getPerformanceSuggestions(context: SuggestionContext): ConfigSuggestion[] {
    const suggestions: ConfigSuggestion[] = [];
    const metrics = context.performanceMetrics;

    if (metrics?.averageResponseTime > 5000) {
      suggestions.push({
        id: 'performance-response-time',
        type: 'optimization',
        priority: 'high',
        title: 'Improve response time',
        description: 'Current response time is above recommended threshold',
        reasoning: 'Slow response times impact user experience',
        confidence: 0.8,
        impact: { performance: 0.5, usability: 0.4 },
        action: {
          type: 'set_value',
          target: 'maxTokens',
          value: Math.min(context.currentConfig.maxTokens || 1000, 500),
        },
        tags: ['performance', 'optimization'],
      });
    }

    return suggestions;
  }

  /**
   * Security-based suggestions
   */
  private getSecuritySuggestions(context: SuggestionContext): ConfigSuggestion[] {
    const suggestions: ConfigSuggestion[] = [];
    const config = context.currentConfig;

    // API key security
    if (config.apiKey && typeof config.apiKey === 'string') {
      suggestions.push({
        id: 'security-api-key',
        type: 'security',
        priority: 'critical',
        title: 'Secure API key storage',
        description: 'API keys should not be stored in plain text',
        reasoning: 'Plain text API keys pose security risks',
        confidence: 1.0,
        impact: { security: 1.0 },
        action: {
          type: 'set_value',
          target: 'apiKey',
          value: '${API_KEY}', // Environment variable reference
        },
        tags: ['security', 'api-keys'],
      });
    }

    return suggestions;
  }

  /**
   * Usability-based suggestions
   */
  private getUsabilitySuggestions(context: SuggestionContext): ConfigSuggestion[] {
    const suggestions: ConfigSuggestion[] = [];
    const config = context.currentConfig;

    // Description suggestions
    if (!config.description || config.description.length < 20) {
      suggestions.push({
        id: 'usability-description',
        type: 'completion',
        priority: 'low',
        title: 'Add detailed description',
        description: 'Descriptions help with organization and collaboration',
        reasoning: 'Good descriptions improve maintainability',
        confidence: 0.6,
        impact: { usability: 0.2, maintainability: 0.3 },
        action: {
          type: 'set_value',
          target: 'description',
          value: '',
        },
        tags: ['documentation', 'usability'],
      });
    }

    return suggestions;
  }

  /**
   * Filter and rank suggestions
   */
  private filterAndRankSuggestions(
    suggestions: ConfigSuggestion[],
    filter: SuggestionFilter
  ): ConfigSuggestion[] {
    let filtered = suggestions;

    // Apply filters
    if (filter.types) {
      filtered = filtered.filter(s => filter.types!.includes(s.type));
    }

    if (filter.priorities) {
      filtered = filtered.filter(s => filter.priorities!.includes(s.priority));
    }

    if (filter.tags) {
      filtered = filtered.filter(s => 
        s.tags.some(tag => filter.tags!.includes(tag))
      );
    }

    if (filter.minConfidence) {
      filtered = filtered.filter(s => s.confidence >= filter.minConfidence!);
    }

    if (filter.excludeApplied) {
      filtered = filtered.filter(s => !this.appliedSuggestions.has(s.id));
    }

    // Remove dismissed suggestions
    filtered = filtered.filter(s => !this.dismissedSuggestions.has(s.id));

    // Sort by priority and confidence
    filtered.sort((a, b) => {
      const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      if (priorityDiff !== 0) return priorityDiff;
      return b.confidence - a.confidence;
    });

    // Limit results
    if (filter.maxSuggestions) {
      filtered = filtered.slice(0, filter.maxSuggestions);
    }

    return filtered;
  }

  /**
   * Rule-based field suggestions
   */
  private getRuleBasedFieldSuggestions(
    field: string,
    currentValue: any,
    context: SuggestionContext
  ): ConfigSuggestion[] {
    // Implementation would depend on specific field types
    return [];
  }

  /**
   * Rule-based validation
   */
  private getRuleBasedValidation(
    config: Record<string, any>,
    context: SuggestionContext
  ): any {
    return {
      isValid: true,
      errors: [],
      suggestions: [],
    };
  }

  /**
   * Utility methods for config manipulation
   */
  private setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    let current = obj;
    
    for (let i = 0; i < keys.length - 1; i++) {
      if (!(keys[i] in current)) {
        current[keys[i]] = {};
      }
      current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]] = value;
  }

  private removeNestedValue(obj: any, path: string): void {
    const keys = path.split('.');
    let current = obj;
    
    for (let i = 0; i < keys.length - 1; i++) {
      if (!(keys[i] in current)) return;
      current = current[keys[i]];
    }
    
    delete current[keys[keys.length - 1]];
  }

  private restructureConfig(
    config: Record<string, any>,
    template: Record<string, any>
  ): Record<string, any> {
    // Deep merge with template structure
    return { ...template, ...config };
  }
}

export const suggestionEngine = new SuggestionEngine();
