import {
  Controller,
  Post,
  Get,
  Delete,
  Body,
  Param,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiBody, ApiProperty } from '@nestjs/swagger';
import { ApiKeyService, CreateApiKeyDto, ApiKeyResponse } from './api-key.service';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { RolesGuard } from './guards/roles.guard';
import { Roles } from './decorators/roles.decorator';
import { CurrentUser } from './decorators/current-user.decorator';
enum UserRole {
  SUPER_ADMIN = 'SUPER_ADMIN',
  ORG_ADMIN = 'ORG_ADMIN',
  DEVELOPER = 'DEVELOPER',
  VIEWER = 'VIEWER'
}
import { IsString, IsArray, IsOptional, IsDateString } from 'class-validator';

class CreateApiKeyRequestDto {
  @ApiProperty({ example: 'Production API Key' })
  @IsString()
  name: string;

  @ApiProperty({ example: 'API key for production environment', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ 
    example: ['agents:read', 'agents:write', 'tools:read'], 
    description: 'Array of permission strings' 
  })
  @IsArray()
  @IsString({ each: true })
  permissions: string[];

  @ApiProperty({ example: '2024-12-31T23:59:59Z', required: false })
  @IsOptional()
  @IsDateString()
  expiresAt?: string;
}

class ApiKeyListResponseDto {
  @ApiProperty({ example: 'api-key-id-123' })
  id: string;

  @ApiProperty({ example: 'Production API Key' })
  name: string;

  @ApiProperty({ example: 'API key for production environment' })
  description?: string;

  @ApiProperty({ example: ['agents:read', 'agents:write'] })
  permissions: string[];

  @ApiProperty({ example: 'sk_12345678' })
  keyPrefix: string;

  @ApiProperty({ example: '2024-12-31T23:59:59Z' })
  expiresAt?: Date;

  @ApiProperty({ example: true })
  isActive: boolean;

  @ApiProperty({ example: '2024-01-15T10:30:00Z' })
  lastUsedAt?: Date;

  @ApiProperty({ example: '2024-01-01T00:00:00Z' })
  createdAt: Date;
}

class ApiKeyStatsDto {
  @ApiProperty({ example: 5 })
  total: number;

  @ApiProperty({ example: 3 })
  active: number;

  @ApiProperty({ example: 2 })
  inactive: number;
}

@ApiTags('API Keys')
@Controller('api/v1/auth/api-keys')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class ApiKeyController {
  constructor(private apiKeyService: ApiKeyService) {}

  @Post()
  @Roles(UserRole.ORG_ADMIN, UserRole.SUPER_ADMIN)
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create new API key' })
  @ApiBody({ type: CreateApiKeyRequestDto })
  @ApiResponse({
    status: 201,
    description: 'API key created successfully',
  })
  @ApiResponse({
    status: 403,
    description: 'Insufficient permissions',
  })
  async createApiKey(
    @Body() createDto: CreateApiKeyRequestDto,
    @CurrentUser() user: any,
  ): Promise<ApiKeyResponse> {
    const apiKeyDto: CreateApiKeyDto = {
      name: createDto.name,
      description: createDto.description,
      permissions: createDto.permissions,
      expiresAt: createDto.expiresAt ? new Date(createDto.expiresAt) : undefined,
      organizationId: user.organizationId,
      createdBy: user.userId,
    };

    return this.apiKeyService.createApiKey(apiKeyDto);
  }

  @Get()
  @Roles(UserRole.ORG_ADMIN, UserRole.SUPER_ADMIN, UserRole.DEVELOPER)
  @ApiOperation({ summary: 'List API keys' })
  @ApiResponse({
    status: 200,
    description: 'API keys retrieved successfully',
    type: [ApiKeyListResponseDto],
  })
  async listApiKeys(@CurrentUser() user: any): Promise<Omit<ApiKeyResponse, 'key'>[]> {
    return this.apiKeyService.listApiKeys(user.organizationId, user.userId);
  }

  @Delete(':id')
  @Roles(UserRole.ORG_ADMIN, UserRole.SUPER_ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Revoke API key' })
  @ApiResponse({
    status: 200,
    description: 'API key revoked successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'API key not found',
  })
  async revokeApiKey(
    @Param('id') apiKeyId: string,
    @CurrentUser() user: any,
  ): Promise<{ message: string }> {
    await this.apiKeyService.revokeApiKey(apiKeyId, user.organizationId, user.userId);
    return { message: 'API key revoked successfully' };
  }

  @Get('stats')
  @Roles(UserRole.ORG_ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Get API key statistics' })
  @ApiResponse({
    status: 200,
    description: 'API key statistics retrieved successfully',
    type: ApiKeyStatsDto,
  })
  async getApiKeyStats(@CurrentUser() user: any): Promise<ApiKeyStatsDto> {
    return this.apiKeyService.getApiKeyStats(user.organizationId);
  }
}
