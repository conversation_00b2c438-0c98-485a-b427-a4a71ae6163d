(()=>{var e={};e.id=98,e.ids=[98],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},43490:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d}),t(64332),t(69319),t(77406),t(12874);var s=t(27105),r=t(15265),i=t(90157),n=t.n(i),l=t(44665),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(a,o);let d=["",{children:["auth",{children:["signin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,64332)),"C:\\laragon\\www\\max\\trae\\kilo-teset\\augsyanapseAI\\frontend\\app\\auth\\signin\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,69319)),"C:\\laragon\\www\\max\\trae\\kilo-teset\\augsyanapseAI\\frontend\\app\\auth\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,77406)),"C:\\laragon\\www\\max\\trae\\kilo-teset\\augsyanapseAI\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,12874,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\laragon\\www\\max\\trae\\kilo-teset\\augsyanapseAI\\frontend\\app\\auth\\signin\\page.tsx"],u="/auth/signin/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/auth/signin/page",pathname:"/auth/signin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},16898:(e,a,t)=>{Promise.resolve().then(t.bind(t,4961))},4961:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>x});var s=t(19899),r=t(5507),i=t(76153),n=t(54175),l=t(15950),o=t(95650),d=t(37513),c=t(20382),u=t(80046),m=t(50572),p=t(98015),h=t(22455);let g=d.Ry({email:d.Z_().email("Please enter a valid email address"),password:d.Z_().min(1,"Password is required"),organizationId:d.Z_().optional()});function x(){let e=(0,i.useRouter)(),a=(0,i.useSearchParams)(),{login:t,isLoading:d,error:x,clearError:f}=(0,m.a)(),[y,w]=(0,r.useState)(!1),{register:v,handleSubmit:j,formState:{errors:b},setError:N}=(0,l.cI)({resolver:(0,o.F)(g),defaultValues:{email:"",password:"",organizationId:""}}),z=async s=>{f();try{if(await t(s)){let t=a.get("callbackUrl")||"/dashboard";e.push(t)}}catch(e){e.field&&N(e.field,{type:"manual",message:e.message})}};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[s.jsx("h2",{className:"text-3xl font-bold tracking-tight text-gray-900",children:"Sign in to your account"}),(0,s.jsxs)("p",{className:"mt-2 text-sm text-gray-600",children:["Or"," ",s.jsx(n.default,{href:"/auth/signup",className:"font-medium text-primary-600 hover:text-primary-500",children:"create a new account"})]})]}),x&&!x.field&&s.jsx("div",{className:"rounded-md bg-error-50 p-4",children:s.jsx("div",{className:"text-sm text-error-700",children:x.message})}),(0,s.jsxs)("form",{className:"space-y-6",onSubmit:j(z),children:[s.jsx(u.I,{label:"Email address",type:"email",autoComplete:"email",placeholder:"Enter your email",error:b.email?.message,...v("email")}),s.jsx(u.I,{label:"Password",type:y?"text":"password",autoComplete:"current-password",placeholder:"Enter your password",error:b.password?.message,rightIcon:s.jsx("button",{type:"button",className:"text-gray-400 hover:text-gray-600",onClick:()=>w(!y),children:y?s.jsx(p.Z,{className:"h-5 w-5"}):s.jsx(h.Z,{className:"h-5 w-5"})}),...v("password")}),s.jsx(u.I,{label:"Organization ID (Optional)",type:"text",placeholder:"Enter organization ID",helperText:"Leave blank to use your default organization",error:b.organizationId?.message,...v("organizationId")}),s.jsx("div",{className:"flex items-center justify-between",children:s.jsx("div",{className:"text-sm",children:s.jsx(n.default,{href:"/auth/forgot-password",className:"font-medium text-primary-600 hover:text-primary-500",children:"Forgot your password?"})})}),s.jsx(c.z,{type:"submit",className:"w-full",loading:d,disabled:d,children:"Sign in"})]}),(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsxs)("div",{className:"relative",children:[s.jsx("div",{className:"absolute inset-0 flex items-center",children:s.jsx("div",{className:"w-full border-t border-gray-300"})}),s.jsx("div",{className:"relative flex justify-center text-sm",children:s.jsx("span",{className:"px-2 bg-white text-gray-500",children:"Or continue with"})})]}),(0,s.jsxs)("div",{className:"mt-6 grid grid-cols-2 gap-3",children:[(0,s.jsxs)(c.z,{variant:"outline",className:"w-full",onClick:()=>{console.log("SSO login not implemented yet")},children:[(0,s.jsxs)("svg",{className:"h-5 w-5 mr-2",viewBox:"0 0 24 24",children:[s.jsx("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),s.jsx("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),s.jsx("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),s.jsx("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Google"]}),(0,s.jsxs)(c.z,{variant:"outline",className:"w-full",onClick:()=>{console.log("Microsoft SSO not implemented yet")},children:[s.jsx("svg",{className:"h-5 w-5 mr-2",fill:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{d:"M11.4 24H0V12.6h11.4V24zM24 24H12.6V12.6H24V24zM11.4 11.4H0V0h11.4v11.4zM24 11.4H12.6V0H24v11.4z"})}),"Microsoft"]})]})]})]})}},50572:(e,a,t)=>{"use strict";t.d(a,{a:()=>n});var s=t(60878),r=t(76153),i=t(5507);function n(){let{data:e,status:a}=(0,s.useSession)(),t=(0,r.useRouter)(),[n,l]=(0,i.useState)(!1),[o,d]=(0,i.useState)(null),c=async e=>{l(!0),d(null);try{let a=await (0,s.signIn)("credentials",{email:e.email,password:e.password,organizationId:e.organizationId,redirect:!1});if(a?.error)return d("Invalid credentials"),!1;return t.push("/dashboard"),!0}catch(e){return d("Login failed"),!1}finally{l(!1)}},u=async e=>{l(!0),d(null);try{let a=await fetch("http://localhost:3000/api/v1/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!a.ok){let e=await a.json();return d(e.message||"Registration failed"),!1}return await c({email:e.email,password:e.password,organizationId:e.organizationId})}catch(e){return d("Registration failed"),!1}finally{l(!1)}},m=async()=>{l(!0);try{await (0,s.signOut)({redirect:!1}),t.push("/auth/signin")}finally{l(!1)}};return{user:e?.user,accessToken:e?.accessToken,isAuthenticated:!!e,isLoading:"loading"===a||n,error:o,login:c,register:u,logout:m,clearError:()=>d(null)}}},64332:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>s});let s=(0,t(2772).createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\app\auth\signin\page.tsx#default`)}};var a=require("../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),s=a.X(0,[942,544,695,188,975],()=>t(43490));module.exports=s})();