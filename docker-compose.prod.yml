version: '3.8'

services:
  # NGINX Load Balancer
  nginx:
    image: nginx:alpine
    container_name: synapseai-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    networks:
      - synapseai-network
    depends_on:
      - backend
    restart: unless-stopped

  # PostgreSQL Database with optimizations
  postgres:
    image: postgres:15-alpine
    container_name: synapseai-postgres-prod
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_prod_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
      - ./postgres/postgresql.conf:/etc/postgresql/postgresql.conf
    networks:
      - synapseai-network
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Redis Cluster for high availability
  redis-master:
    image: redis:7-alpine
    container_name: synapseai-redis-master-prod
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD} --maxmemory 2gb --maxmemory-policy allkeys-lru
    volumes:
      - redis_master_data:/data
    networks:
      - synapseai-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    restart: unless-stopped

  redis-replica:
    image: redis:7-alpine
    container_name: synapseai-redis-replica-prod
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD} --replicaof redis-master 6379 --masterauth ${REDIS_PASSWORD}
    volumes:
      - redis_replica_data:/data
    networks:
      - synapseai-network
    depends_on:
      redis-master:
        condition: service_healthy
    restart: unless-stopped

  # NESTJS Backend (multiple instances for load balancing)
  backend-1:
    build:
      context: .
      dockerfile: backend/Dockerfile
      target: production
    container_name: synapseai-backend-1-prod
    environment:
      NODE_ENV: production
      PORT: 3001
      DATABASE_URL: ${DATABASE_URL}
      REDIS_HOST: redis-master
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD}
      JWT_SECRET: ${JWT_SECRET}
      SENTRY_DSN: ${SENTRY_DSN}
      NEW_RELIC_LICENSE_KEY: ${NEW_RELIC_LICENSE_KEY}
    networks:
      - synapseai-network
    depends_on:
      postgres:
        condition: service_healthy
      redis-master:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3001/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  backend-2:
    build:
      context: .
      dockerfile: backend/Dockerfile
      target: production
    container_name: synapseai-backend-2-prod
    environment:
      NODE_ENV: production
      PORT: 3001
      DATABASE_URL: ${DATABASE_URL}
      REDIS_HOST: redis-master
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD}
      JWT_SECRET: ${JWT_SECRET}
      SENTRY_DSN: ${SENTRY_DSN}
      NEW_RELIC_LICENSE_KEY: ${NEW_RELIC_LICENSE_KEY}
    networks:
      - synapseai-network
    depends_on:
      postgres:
        condition: service_healthy
      redis-master:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3001/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  # Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:latest
    container_name: synapseai-prometheus-prod
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - synapseai-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped

  # Grafana for dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: synapseai-grafana-prod
    ports:
      - "3003:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_ADMIN_PASSWORD}
      GF_USERS_ALLOW_SIGN_UP: false
    networks:
      - synapseai-network
    depends_on:
      - prometheus
    restart: unless-stopped

volumes:
  postgres_prod_data:
    driver: local
  redis_master_data:
    driver: local
  redis_replica_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  synapseai-network:
    driver: bridge
