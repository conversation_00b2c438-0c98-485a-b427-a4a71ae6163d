"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiKeyController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const api_key_service_1 = require("./api-key.service");
const jwt_auth_guard_1 = require("./guards/jwt-auth.guard");
const roles_guard_1 = require("./guards/roles.guard");
const roles_decorator_1 = require("./decorators/roles.decorator");
const current_user_decorator_1 = require("./decorators/current-user.decorator");
var UserRole;
(function (UserRole) {
    UserRole["SUPER_ADMIN"] = "SUPER_ADMIN";
    UserRole["ORG_ADMIN"] = "ORG_ADMIN";
    UserRole["DEVELOPER"] = "DEVELOPER";
    UserRole["VIEWER"] = "VIEWER";
})(UserRole || (UserRole = {}));
const class_validator_1 = require("class-validator");
class CreateApiKeyRequestDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Production API Key' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateApiKeyRequestDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'API key for production environment', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateApiKeyRequestDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: ['agents:read', 'agents:write', 'tools:read'],
        description: 'Array of permission strings'
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateApiKeyRequestDto.prototype, "permissions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '2024-12-31T23:59:59Z', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateApiKeyRequestDto.prototype, "expiresAt", void 0);
class ApiKeyListResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'api-key-id-123' }),
    __metadata("design:type", String)
], ApiKeyListResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Production API Key' }),
    __metadata("design:type", String)
], ApiKeyListResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'API key for production environment' }),
    __metadata("design:type", String)
], ApiKeyListResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: ['agents:read', 'agents:write'] }),
    __metadata("design:type", Array)
], ApiKeyListResponseDto.prototype, "permissions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'sk_12345678' }),
    __metadata("design:type", String)
], ApiKeyListResponseDto.prototype, "keyPrefix", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '2024-12-31T23:59:59Z' }),
    __metadata("design:type", Date)
], ApiKeyListResponseDto.prototype, "expiresAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true }),
    __metadata("design:type", Boolean)
], ApiKeyListResponseDto.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '2024-01-15T10:30:00Z' }),
    __metadata("design:type", Date)
], ApiKeyListResponseDto.prototype, "lastUsedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '2024-01-01T00:00:00Z' }),
    __metadata("design:type", Date)
], ApiKeyListResponseDto.prototype, "createdAt", void 0);
class ApiKeyStatsDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ example: 5 }),
    __metadata("design:type", Number)
], ApiKeyStatsDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 3 }),
    __metadata("design:type", Number)
], ApiKeyStatsDto.prototype, "active", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 2 }),
    __metadata("design:type", Number)
], ApiKeyStatsDto.prototype, "inactive", void 0);
let ApiKeyController = class ApiKeyController {
    constructor(apiKeyService) {
        this.apiKeyService = apiKeyService;
    }
    async createApiKey(createDto, user) {
        const apiKeyDto = {
            name: createDto.name,
            description: createDto.description,
            permissions: createDto.permissions,
            expiresAt: createDto.expiresAt ? new Date(createDto.expiresAt) : undefined,
            organizationId: user.organizationId,
            createdBy: user.userId,
        };
        return this.apiKeyService.createApiKey(apiKeyDto);
    }
    async listApiKeys(user) {
        return this.apiKeyService.listApiKeys(user.organizationId, user.userId);
    }
    async revokeApiKey(apiKeyId, user) {
        await this.apiKeyService.revokeApiKey(apiKeyId, user.organizationId, user.userId);
        return { message: 'API key revoked successfully' };
    }
    async getApiKeyStats(user) {
        return this.apiKeyService.getApiKeyStats(user.organizationId);
    }
};
exports.ApiKeyController = ApiKeyController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)(UserRole.ORG_ADMIN, UserRole.SUPER_ADMIN),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiOperation)({ summary: 'Create new API key' }),
    (0, swagger_1.ApiBody)({ type: CreateApiKeyRequestDto }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'API key created successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Insufficient permissions',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [CreateApiKeyRequestDto, Object]),
    __metadata("design:returntype", Promise)
], ApiKeyController.prototype, "createApiKey", null);
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)(UserRole.ORG_ADMIN, UserRole.SUPER_ADMIN, UserRole.DEVELOPER),
    (0, swagger_1.ApiOperation)({ summary: 'List API keys' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'API keys retrieved successfully',
        type: [ApiKeyListResponseDto],
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ApiKeyController.prototype, "listApiKeys", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)(UserRole.ORG_ADMIN, UserRole.SUPER_ADMIN),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: 'Revoke API key' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'API key revoked successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'API key not found',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ApiKeyController.prototype, "revokeApiKey", null);
__decorate([
    (0, common_1.Get)('stats'),
    (0, roles_decorator_1.Roles)(UserRole.ORG_ADMIN, UserRole.SUPER_ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Get API key statistics' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'API key statistics retrieved successfully',
        type: ApiKeyStatsDto,
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ApiKeyController.prototype, "getApiKeyStats", null);
exports.ApiKeyController = ApiKeyController = __decorate([
    (0, swagger_1.ApiTags)('API Keys'),
    (0, common_1.Controller)('api/v1/auth/api-keys'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [api_key_service_1.ApiKeyService])
], ApiKeyController);
//# sourceMappingURL=api-key.controller.js.map