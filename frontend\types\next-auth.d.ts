import { DefaultSession, DefaultUser } from 'next-auth';
import { JWT, DefaultJWT } from 'next-auth/jwt';

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      email: string;
      firstName: string;
      lastName: string;
      role: 'admin' | 'developer' | 'viewer' | 'owner';
      organizationId: string;
      organizationName: string;
      organizationRole: 'owner' | 'admin' | 'member';
      permissions: string[];
      avatar?: string;
      emailVerified: boolean;
      twoFactorEnabled: boolean;
      lastLoginAt: string;
      createdAt: string;
    } & DefaultSession['user'];
    accessToken: string;
    refreshToken: string;
    error?: string;
  }

  interface User {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: 'admin' | 'developer' | 'viewer' | 'owner';
    organizationId: string;
    organizationName: string;
    organizationRole: 'owner' | 'admin' | 'member';
    permissions: string[];
    avatar?: string;
    emailVerified: boolean;
    twoFactorEnabled: boolean;
    accessToken: string;
    refreshToken: string;
    lastLoginAt: string;
    createdAt: string;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: 'admin' | 'developer' | 'viewer' | 'owner';
    organizationId: string;
    organizationName: string;
    organizationRole: 'owner' | 'admin' | 'member';
    permissions: string[];
    avatar?: string;
    emailVerified: boolean;
    twoFactorEnabled: boolean;
    accessToken: string;
    refreshToken: string;
    lastLoginAt: string;
    createdAt: string;
    error?: string;
  }
}
