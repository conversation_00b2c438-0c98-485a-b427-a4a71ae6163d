import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy as SamlStrategy as PassportSamlStrategy } from 'passport-saml';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class SamlStrategy extends PassportStrategy(PassportSamlStrategy, 'saml') {
  constructor(private configService: ConfigService) {
    super({
      entryPoint: configService.get('SAML_ENTRY_POINT'),
      issuer: configService.get('SAML_ISSUER'),
      cert: configService.get('SAML_CERT'),
      callbackUrl: configService.get('SAML_CALLBACK_URL'),
      authnRequestBinding: 'HTTP-POST',
      identifierFormat: 'urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress',
    });
  }

  async validate(profile: any): Promise<any> {
    // Extract user information from SAML assertion
    return {
      email: profile.nameID || profile.email,
      firstName: profile.firstName || profile.givenName,
      lastName: profile.lastName || profile.surname,
      groups: profile.groups || [],
      attributes: profile,
    };
  }
}
