{"name": "@prisma/engines-version", "version": "6.12.0-15.8047c96bbd92db98a2abc7c9323ce77c02c89dbc", "main": "index.js", "types": "index.d.ts", "license": "Apache-2.0", "author": "<PERSON> <suchane<PERSON>@prisma.io>", "prisma": {"enginesVersion": "8047c96bbd92db98a2abc7c9323ce77c02c89dbc"}, "repository": {"type": "git", "url": "https://github.com/prisma/engines-wrapper.git", "directory": "packages/engines-version"}, "devDependencies": {"@types/node": "18.19.76", "typescript": "4.9.5"}, "files": ["index.js", "index.d.ts"], "scripts": {"build": "tsc -d"}}