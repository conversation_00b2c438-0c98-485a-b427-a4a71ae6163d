exports.id=975,exports.ids=[975],exports.modules={74868:()=>{},20382:(e,r,t)=>{"use strict";t.d(r,{z:()=>o});var s=t(19899),a=t(5507);!function(){var e=Error("Cannot find module '@radix-ui/react-slot'");throw e.code="MODULE_NOT_FOUND",e}();var i=t(51138),n=t(66409);let l=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",gradient:"bg-gradient-to-r from-primary-500 to-primary-600 text-white hover:from-primary-600 hover:to-primary-700",success:"bg-success-500 text-white hover:bg-success-600",warning:"bg-warning-500 text-white hover:bg-warning-600",error:"bg-error-500 text-white hover:bg-error-600"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",xl:"h-12 rounded-lg px-10 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=a.forwardRef(({className:e,variant:r,size:t,asChild:a=!1,loading:i=!1,leftIcon:o,rightIcon:d,children:c,disabled:u,...m},x)=>{let f=a?Object(function(){var e=Error("Cannot find module '@radix-ui/react-slot'");throw e.code="MODULE_NOT_FOUND",e}()):"button";return(0,s.jsxs)(f,{className:(0,n.cn)(l({variant:r,size:t,className:e})),ref:x,disabled:u||i,...m,children:[i&&(0,s.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[s.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),s.jsx("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!i&&o&&s.jsx("span",{className:"mr-2",children:o}),c,!i&&d&&s.jsx("span",{className:"ml-2",children:d})]})});o.displayName="Button"},80046:(e,r,t)=>{"use strict";t.d(r,{I:()=>o});var s=t(19899),a=t(5507),i=t(51138),n=t(66409);let l=(0,i.j)("flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",{variants:{variant:{default:"border-input",error:"border-error-500 focus-visible:ring-error-500",success:"border-success-500 focus-visible:ring-success-500",warning:"border-warning-500 focus-visible:ring-warning-500"},size:{default:"h-10",sm:"h-9 px-2 text-xs",lg:"h-11 px-4",xl:"h-12 px-4 text-base"}},defaultVariants:{variant:"default",size:"default"}}),o=a.forwardRef(({className:e,variant:r,size:t,type:i,leftIcon:o,rightIcon:d,error:c,helperText:u,label:m,id:x,...f},h)=>{let p=x||a.useId(),g=!!c;return(0,s.jsxs)("div",{className:"w-full",children:[m&&s.jsx("label",{htmlFor:p,className:"block text-sm font-medium text-foreground mb-1",children:m}),(0,s.jsxs)("div",{className:"relative",children:[o&&s.jsx("div",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground",children:o}),s.jsx("input",{type:i,id:p,className:(0,n.cn)(l({variant:g?"error":r,size:t,className:e}),o&&"pl-10",d&&"pr-10"),ref:h,...f}),d&&s.jsx("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground",children:d})]}),(c||u)&&s.jsx("p",{className:(0,n.cn)("text-xs mt-1",g?"text-error-600":"text-muted-foreground"),children:c||u})]})});o.displayName="Input"},66409:(e,r,t)=>{"use strict";t.d(r,{cn:()=>a});var s=t(23332);function a(...e){return Object(function(){var e=Error("Cannot find module 'tailwind-merge'");throw e.code="MODULE_NOT_FOUND",e}())((0,s.W)(e))}!function(){var e=Error("Cannot find module 'tailwind-merge'");throw e.code="MODULE_NOT_FOUND",e}()},76153:(e,r,t)=>{"use strict";var s=t(81545);t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},69319:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i,metadata:()=>a});var s=t(35023);let a={title:"Authentication",description:"Sign in to your SynapseAI account"};function i({children:e}){return(0,s.jsxs)("div",{className:"min-h-screen flex",children:[(0,s.jsxs)("div",{className:"hidden lg:flex lg:w-1/2 bg-gradient-to-br from-primary-600 to-primary-800 relative overflow-hidden",children:[s.jsx("div",{className:"absolute inset-0 bg-black/20"}),s.jsx("div",{className:"relative z-10 flex flex-col justify-center px-12 text-white",children:(0,s.jsxs)("div",{className:"max-w-md",children:[s.jsx("h1",{className:"text-4xl font-bold mb-6",children:"Welcome to SynapseAI"}),s.jsx("p",{className:"text-xl text-primary-100 mb-8",children:"The universal AI orchestration platform that empowers you to build, deploy, and manage intelligent agents and workflows with ease."}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-2 h-2 bg-primary-300 rounded-full"}),s.jsx("span",{className:"text-primary-100",children:"No-code AI agent builder"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-2 h-2 bg-primary-300 rounded-full"}),s.jsx("span",{className:"text-primary-100",children:"Enterprise-grade security"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-2 h-2 bg-primary-300 rounded-full"}),s.jsx("span",{className:"text-primary-100",children:"Real-time collaboration"})]})]})]})}),s.jsx("div",{className:"absolute top-0 right-0 w-64 h-64 bg-primary-400/20 rounded-full -translate-y-32 translate-x-32"}),s.jsx("div",{className:"absolute bottom-0 left-0 w-96 h-96 bg-primary-400/10 rounded-full translate-y-48 -translate-x-48"})]}),s.jsx("div",{className:"flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:px-20 xl:px-24",children:(0,s.jsxs)("div",{className:"mx-auto w-full max-w-sm lg:w-96",children:[s.jsx("div",{className:"mb-8",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[s.jsx("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:s.jsx("span",{className:"text-white font-bold text-sm",children:"S"})}),s.jsx("span",{className:"text-2xl font-bold text-gray-900",children:"SynapseAI"})]})}),e]})})]})}},22455:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});var s=t(5507);let a=s.forwardRef(function({title:e,titleId:r,...t},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?s.createElement("title",{id:r},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},98015:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});var s=t(5507);let a=s.forwardRef(function({title:e,titleId:r,...t},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?s.createElement("title",{id:r},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))})}};