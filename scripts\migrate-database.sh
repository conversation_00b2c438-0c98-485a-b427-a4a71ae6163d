#!/bin/bash

# SynapseAI Database Migration Script
# Handles database migrations with proper error handling and rollback capabilities

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
BACKEND_DIR="$PROJECT_ROOT/backend"
LOG_FILE="$PROJECT_ROOT/logs/migration.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# Create logs directory if it doesn't exist
mkdir -p "$(dirname "$LOG_FILE")"

# Check if we're in the correct directory
if [[ ! -f "$BACKEND_DIR/prisma/schema.prisma" ]]; then
    error "Prisma schema not found. Please run this script from the project root."
    exit 1
fi

# Check if DATABASE_URL is set
if [[ -z "${DATABASE_URL:-}" ]]; then
    error "DATABASE_URL environment variable is not set"
    exit 1
fi

# Function to check database connectivity
check_database_connection() {
    log "Checking database connection..."
    
    cd "$BACKEND_DIR"
    
    if npx prisma db execute --stdin <<< "SELECT 1;" > /dev/null 2>&1; then
        success "Database connection successful"
        return 0
    else
        error "Cannot connect to database"
        return 1
    fi
}

# Function to backup database
backup_database() {
    log "Creating database backup..."
    
    local backup_dir="$PROJECT_ROOT/backups/migrations"
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local backup_file="$backup_dir/pre_migration_backup_$timestamp.sql"
    
    mkdir -p "$backup_dir"
    
    # Extract database details from DATABASE_URL
    local db_url="${DATABASE_URL}"
    
    if command -v pg_dump >/dev/null 2>&1; then
        if pg_dump "$db_url" > "$backup_file" 2>/dev/null; then
            success "Database backup created: $backup_file"
            echo "$backup_file"
        else
            warning "Failed to create database backup, continuing without backup"
            echo ""
        fi
    else
        warning "pg_dump not found, skipping backup"
        echo ""
    fi
}

# Function to generate migration
generate_migration() {
    local migration_name="$1"
    
    log "Generating migration: $migration_name"
    
    cd "$BACKEND_DIR"
    
    if npx prisma migrate dev --name "$migration_name" --create-only; then
        success "Migration generated successfully"
        return 0
    else
        error "Failed to generate migration"
        return 1
    fi
}

# Function to apply migrations
apply_migrations() {
    log "Applying database migrations..."
    
    cd "$BACKEND_DIR"
    
    # Check migration status first
    log "Checking migration status..."
    npx prisma migrate status
    
    # Apply migrations
    if npx prisma migrate deploy; then
        success "Migrations applied successfully"
        return 0
    else
        error "Failed to apply migrations"
        return 1
    fi
}

# Function to generate Prisma client
generate_client() {
    log "Generating Prisma client..."
    
    cd "$BACKEND_DIR"
    
    if npx prisma generate; then
        success "Prisma client generated successfully"
        return 0
    else
        error "Failed to generate Prisma client"
        return 1
    fi
}

# Function to seed database
seed_database() {
    log "Seeding database with initial data..."
    
    cd "$BACKEND_DIR"
    
    if npm run db:seed; then
        success "Database seeded successfully"
        return 0
    else
        error "Failed to seed database"
        return 1
    fi
}

# Function to validate schema
validate_schema() {
    log "Validating database schema..."
    
    cd "$BACKEND_DIR"
    
    if npx prisma validate; then
        success "Schema validation passed"
        return 0
    else
        error "Schema validation failed"
        return 1
    fi
}

# Function to reset database (for development)
reset_database() {
    warning "This will completely reset the database and all data will be lost!"
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log "Resetting database..."
        
        cd "$BACKEND_DIR"
        
        if npx prisma migrate reset --force; then
            success "Database reset successfully"
            return 0
        else
            error "Failed to reset database"
            return 1
        fi
    else
        log "Database reset cancelled"
        return 1
    fi
}

# Function to show migration status
show_status() {
    log "Checking migration status..."
    
    cd "$BACKEND_DIR"
    npx prisma migrate status
}

# Main function
main() {
    local command="${1:-apply}"
    
    log "Starting database migration process..."
    log "Command: $command"
    log "Database URL: ${DATABASE_URL}"
    
    case "$command" in
        "apply")
            check_database_connection || exit 1
            validate_schema || exit 1
            backup_database
            apply_migrations || exit 1
            generate_client || exit 1
            success "Migration process completed successfully"
            ;;
        
        "generate")
            local migration_name="${2:-auto_migration_$(date '+%Y%m%d_%H%M%S')}"
            validate_schema || exit 1
            generate_migration "$migration_name" || exit 1
            ;;
        
        "seed")
            check_database_connection || exit 1
            seed_database || exit 1
            ;;
        
        "reset")
            reset_database || exit 1
            ;;
        
        "status")
            check_database_connection || exit 1
            show_status
            ;;
        
        "validate")
            validate_schema || exit 1
            ;;
        
        "full")
            check_database_connection || exit 1
            validate_schema || exit 1
            backup_database
            apply_migrations || exit 1
            generate_client || exit 1
            seed_database || exit 1
            success "Full migration process completed successfully"
            ;;
        
        *)
            echo "Usage: $0 {apply|generate|seed|reset|status|validate|full} [migration_name]"
            echo ""
            echo "Commands:"
            echo "  apply     - Apply pending migrations"
            echo "  generate  - Generate new migration"
            echo "  seed      - Seed database with initial data"
            echo "  reset     - Reset database (development only)"
            echo "  status    - Show migration status"
            echo "  validate  - Validate schema"
            echo "  full      - Apply migrations, generate client, and seed"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
