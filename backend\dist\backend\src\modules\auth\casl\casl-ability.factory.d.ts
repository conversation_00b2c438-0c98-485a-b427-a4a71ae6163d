import { Ability, InferSubjects } from '@casl/ability';
declare enum UserRole {
    SUPER_ADMIN = "SUPER_ADMIN",
    ORG_ADMIN = "ORG_ADMIN",
    DEVELOPER = "DEVELOPER",
    VIEWER = "VIEWER"
}
type Subjects = InferSubjects<'User' | 'Organization' | 'Agent' | 'Tool' | 'Workflow' | 'Template' | 'Provider' | 'Widget' | 'Analytics' | 'Billing' | 'Session' | 'Notification' | 'Document' | 'Sandbox' | 'all'>;
export type Action = 'manage' | 'create' | 'read' | 'update' | 'delete' | 'execute' | 'publish';
export type AppAbility = Ability<[Action, Subjects]>;
export interface UserContext {
    userId: string;
    organizationId: string;
    role: UserRole;
    email: string;
}
export declare class CaslAbilityFactory {
    createForUser(user: UserContext): AppAbility;
    createForApiKey(organizationId: string, permissions: string[]): AppAbility;
}
export {};
