import { Ability, InferSubjects } from '@casl/ability';
import { UserRole } from '@prisma/client';
type Subjects = InferSubjects<'User' | 'Organization' | 'Agent' | 'Tool' | 'Workflow' | 'Template' | 'Provider' | 'Widget' | 'Analytics' | 'Billing' | 'Session' | 'Notification' | 'Document' | 'Sandbox' | 'all'>;
export type Action = 'manage' | 'create' | 'read' | 'update' | 'delete' | 'execute' | 'publish';
export type AppAbility = Ability<[Action, Subjects]>;
export interface UserContext {
    userId: string;
    organizationId: string;
    role: UserRole;
    email: string;
}
export declare class CaslAbilityFactory {
    createForUser(user: UserContext): AppAbility;
    createForApiKey(organizationId: string, permissions: string[]): AppAbility;
}
export {};
