import { useState, useEffect } from 'react';

export interface Breakpoint {
  name: string;
  min: number;
  max?: number;
  cols: number;
  gutters: number;
  margins: number;
}

export interface ResponsiveConfig {
  breakpoints: Breakpoint[];
  defaultCols: number;
  maxWidth: number;
  fluidTypography: boolean;
  touchTargetSize: number;
}

// Mobile-first breakpoint system
export const defaultBreakpoints: Breakpoint[] = [
  {
    name: 'xs',
    min: 0,
    max: 639,
    cols: 4,
    gutters: 16,
    margins: 16,
  },
  {
    name: 'sm',
    min: 640,
    max: 767,
    cols: 8,
    gutters: 20,
    margins: 24,
  },
  {
    name: 'md',
    min: 768,
    max: 1023,
    cols: 12,
    gutters: 24,
    margins: 32,
  },
  {
    name: 'lg',
    min: 1024,
    max: 1279,
    cols: 12,
    gutters: 32,
    margins: 40,
  },
  {
    name: 'xl',
    min: 1280,
    max: 1535,
    cols: 12,
    gutters: 32,
    margins: 48,
  },
  {
    name: '2xl',
    min: 1536,
    cols: 12,
    gutters: 32,
    margins: 64,
  },
];

export const responsiveConfig: ResponsiveConfig = {
  breakpoints: defaultBreakpoints,
  defaultCols: 12,
  maxWidth: 1440,
  fluidTypography: true,
  touchTargetSize: 44, // Minimum touch target size in pixels
};

/**
 * Get current breakpoint based on window width
 */
export function getCurrentBreakpoint(width: number): Breakpoint {
  const breakpoint = defaultBreakpoints.find(bp => 
    width >= bp.min && (bp.max === undefined || width <= bp.max)
  );  
  return breakpoint || defaultBreakpoints[defaultBreakpoints.length - 1]!;
}

/**
 * Check if current width matches breakpoint
 */
export function matchesBreakpoint(width: number, breakpointName: string): boolean {
  const breakpoint = defaultBreakpoints.find(bp => bp.name === breakpointName);
  if (!breakpoint) return false;
  
  return width >= breakpoint.min && (breakpoint.max === undefined || width <= breakpoint.max);
}

/**
 * Get responsive value based on current breakpoint
 */
export function getResponsiveValue<T>(
  values: Partial<Record<string, T>>,
  currentBreakpoint: string,
  fallback: T
): T {
  // Try current breakpoint first
  if (values[currentBreakpoint] !== undefined) {
    return values[currentBreakpoint]!;
  }

  // Fall back to smaller breakpoints
  const currentIndex = defaultBreakpoints.findIndex(bp => bp.name === currentBreakpoint);
  for (let i = currentIndex - 1; i >= 0; i--) {
    const bp = defaultBreakpoints[i];
    if (bp && values[bp.name] !== undefined) {
      return values[bp.name]!;
    }
  }

  return fallback;
}

/**
 * Generate responsive CSS classes
 */
export function generateResponsiveClasses(
  property: string,
  values: Partial<Record<string, string | number>>
): string {
  const classes: string[] = [];

  defaultBreakpoints.forEach(bp => {
    const value = values[bp!.name];
    if (value !== undefined) {
      const prefix = bp.name === 'xs' ? '' : `${bp.name}:`;
      classes.push(`${prefix}${property}-${value}`);
    }
  });

  return classes.join(' ');
}

/**
 * Create media query string for breakpoint
 */
export function createMediaQuery(breakpointName: string): string {
  const breakpoint = defaultBreakpoints.find(bp => bp.name === breakpointName);
  if (!breakpoint) return '';

  if (breakpoint.max === undefined) {
    return `(min-width: ${breakpoint.min}px)`;
  }

  return `(min-width: ${breakpoint.min}px) and (max-width: ${breakpoint.max}px)`;
}

/**
 * Responsive grid system utilities
 */
export class ResponsiveGrid {
  private config: ResponsiveConfig;

  constructor(config: ResponsiveConfig = responsiveConfig) {
    this.config = config;
  }

  /**
   * Calculate column width percentage
   */
  getColumnWidth(cols: number, totalCols?: number): string {
    const total = totalCols || this.config.defaultCols;
    return `${(cols / total) * 100}%`;
  }

  /**
   * Get gutter size for breakpoint
   */
  getGutterSize(breakpointName: string): number {
    const breakpoint = this.config.breakpoints.find(bp => bp.name === breakpointName);
    return breakpoint?.gutters || 24;
  }

  /**
   * Get margin size for breakpoint
   */
  getMarginSize(breakpointName: string): number {
    const breakpoint = this.config.breakpoints.find(bp => bp.name === breakpointName);
    return breakpoint?.margins || 32;
  }

  /**
   * Generate container classes
   */
  getContainerClasses(): string {
    const classes = ['w-full', 'mx-auto'];
    
    this.config.breakpoints.forEach(bp => {
      if (bp.margins) {
        const prefix = bp.name === 'xs' ? '' : `${bp.name}:`;
        classes.push(`${prefix}px-${bp.margins / 4}`); // Tailwind uses 4px units
      }
    });

    // Max width
    classes.push(`max-w-screen-2xl`);

    return classes.join(' ');
  }

  /**
   * Generate grid classes
   */
  getGridClasses(responsive: Partial<Record<string, number>>): string {
    const classes = ['grid'];

    this.config.breakpoints.forEach(bp => {
      const cols = responsive[bp!.name];
      if (cols !== undefined) {
        const prefix = bp.name === 'xs' ? '' : `${bp.name}:`;
        classes.push(`${prefix}grid-cols-${cols}`);
      }
    });

    return classes.join(' ');
  }

  /**
   * Generate gap classes
   */
  getGapClasses(): string {
    const classes: string[] = [];

    this.config.breakpoints.forEach(bp => {
      const gutter = bp.gutters / 4; // Convert to Tailwind units
      const prefix = bp.name === 'xs' ? '' : `${bp.name}:`;
      classes.push(`${prefix}gap-${gutter}`);
    });

    return classes.join(' ');
  }
}

/**
 * Responsive typography utilities
 */
export class ResponsiveTypography {
  private config: ResponsiveConfig;

  constructor(config: ResponsiveConfig = responsiveConfig) {
    this.config = config;
  }

  /**
   * Generate fluid typography classes
   */
  getFluidTextClasses(
    sizes: Partial<Record<string, string>>
  ): string {
    if (!this.config.fluidTypography) {
      return generateResponsiveClasses('text', sizes);
    }

    const classes: string[] = [];

    defaultBreakpoints.forEach(bp => {
      const size = sizes[bp.name];
      if (size !== undefined) {
        const prefix = bp.name === 'xs' ? '' : `${bp.name}:`;
        classes.push(`${prefix}text-${size}`);
      }
    });

    return classes.join(' ');
  }

  /**
   * Generate responsive line height classes
   */
  getLineHeightClasses(
    heights: Partial<Record<string, string>>
  ): string {
    return generateResponsiveClasses('leading', heights);
  }

  /**
   * Generate responsive spacing classes
   */
  getSpacingClasses(
    property: 'p' | 'm' | 'px' | 'py' | 'pt' | 'pb' | 'pl' | 'pr' | 'mx' | 'my' | 'mt' | 'mb' | 'ml' | 'mr',
    values: Partial<Record<string, string | number>>
  ): string {
    return generateResponsiveClasses(property, values);
  }
}

/**
 * Touch and interaction utilities
 */
export class TouchInteraction {
  private config: ResponsiveConfig;

  constructor(config: ResponsiveConfig = responsiveConfig) {
    this.config = config;
  }

  /**
   * Get minimum touch target size
   */
  getTouchTargetSize(): number {
    return this.config.touchTargetSize;
  }

  /**
   * Generate touch-friendly button classes
   */
  getTouchButtonClasses(size: 'sm' | 'md' | 'lg' = 'md'): string {
    const minSize = this.config.touchTargetSize;
    
    const sizeMap = {
      sm: Math.max(32, minSize * 0.8),
      md: minSize,
      lg: minSize * 1.2,
    };

    const targetSize = sizeMap[size];
    const padding = Math.max(8, (targetSize - 24) / 2); // Assuming 24px icon/text

    return `min-h-[${targetSize}px] min-w-[${targetSize}px] p-${Math.round(padding / 4)}`;
  }

  /**
   * Check if device supports touch
   */
  isTouchDevice(): boolean {
    if (typeof window === 'undefined') return false;
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  }

  /**
   * Check if device supports hover
   */
  supportsHover(): boolean {
    if (typeof window === 'undefined') return true;
    return window.matchMedia('(hover: hover)').matches;
  }
}

// Global instances
export const responsiveGrid = new ResponsiveGrid();
export const responsiveTypography = new ResponsiveTypography();
export const touchInteraction = new TouchInteraction();

/**
 * React hook for responsive breakpoints
 */
export function useBreakpoint() {
  if (typeof window === 'undefined') {
    return {
      current: 'lg',
      width: 1024,
      isMobile: false,
      isTablet: false,
      isDesktop: true,
    };
  }

  const [width, setWidth] = useState(window.innerWidth);
  const [current, setCurrent] = useState(getCurrentBreakpoint(window.innerWidth).name);

  useEffect(() => {
    const handleResize = () => {
      const newWidth = window.innerWidth;
      setWidth(newWidth);
      setCurrent(getCurrentBreakpoint(newWidth).name);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return {
    current,
    width,
    isMobile: matchesBreakpoint(width, 'xs') || matchesBreakpoint(width, 'sm'),
    isTablet: matchesBreakpoint(width, 'md'),
    isDesktop: matchesBreakpoint(width, 'lg') || matchesBreakpoint(width, 'xl') || matchesBreakpoint(width, '2xl'),
  };
}


