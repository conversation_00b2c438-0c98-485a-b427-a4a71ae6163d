"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SsoController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const sso_service_1 = require("./sso.service");
const jwt_auth_guard_1 = require("../guards/jwt-auth.guard");
const roles_guard_1 = require("../guards/roles.guard");
const roles_decorator_1 = require("../decorators/roles.decorator");
const current_user_decorator_1 = require("../decorators/current-user.decorator");
const class_validator_1 = require("class-validator");
var UserRole;
(function (UserRole) {
    UserRole["SUPER_ADMIN"] = "SUPER_ADMIN";
    UserRole["ORG_ADMIN"] = "ORG_ADMIN";
    UserRole["DEVELOPER"] = "DEVELOPER";
    UserRole["VIEWER"] = "VIEWER";
})(UserRole || (UserRole = {}));
class CreateSsoProviderDto {
}
__decorate([
    ApiProperty({ example: 'Company SAML' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateSsoProviderDto.prototype, "name", void 0);
__decorate([
    ApiProperty({ example: 'SAML', enum: ['SAML', 'OIDC', 'ACTIVE_DIRECTORY'] }),
    (0, class_validator_1.IsEnum)(['SAML', 'OIDC', 'ACTIVE_DIRECTORY']),
    __metadata("design:type", String)
], CreateSsoProviderDto.prototype, "type", void 0);
__decorate([
    ApiProperty({
        example: {
            entryPoint: 'https://company.okta.com/sso/saml',
            issuer: 'company-saml',
            cert: '-----BEGIN CERTIFICATE-----...'
        }
    }),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CreateSsoProviderDto.prototype, "config", void 0);
let SsoController = class SsoController {
    constructor(ssoService) {
        this.ssoService = ssoService;
    }
    async createSsoProvider(createDto, user) {
        return this.ssoService.createSsoProvider({
            ...createDto,
            organizationId: user.organizationId,
        });
    }
    async getSsoProviders(user) {
        return this.ssoService.getSsoProviders(user.organizationId);
    }
    async initiateSsoLogin(providerId, res) {
        res.redirect(`/api/v1/auth/sso/callback/${providerId}`);
    }
    async handleSsoCallback(providerId, req) {
        const userProfile = req.user;
        return this.ssoService.processSsoCallback(providerId, userProfile);
    }
    async getSsoMetadata(providerId, res) {
        res.setHeader('Content-Type', 'application/xml');
        res.send('<?xml version="1.0"?><EntityDescriptor>...</EntityDescriptor>');
    }
};
exports.SsoController = SsoController;
__decorate([
    (0, common_1.Post)('providers'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(UserRole.ORG_ADMIN, UserRole.SUPER_ADMIN),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create SSO provider configuration' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'SSO provider created successfully',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [CreateSsoProviderDto, Object]),
    __metadata("design:returntype", Promise)
], SsoController.prototype, "createSsoProvider", null);
__decorate([
    (0, common_1.Get)('providers'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(UserRole.ORG_ADMIN, UserRole.SUPER_ADMIN, UserRole.DEVELOPER),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get SSO providers for organization' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'SSO providers retrieved successfully',
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SsoController.prototype, "getSsoProviders", null);
__decorate([
    (0, common_1.Get)('login/:providerId'),
    (0, swagger_1.ApiOperation)({ summary: 'Initiate SSO login' }),
    (0, swagger_1.ApiResponse)({
        status: 302,
        description: 'Redirect to SSO provider',
    }),
    __param(0, (0, common_1.Param)('providerId')),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], SsoController.prototype, "initiateSsoLogin", null);
__decorate([
    (0, common_1.Post)('callback/:providerId'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: 'Handle SSO callback' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'SSO authentication successful',
    }),
    __param(0, (0, common_1.Param)('providerId')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], SsoController.prototype, "handleSsoCallback", null);
__decorate([
    (0, common_1.Get)('metadata/:providerId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get SSO provider metadata' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'SSO provider metadata',
    }),
    __param(0, (0, common_1.Param)('providerId')),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], SsoController.prototype, "getSsoMetadata", null);
exports.SsoController = SsoController = __decorate([
    (0, swagger_1.ApiTags)('SSO Authentication'),
    (0, common_1.Controller)('api/v1/auth/sso'),
    __metadata("design:paramtypes", [sso_service_1.SsoService])
], SsoController);
//# sourceMappingURL=sso.controller.js.map