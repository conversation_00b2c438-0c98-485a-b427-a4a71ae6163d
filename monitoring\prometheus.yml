# Prometheus Configuration for SynapseAI Production Monitoring
# Comprehensive metrics collection for AI platform

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'synapseai-production'
    environment: 'production'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Load rules once and periodically evaluate them
rule_files:
  - "alert_rules.yml"
  - "recording_rules.yml"

# Scrape configurations
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # SynapseAI Backend Services
  - job_name: 'synapseai-backend'
    static_configs:
      - targets: 
        - 'backend-1:3001'
        - 'backend-2:3001'
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
      - source_labels: [__address__]
        regex: '([^:]+):(.*)'
        target_label: __address__
        replacement: '${1}:3001'

  # NGINX Load Balancer
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:8080']
    scrape_interval: 30s
    metrics_path: /nginx_status
    honor_labels: true

  # PostgreSQL Database
  - job_name: 'postgresql'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s
    honor_labels: true

  # Redis Cache
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s
    honor_labels: true

  # Node Exporter (System Metrics)
  - job_name: 'node-exporter'
    static_configs:
      - targets: 
        - 'node-exporter-1:9100'
        - 'node-exporter-2:9100'
    scrape_interval: 30s
    honor_labels: true

  # cAdvisor (Container Metrics)
  - job_name: 'cadvisor'
    static_configs:
      - targets: 
        - 'cadvisor-1:8080'
        - 'cadvisor-2:8080'
    scrape_interval: 30s
    honor_labels: true

  # Blackbox Exporter (Endpoint Monitoring)
  - job_name: 'blackbox'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - https://synapseai.com
        - https://api.synapseai.com/health
        - https://widgets.synapseai.com
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

  # AI Provider Monitoring (Custom Metrics)
  - job_name: 'ai-providers'
    static_configs:
      - targets: ['backend-1:3001', 'backend-2:3001']
    scrape_interval: 60s
    metrics_path: /metrics/ai-providers
    honor_labels: true

  # Queue Monitoring (Bull/BullMQ)
  - job_name: 'queue-metrics'
    static_configs:
      - targets: ['backend-1:3001', 'backend-2:3001']
    scrape_interval: 30s
    metrics_path: /metrics/queues
    honor_labels: true

  # Custom SynapseAI Metrics
  - job_name: 'synapseai-custom'
    static_configs:
      - targets: ['backend-1:3001', 'backend-2:3001']
    scrape_interval: 15s
    metrics_path: /metrics/custom
    honor_labels: true
    relabel_configs:
      - source_labels: [__address__]
        target_label: service
        replacement: 'synapseai-backend'

# Remote write configuration (for long-term storage)
remote_write:
  - url: "https://prometheus-remote-write.synapseai.com/api/v1/write"
    basic_auth:
      username: "prometheus"
      password: "secure_remote_write_password"
    queue_config:
      max_samples_per_send: 1000
      max_shards: 200
      capacity: 2500

# Storage configuration
storage:
  tsdb:
    retention.time: 30d
    retention.size: 50GB
    wal-compression: true
