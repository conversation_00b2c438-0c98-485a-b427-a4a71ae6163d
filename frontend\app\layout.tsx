import type { <PERSON>ada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { AuthProvider } from '@/components/auth/AuthProvider';
import { QueryProvider } from '@/components/providers/QueryProvider';
import { ToastProvider } from '@/components/providers/ToastProvider';
import { ThemeProvider } from '@/components/providers/ThemeProvider';
import { A11yProvider } from '@/components/accessibility/A11yProvider';
import { OrganizationProvider } from '@/components/organization/OrganizationProvider';

const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
});

export const metadata: Metadata = {
  title: {
    default: 'SynapseAI - Universal AI Orchestration Platform',
    template: '%s | SynapseAI',
  },
  description: 'Build, deploy, and manage AI agents, tools, and workflows with SynapseAI\'s no-code platform.',
  keywords: [
    'AI',
    'artificial intelligence',
    'automation',
    'no-code',
    'agents',
    'workflows',
    'orchestration',
    'SynapseAI',
  ],
  authors: [{ name: 'SynapseAI Team' }],
  creator: '<PERSON><PERSON>pse<PERSON><PERSON>',
  publisher: 'SynapseAI',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3001'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: '/',
    title: 'SynapseAI - Universal AI Orchestration Platform',
    description: 'Build, deploy, and manage AI agents, tools, and workflows with SynapseAI\'s no-code platform.',
    siteName: 'SynapseAI',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'SynapseAI Platform',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'SynapseAI - Universal AI Orchestration Platform',
    description: 'Build, deploy, and manage AI agents, tools, and workflows with SynapseAI\'s no-code platform.',
    images: ['/og-image.png'],
    creator: '@synapseai',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION || undefined,
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={inter.variable} suppressHydrationWarning>
      <head>
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#0ea5e9" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
      </head>
      <body className="min-h-screen bg-background font-sans antialiased">
        <ThemeProvider
          enableTransitions={true}
          transitionDuration={200}
        >
          <A11yProvider>
            <AuthProvider>
              <OrganizationProvider>
                <QueryProvider>
                  <ToastProvider>
                    <div className="relative flex min-h-screen flex-col">
                      <div className="flex-1">
                        {children}
                      </div>
                    </div>
                  </ToastProvider>
                </QueryProvider>
              </OrganizationProvider>
            </AuthProvider>
          </A11yProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
