
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.12.0
 * Query Engine version: 8047c96bbd92db98a2abc7c9323ce77c02c89dbc
 */
Prisma.prismaVersion = {
  client: "6.12.0",
  engine: "8047c96bbd92db98a2abc7c9323ce77c02c89dbc"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.OrganizationScalarFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  settings: 'settings',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  passwordHash: 'passwordHash',
  firstName: 'firstName',
  lastName: 'lastName',
  role: 'role',
  organizationId: 'organizationId',
  isActive: 'isActive',
  lastLoginAt: 'lastLoginAt',
  ssoProvider: 'ssoProvider',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.RoleScalarFieldEnum = {
  id: 'id',
  name: 'name',
  permissions: 'permissions',
  organizationId: 'organizationId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SessionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  organizationId: 'organizationId',
  agentId: 'agentId',
  toolId: 'toolId',
  workflowId: 'workflowId',
  context: 'context',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TemplateScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  category: 'category',
  content: 'content',
  variables: 'variables',
  metadata: 'metadata',
  isPublic: 'isPublic',
  organizationId: 'organizationId',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TemplateVersionScalarFieldEnum = {
  id: 'id',
  templateId: 'templateId',
  version: 'version',
  content: 'content',
  variables: 'variables',
  changelog: 'changelog',
  isActive: 'isActive',
  createdAt: 'createdAt'
};

exports.Prisma.AgentScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  templateId: 'templateId',
  configuration: 'configuration',
  isActive: 'isActive',
  organizationId: 'organizationId',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AgentExecutionScalarFieldEnum = {
  id: 'id',
  agentId: 'agentId',
  sessionId: 'sessionId',
  input: 'input',
  output: 'output',
  status: 'status',
  startedAt: 'startedAt',
  completedAt: 'completedAt',
  duration: 'duration',
  errorMessage: 'errorMessage',
  metadata: 'metadata',
  organizationId: 'organizationId'
};

exports.Prisma.ToolScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  type: 'type',
  configuration: 'configuration',
  schema: 'schema',
  isActive: 'isActive',
  organizationId: 'organizationId',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ToolExecutionScalarFieldEnum = {
  id: 'id',
  toolId: 'toolId',
  sessionId: 'sessionId',
  input: 'input',
  output: 'output',
  status: 'status',
  startedAt: 'startedAt',
  completedAt: 'completedAt',
  duration: 'duration',
  errorMessage: 'errorMessage',
  metadata: 'metadata',
  organizationId: 'organizationId'
};

exports.Prisma.HybridScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  agentId: 'agentId',
  toolIds: 'toolIds',
  configuration: 'configuration',
  workflow: 'workflow',
  isActive: 'isActive',
  organizationId: 'organizationId',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.HybridExecutionScalarFieldEnum = {
  id: 'id',
  hybridId: 'hybridId',
  sessionId: 'sessionId',
  input: 'input',
  output: 'output',
  status: 'status',
  steps: 'steps',
  currentStep: 'currentStep',
  startedAt: 'startedAt',
  completedAt: 'completedAt',
  duration: 'duration',
  errorMessage: 'errorMessage',
  metadata: 'metadata',
  organizationId: 'organizationId'
};

exports.Prisma.WorkflowScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  definition: 'definition',
  agentIds: 'agentIds',
  toolIds: 'toolIds',
  isActive: 'isActive',
  organizationId: 'organizationId',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.WorkflowExecutionScalarFieldEnum = {
  id: 'id',
  workflowId: 'workflowId',
  sessionId: 'sessionId',
  input: 'input',
  output: 'output',
  status: 'status',
  steps: 'steps',
  currentStep: 'currentStep',
  startedAt: 'startedAt',
  completedAt: 'completedAt',
  duration: 'duration',
  errorMessage: 'errorMessage',
  metadata: 'metadata',
  organizationId: 'organizationId'
};

exports.Prisma.ProviderScalarFieldEnum = {
  id: 'id',
  name: 'name',
  type: 'type',
  configuration: 'configuration',
  credentials: 'credentials',
  isActive: 'isActive',
  organizationId: 'organizationId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProviderUsageScalarFieldEnum = {
  id: 'id',
  providerId: 'providerId',
  model: 'model',
  tokensUsed: 'tokensUsed',
  requestCount: 'requestCount',
  cost: 'cost',
  date: 'date',
  organizationId: 'organizationId'
};

exports.Prisma.HITLRequestScalarFieldEnum = {
  id: 'id',
  type: 'type',
  title: 'title',
  description: 'description',
  context: 'context',
  priority: 'priority',
  status: 'status',
  assignedTo: 'assignedTo',
  expiresAt: 'expiresAt',
  organizationId: 'organizationId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.HITLDecisionScalarFieldEnum = {
  id: 'id',
  requestId: 'requestId',
  decision: 'decision',
  reason: 'reason',
  data: 'data',
  decidedBy: 'decidedBy',
  decidedAt: 'decidedAt'
};

exports.Prisma.DocumentScalarFieldEnum = {
  id: 'id',
  name: 'name',
  type: 'type',
  content: 'content',
  url: 'url',
  metadata: 'metadata',
  size: 'size',
  isProcessed: 'isProcessed',
  organizationId: 'organizationId',
  uploadedBy: 'uploadedBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DocumentChunkScalarFieldEnum = {
  id: 'id',
  documentId: 'documentId',
  content: 'content',
  embedding: 'embedding',
  metadata: 'metadata',
  chunkIndex: 'chunkIndex',
  startPos: 'startPos',
  endPos: 'endPos'
};

exports.Prisma.KnowledgeSearchScalarFieldEnum = {
  id: 'id',
  query: 'query',
  documentIds: 'documentIds',
  results: 'results',
  metadata: 'metadata',
  organizationId: 'organizationId',
  createdAt: 'createdAt'
};

exports.Prisma.WidgetScalarFieldEnum = {
  id: 'id',
  name: 'name',
  type: 'type',
  targetId: 'targetId',
  configuration: 'configuration',
  styling: 'styling',
  embedCode: 'embedCode',
  isActive: 'isActive',
  organizationId: 'organizationId',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.WidgetExecutionScalarFieldEnum = {
  id: 'id',
  widgetId: 'widgetId',
  sessionId: 'sessionId',
  input: 'input',
  output: 'output',
  status: 'status',
  startedAt: 'startedAt',
  completedAt: 'completedAt',
  duration: 'duration',
  errorMessage: 'errorMessage',
  metadata: 'metadata',
  organizationId: 'organizationId'
};

exports.Prisma.AnalyticsScalarFieldEnum = {
  id: 'id',
  event: 'event',
  category: 'category',
  properties: 'properties',
  userId: 'userId',
  sessionId: 'sessionId',
  timestamp: 'timestamp',
  organizationId: 'organizationId'
};

exports.Prisma.MetricsScalarFieldEnum = {
  id: 'id',
  name: 'name',
  value: 'value',
  unit: 'unit',
  tags: 'tags',
  timestamp: 'timestamp',
  organizationId: 'organizationId'
};

exports.Prisma.BillingScalarFieldEnum = {
  id: 'id',
  period: 'period',
  totalCost: 'totalCost',
  breakdown: 'breakdown',
  status: 'status',
  dueDate: 'dueDate',
  paidAt: 'paidAt',
  organizationId: 'organizationId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UsageMeterScalarFieldEnum = {
  id: 'id',
  name: 'name',
  value: 'value',
  unit: 'unit',
  period: 'period',
  organizationId: 'organizationId',
  updatedAt: 'updatedAt'
};

exports.Prisma.QuotaScalarFieldEnum = {
  id: 'id',
  name: 'name',
  limit: 'limit',
  used: 'used',
  period: 'period',
  organizationId: 'organizationId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.NotificationScalarFieldEnum = {
  id: 'id',
  title: 'title',
  message: 'message',
  type: 'type',
  channel: 'channel',
  recipientId: 'recipientId',
  data: 'data',
  isRead: 'isRead',
  sentAt: 'sentAt',
  organizationId: 'organizationId',
  createdAt: 'createdAt'
};

exports.Prisma.NotificationPreferenceScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  channel: 'channel',
  type: 'type',
  enabled: 'enabled',
  organizationId: 'organizationId'
};

exports.Prisma.SandboxScalarFieldEnum = {
  id: 'id',
  name: 'name',
  type: 'type',
  targetId: 'targetId',
  configuration: 'configuration',
  isActive: 'isActive',
  organizationId: 'organizationId',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TestResultScalarFieldEnum = {
  id: 'id',
  sandboxId: 'sandboxId',
  testName: 'testName',
  input: 'input',
  expectedOutput: 'expectedOutput',
  actualOutput: 'actualOutput',
  status: 'status',
  passed: 'passed',
  errorMessage: 'errorMessage',
  duration: 'duration',
  metadata: 'metadata',
  organizationId: 'organizationId',
  createdAt: 'createdAt'
};

exports.Prisma.ApiKeyScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  keyHash: 'keyHash',
  keyPrefix: 'keyPrefix',
  permissions: 'permissions',
  expiresAt: 'expiresAt',
  isActive: 'isActive',
  lastUsedAt: 'lastUsedAt',
  organizationId: 'organizationId',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SsoProviderScalarFieldEnum = {
  id: 'id',
  name: 'name',
  type: 'type',
  config: 'config',
  isActive: 'isActive',
  organizationId: 'organizationId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};
exports.UserRole = exports.$Enums.UserRole = {
  SUPER_ADMIN: 'SUPER_ADMIN',
  ORG_ADMIN: 'ORG_ADMIN',
  DEVELOPER: 'DEVELOPER',
  VIEWER: 'VIEWER'
};

exports.ExecutionStatus = exports.$Enums.ExecutionStatus = {
  PENDING: 'PENDING',
  RUNNING: 'RUNNING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
  CANCELLED: 'CANCELLED'
};

exports.ApprovalStatus = exports.$Enums.ApprovalStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  EXPIRED: 'EXPIRED'
};

exports.NotificationChannel = exports.$Enums.NotificationChannel = {
  EMAIL: 'EMAIL',
  SMS: 'SMS',
  PUSH: 'PUSH',
  IN_APP: 'IN_APP',
  WEBHOOK: 'WEBHOOK'
};

exports.Prisma.ModelName = {
  Organization: 'Organization',
  User: 'User',
  Role: 'Role',
  Session: 'Session',
  Template: 'Template',
  TemplateVersion: 'TemplateVersion',
  Agent: 'Agent',
  AgentExecution: 'AgentExecution',
  Tool: 'Tool',
  ToolExecution: 'ToolExecution',
  Hybrid: 'Hybrid',
  HybridExecution: 'HybridExecution',
  Workflow: 'Workflow',
  WorkflowExecution: 'WorkflowExecution',
  Provider: 'Provider',
  ProviderUsage: 'ProviderUsage',
  HITLRequest: 'HITLRequest',
  HITLDecision: 'HITLDecision',
  Document: 'Document',
  DocumentChunk: 'DocumentChunk',
  KnowledgeSearch: 'KnowledgeSearch',
  Widget: 'Widget',
  WidgetExecution: 'WidgetExecution',
  Analytics: 'Analytics',
  Metrics: 'Metrics',
  Billing: 'Billing',
  UsageMeter: 'UsageMeter',
  Quota: 'Quota',
  Notification: 'Notification',
  NotificationPreference: 'NotificationPreference',
  Sandbox: 'Sandbox',
  TestResult: 'TestResult',
  ApiKey: 'ApiKey',
  SsoProvider: 'SsoProvider'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
