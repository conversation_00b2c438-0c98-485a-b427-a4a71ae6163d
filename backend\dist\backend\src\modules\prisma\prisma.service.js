"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PrismaService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrismaService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const client_1 = require("@prisma/client");
let PrismaService = PrismaService_1 = class PrismaService extends client_1.PrismaClient {
    constructor(configService) {
        super({
            datasources: {
                db: {
                    url: configService.get('DATABASE_URL'),
                },
            },
            log: [
                {
                    emit: 'event',
                    level: 'query',
                },
                {
                    emit: 'event',
                    level: 'error',
                },
                {
                    emit: 'event',
                    level: 'info',
                },
                {
                    emit: 'event',
                    level: 'warn',
                },
            ],
        });
        this.configService = configService;
        this.logger = new common_1.Logger(PrismaService_1.name);
        if (configService.get('NODE_ENV') === 'development') {
            this.$on('query', (e) => {
                this.logger.debug(`Query: ${e.query}`);
                this.logger.debug(`Params: ${e.params}`);
                this.logger.debug(`Duration: ${e.duration}ms`);
            });
        }
        this.$on('error', (e) => {
            this.logger.error('Database error:', e);
        });
        this.$on('info', (e) => {
            this.logger.log(`Database info: ${e.message}`);
        });
        this.$on('warn', (e) => {
            this.logger.warn(`Database warning: ${e.message}`);
        });
    }
    async onModuleInit() {
        try {
            await this.$connect();
            this.logger.log('Successfully connected to database');
        }
        catch (error) {
            this.logger.error('Failed to connect to database:', error);
            throw error;
        }
    }
    async onModuleDestroy() {
        try {
            await this.$disconnect();
            this.logger.log('Successfully disconnected from database');
        }
        catch (error) {
            this.logger.error('Error disconnecting from database:', error);
        }
    }
    async executeTransaction(fn, maxRetries = 3) {
        let lastError;
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return await this.$transaction(fn);
            }
            catch (error) {
                lastError = error;
                this.logger.warn(`Transaction attempt ${attempt} failed:`, error);
                if (attempt === maxRetries) {
                    break;
                }
                await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
            }
        }
        this.logger.error(`Transaction failed after ${maxRetries} attempts:`, lastError);
        throw lastError;
    }
    async isHealthy() {
        try {
            await this.$queryRaw `SELECT 1`;
            return true;
        }
        catch (error) {
            this.logger.error('Database health check failed:', error);
            return false;
        }
    }
    forOrganization(organizationId) {
        return {
            users: this.user.findMany({ where: { organizationId } }),
            roles: this.role.findMany({ where: { organizationId } }),
            sessions: this.session.findMany({ where: { organizationId } }),
            templates: this.template.findMany({ where: { organizationId } }),
            templateVersions: (templateId) => this.templateVersion.findMany({ where: { template: { organizationId }, templateId } }),
            agents: this.agent.findMany({ where: { organizationId } }),
            agentExecutions: this.agentExecution.findMany({ where: { organizationId } }),
            tools: this.tool.findMany({ where: { organizationId } }),
            toolExecutions: this.toolExecution.findMany({ where: { organizationId } }),
            hybrids: this.hybrid.findMany({ where: { organizationId } }),
            hybridExecutions: this.hybridExecution.findMany({ where: { organizationId } }),
            workflows: this.workflow.findMany({ where: { organizationId } }),
            workflowExecutions: this.workflowExecution.findMany({ where: { organizationId } }),
            providers: this.provider.findMany({ where: { organizationId } }),
            providerUsage: this.providerUsage.findMany({ where: { organizationId } }),
            hitlRequests: this.hITLRequest.findMany({ where: { organizationId } }),
            documents: this.document.findMany({ where: { organizationId } }),
            knowledgeSearches: this.knowledgeSearch.findMany({ where: { organizationId } }),
            widgets: this.widget.findMany({ where: { organizationId } }),
            widgetExecutions: this.widgetExecution.findMany({ where: { organizationId } }),
            analytics: this.analytics.findMany({ where: { organizationId } }),
            metrics: this.metrics.findMany({ where: { organizationId } }),
            billing: this.billing.findMany({ where: { organizationId } }),
            usageMeters: this.usageMeter.findMany({ where: { organizationId } }),
            quotas: this.quota.findMany({ where: { organizationId } }),
            notifications: this.notification.findMany({ where: { organizationId } }),
            notificationPreferences: this.notificationPreference.findMany({ where: { organizationId } }),
            sandboxes: this.sandbox.findMany({ where: { organizationId } }),
            testResults: this.testResult.findMany({ where: { organizationId } }),
        };
    }
    async validateOrganizationAccess(userId, organizationId) {
        try {
            const user = await this.user.findFirst({
                where: {
                    id: userId,
                    organizationId,
                    isActive: true,
                },
            });
            return !!user;
        }
        catch (error) {
            this.logger.error('Organization access validation failed:', error);
            return false;
        }
    }
};
exports.PrismaService = PrismaService;
exports.PrismaService = PrismaService = PrismaService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], PrismaService);
//# sourceMappingURL=prisma.service.js.map