export declare const API_VERSION = "v1";
export declare const API_PREFIX = "/api/v1";
export declare const DEFAULT_PAGE_SIZE = 20;
export declare const MAX_PAGE_SIZE = 100;
export declare const SESSION_TIMEOUT: number;
export declare const SESSION_CLEANUP_INTERVAL: number;
export declare const DEFAULT_RATE_LIMIT: {
    windowMs: number;
    maxRequests: number;
};
export declare const AUTH_RATE_LIMIT: {
    windowMs: number;
    maxRequests: number;
};
export declare const MAX_FILE_SIZE: number;
export declare const ALLOWED_FILE_TYPES: string[];
export declare const VALIDATION_RULES: {
    email: {
        maxLength: number;
        pattern: RegExp;
    };
    password: {
        minLength: number;
        maxLength: number;
        pattern: RegExp;
    };
    organizationSlug: {
        minLength: number;
        maxLength: number;
        pattern: RegExp;
    };
    name: {
        minLength: number;
        maxLength: number;
    };
};
export declare const ERROR_CODES: {
    readonly INVALID_CREDENTIALS: "INVALID_CREDENTIALS";
    readonly TOKEN_EXPIRED: "TOKEN_EXPIRED";
    readonly TOKEN_INVALID: "TOKEN_INVALID";
    readonly UNAUTHORIZED: "UNAUTHORIZED";
    readonly FORBIDDEN: "FORBIDDEN";
    readonly VALIDATION_ERROR: "VALIDATION_ERROR";
    readonly INVALID_INPUT: "INVALID_INPUT";
    readonly MISSING_REQUIRED_FIELD: "MISSING_REQUIRED_FIELD";
    readonly RESOURCE_NOT_FOUND: "RESOURCE_NOT_FOUND";
    readonly RESOURCE_ALREADY_EXISTS: "RESOURCE_ALREADY_EXISTS";
    readonly RESOURCE_CONFLICT: "RESOURCE_CONFLICT";
    readonly INTERNAL_SERVER_ERROR: "INTERNAL_SERVER_ERROR";
    readonly SERVICE_UNAVAILABLE: "SERVICE_UNAVAILABLE";
    readonly DATABASE_ERROR: "DATABASE_ERROR";
    readonly EXTERNAL_SERVICE_ERROR: "EXTERNAL_SERVICE_ERROR";
    readonly RATE_LIMIT_EXCEEDED: "RATE_LIMIT_EXCEEDED";
    readonly ORGANIZATION_LIMIT_EXCEEDED: "ORGANIZATION_LIMIT_EXCEEDED";
    readonly FEATURE_NOT_AVAILABLE: "FEATURE_NOT_AVAILABLE";
};
export declare const HTTP_STATUS: {
    readonly OK: 200;
    readonly CREATED: 201;
    readonly NO_CONTENT: 204;
    readonly BAD_REQUEST: 400;
    readonly UNAUTHORIZED: 401;
    readonly FORBIDDEN: 403;
    readonly NOT_FOUND: 404;
    readonly CONFLICT: 409;
    readonly UNPROCESSABLE_ENTITY: 422;
    readonly TOO_MANY_REQUESTS: 429;
    readonly INTERNAL_SERVER_ERROR: 500;
    readonly SERVICE_UNAVAILABLE: 503;
};
export declare const WEBSOCKET_EVENTS: {
    readonly CONNECTION: "connection";
    readonly DISCONNECT: "disconnect";
    readonly ERROR: "error";
    readonly USER_MESSAGE: "user_message";
    readonly THINKING_STATUS: "thinking_status";
    readonly TEXT_CHUNK: "text_chunk";
    readonly TOOL_CALL_START: "tool_call_start";
    readonly TOOL_CALL_RESULT: "tool_call_result";
    readonly TOOL_CALL_ERROR: "tool_call_error";
    readonly REQUEST_USER_INPUT: "request_user_input";
    readonly USER_RESPONSE: "user_response";
    readonly STATE_UPDATE: "state_update";
    readonly CONTROL_SIGNAL: "control_signal";
};
export declare const QUEUE_NAMES: {
    readonly AGENT_EXECUTION: "agent-execution";
    readonly TOOL_EXECUTION: "tool-execution";
    readonly WORKFLOW_EXECUTION: "workflow-execution";
    readonly NOTIFICATION: "notification";
    readonly ANALYTICS: "analytics";
    readonly BILLING: "billing";
};
export declare const CACHE_KEYS: {
    readonly USER_SESSION: (sessionId: string) => string;
    readonly USER_PERMISSIONS: (userId: string) => string;
    readonly ORGANIZATION_SETTINGS: (orgId: string) => string;
    readonly RATE_LIMIT: (key: string) => string;
    readonly FEATURE_FLAGS: (orgId: string) => string;
};
export declare const DEFAULT_CONFIG: {
    readonly jwt: {
        readonly expiresIn: "1h";
        readonly refreshExpiresIn: "7d";
    };
    readonly pagination: {
        readonly defaultPage: 1;
        readonly defaultLimit: 20;
        readonly maxLimit: 100;
    };
    readonly upload: {
        readonly maxFileSize: number;
        readonly allowedTypes: string[];
    };
};
