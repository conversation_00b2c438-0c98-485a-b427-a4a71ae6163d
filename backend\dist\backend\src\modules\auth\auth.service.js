"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const config_1 = require("@nestjs/config");
const bcrypt = require("bcryptjs");
const prisma_service_1 = require("../prisma/prisma.service");
const redis_service_1 = require("../redis/redis.service");
const logger_service_1 = require("../../services/logger/logger.service");
const client_1 = require("@prisma/client");
let AuthService = class AuthService {
    constructor(jwtService, prismaService, redisService, configService, logger) {
        this.jwtService = jwtService;
        this.prismaService = prismaService;
        this.redisService = redisService;
        this.configService = configService;
        this.logger = logger;
        this.saltRounds = 12;
        this.refreshTokenExpiry = 7 * 24 * 60 * 60;
    }
    async validateUser(email, password, organizationId) {
        try {
            const user = await this.prismaService.user.findFirst({
                where: {
                    email,
                    organizationId,
                    isActive: true,
                },
                include: {
                    organization: true,
                },
            });
            if (!user || !user.passwordHash) {
                throw new common_1.UnauthorizedException('Invalid credentials');
            }
            const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
            if (!isPasswordValid) {
                throw new common_1.UnauthorizedException('Invalid credentials');
            }
            await this.prismaService.user.update({
                where: { id: user.id },
                data: { lastLoginAt: new Date() },
            });
            const { passwordHash, ...result } = user;
            return result;
        }
        catch (error) {
            this.logger.error('User validation failed:', error);
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
    }
    async login(loginDto) {
        const user = await this.validateUser(loginDto.email, loginDto.password, loginDto.organizationId);
        const payload = {
            userId: user.id,
            organizationId: user.organizationId,
            role: user.role,
            email: user.email,
        };
        const accessToken = this.jwtService.sign(payload);
        const refreshToken = await this.generateRefreshToken(user.id, user.organizationId);
        this.logger.audit('USER_LOGIN', 'success', {
            userId: user.id,
            organizationId: user.organizationId,
            resource: 'user',
            details: {
                email: user.email,
                role: user.role,
            },
        });
        return {
            accessToken,
            refreshToken,
            expiresIn: parseInt(this.configService.get('JWT_EXPIRES_IN', '3600')),
        };
    }
    async register(registerDto) {
        const organization = await this.prismaService.organization.findFirst({
            where: {
                id: registerDto.organizationId,
                isActive: true,
            },
        });
        if (!organization) {
            throw new common_1.BadRequestException('Invalid organization');
        }
        const existingUser = await this.prismaService.user.findFirst({
            where: {
                email: registerDto.email,
                organizationId: registerDto.organizationId,
            },
        });
        if (existingUser) {
            throw new common_1.ConflictException('User already exists');
        }
        this.validatePasswordStrength(registerDto.password);
        const passwordHash = await bcrypt.hash(registerDto.password, this.saltRounds);
        const user = await this.prismaService.user.create({
            data: {
                email: registerDto.email,
                passwordHash,
                firstName: registerDto.firstName,
                lastName: registerDto.lastName,
                organizationId: registerDto.organizationId,
                role: registerDto.role || client_1.UserRole.VIEWER,
                isActive: true,
            },
            include: {
                organization: true,
            },
        });
        const payload = {
            userId: user.id,
            organizationId: user.organizationId,
            role: user.role,
            email: user.email,
        };
        const accessToken = this.jwtService.sign(payload);
        const refreshToken = await this.generateRefreshToken(user.id, user.organizationId);
        this.logger.audit('USER_REGISTER', 'success', {
            userId: user.id,
            organizationId: user.organizationId,
            resource: 'user',
            details: {
                email: user.email,
                role: user.role,
            },
        });
        return {
            accessToken,
            refreshToken,
            expiresIn: parseInt(this.configService.get('JWT_EXPIRES_IN', '3600')),
        };
    }
    async refreshToken(refreshToken) {
        try {
            const tokenData = await this.redisService.get(`refresh_token:${refreshToken}`);
            if (!tokenData) {
                throw new common_1.UnauthorizedException('Invalid refresh token');
            }
            const { userId, organizationId } = JSON.parse(tokenData);
            const user = await this.prismaService.user.findFirst({
                where: {
                    id: userId,
                    organizationId,
                    isActive: true,
                },
            });
            if (!user) {
                throw new common_1.UnauthorizedException('User not found');
            }
            const payload = {
                userId: user.id,
                organizationId: user.organizationId,
                role: user.role,
                email: user.email,
            };
            const newAccessToken = this.jwtService.sign(payload);
            const newRefreshToken = await this.generateRefreshToken(user.id, user.organizationId);
            await this.redisService.del(`refresh_token:${refreshToken}`);
            return {
                accessToken: newAccessToken,
                refreshToken: newRefreshToken,
                expiresIn: parseInt(this.configService.get('JWT_EXPIRES_IN', '3600')),
            };
        }
        catch (error) {
            this.logger.error('Token refresh failed:', error);
            throw new common_1.UnauthorizedException('Invalid refresh token');
        }
    }
    async logout(userId, refreshToken) {
        try {
            if (refreshToken) {
                await this.redisService.del(`refresh_token:${refreshToken}`);
            }
            const pattern = `refresh_token:*:${userId}:*`;
            const keys = await this.redisService.keys(pattern);
            if (keys.length > 0) {
                await this.redisService.del(...keys);
            }
            this.logger.audit('USER_LOGOUT', 'success', {
                userId,
                resource: 'user',
            });
        }
        catch (error) {
            this.logger.error('Logout failed:', error);
        }
    }
    async generateRefreshToken(userId, organizationId) {
        const tokenId = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const refreshToken = `${userId}_${organizationId}_${tokenId}`;
        const tokenData = {
            userId,
            organizationId,
            createdAt: new Date().toISOString(),
        };
        await this.redisService.setex(`refresh_token:${refreshToken}`, this.refreshTokenExpiry, JSON.stringify(tokenData));
        return refreshToken;
    }
    validatePasswordStrength(password) {
        const minLength = 8;
        const hasUpperCase = /[A-Z]/.test(password);
        const hasLowerCase = /[a-z]/.test(password);
        const hasNumbers = /\d/.test(password);
        const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
        if (password.length < minLength) {
            throw new common_1.BadRequestException('Password must be at least 8 characters long');
        }
        if (!hasUpperCase) {
            throw new common_1.BadRequestException('Password must contain at least one uppercase letter');
        }
        if (!hasLowerCase) {
            throw new common_1.BadRequestException('Password must contain at least one lowercase letter');
        }
        if (!hasNumbers) {
            throw new common_1.BadRequestException('Password must contain at least one number');
        }
        if (!hasSpecialChar) {
            throw new common_1.BadRequestException('Password must contain at least one special character');
        }
    }
    async verifyToken(token) {
        try {
            return this.jwtService.verify(token);
        }
        catch (error) {
            throw new common_1.UnauthorizedException('Invalid token');
        }
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [jwt_1.JwtService,
        prisma_service_1.PrismaService,
        redis_service_1.RedisService,
        config_1.ConfigService,
        logger_service_1.LoggerService])
], AuthService);
//# sourceMappingURL=auth.service.js.map