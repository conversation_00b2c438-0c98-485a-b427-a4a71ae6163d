# NGINX Load Balancer Configuration for SynapseAI Production
# High-performance reverse proxy with SSL termination and load balancing

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# Worker configuration
worker_rlimit_nofile 65535;

events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
}

http {
    # Basic Settings
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Logging Format
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';
    
    access_log /var/log/nginx/access.log main;
    
    # Performance Settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    keepalive_requests 1000;
    types_hash_max_size 2048;
    server_tokens off;
    
    # Buffer Settings
    client_body_buffer_size 128k;
    client_max_body_size 100m;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    output_buffers 1 32k;
    postpone_output 1460;
    
    # Timeout Settings
    client_body_timeout 12;
    client_header_timeout 12;
    send_timeout 10;
    
    # Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;
    limit_req_zone $binary_remote_addr zone=widgets:10m rate=50r/s;
    
    # Connection Limiting
    limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;
    limit_conn conn_limit_per_ip 20;
    
    # Upstream Backend Servers
    upstream synapseai_backend {
        least_conn;
        server backend-1:3001 max_fails=3 fail_timeout=30s weight=1;
        server backend-2:3001 max_fails=3 fail_timeout=30s weight=1;
        keepalive 32;
    }
    
    # Upstream Frontend Servers
    upstream synapseai_frontend {
        least_conn;
        server frontend-1:3000 max_fails=3 fail_timeout=30s weight=1;
        server frontend-2:3000 max_fails=3 fail_timeout=30s weight=1;
        keepalive 32;
    }
    
    # SSL Configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;
    ssl_stapling on;
    ssl_stapling_verify on;
    
    # Security Headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' wss: https:; frame-ancestors 'none';" always;
    
    # Main Application Server
    server {
        listen 80;
        server_name synapseai.com www.synapseai.com;
        return 301 https://$server_name$request_uri;
    }
    
    server {
        listen 443 ssl http2;
        server_name synapseai.com www.synapseai.com;
        
        # SSL Certificates
        ssl_certificate /etc/nginx/ssl/synapseai.com.crt;
        ssl_certificate_key /etc/nginx/ssl/synapseai.com.key;
        ssl_trusted_certificate /etc/nginx/ssl/ca-bundle.crt;
        
        # Security Headers
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
        
        # Frontend Application
        location / {
            proxy_pass http://synapseai_frontend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            proxy_read_timeout 86400;
        }
        
        # API Endpoints
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://synapseai_backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            proxy_read_timeout 300;
            proxy_connect_timeout 60;
            proxy_send_timeout 300;
        }
        
        # Authentication Endpoints (stricter rate limiting)
        location /api/v1/auth/ {
            limit_req zone=auth burst=10 nodelay;
            
            proxy_pass http://synapseai_backend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_read_timeout 60;
        }
        
        # WebSocket Connections
        location /socket.io/ {
            proxy_pass http://synapseai_backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_read_timeout 86400;
            proxy_send_timeout 86400;
        }
        
        # Health Check
        location /health {
            access_log off;
            proxy_pass http://synapseai_backend;
            proxy_set_header Host $host;
        }
        
        # Static Assets (with caching)
        location /static/ {
            expires 30d;
            add_header Cache-Control "public, immutable";
            proxy_pass http://synapseai_frontend;
        }
    }
    
    # Widget Subdomain
    server {
        listen 443 ssl http2;
        server_name widgets.synapseai.com;
        
        # SSL Certificates
        ssl_certificate /etc/nginx/ssl/widgets.synapseai.com.crt;
        ssl_certificate_key /etc/nginx/ssl/widgets.synapseai.com.key;
        
        # CORS Headers for Widget Embedding
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization" always;
        
        location / {
            limit_req zone=widgets burst=100 nodelay;
            
            # Handle preflight requests
            if ($request_method = 'OPTIONS') {
                add_header Access-Control-Allow-Origin "*";
                add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
                add_header Access-Control-Allow-Headers "Content-Type, Authorization";
                add_header Access-Control-Max-Age 86400;
                add_header Content-Length 0;
                add_header Content-Type text/plain;
                return 204;
            }
            
            proxy_pass http://synapseai_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Widget-specific caching
            expires 1h;
            add_header Cache-Control "public, max-age=3600";
        }
    }
    
    # Monitoring and Admin
    server {
        listen 8080;
        server_name localhost;
        
        # NGINX Status
        location /nginx_status {
            stub_status on;
            access_log off;
            allow 127.0.0.1;
            allow 10.0.0.0/8;
            deny all;
        }
        
        # Health Check for Load Balancer
        location /lb_health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}

# Stream block for TCP/UDP load balancing (if needed)
stream {
    # Redis Load Balancing (if using multiple Redis instances)
    upstream redis_backend {
        server redis-master:6379 max_fails=3 fail_timeout=30s;
        server redis-replica:6379 max_fails=3 fail_timeout=30s backup;
    }
    
    server {
        listen 6379;
        proxy_pass redis_backend;
        proxy_timeout 3s;
        proxy_responses 1;
    }
}
