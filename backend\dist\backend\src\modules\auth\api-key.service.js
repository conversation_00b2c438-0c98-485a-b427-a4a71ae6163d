"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiKeyService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const logger_service_1 = require("../../services/logger/logger.service");
const crypto = require("crypto");
let ApiKeyService = class ApiKeyService {
    constructor(prismaService, logger) {
        this.prismaService = prismaService;
        this.logger = logger;
    }
    async createApiKey(createDto) {
        const keyId = crypto.randomBytes(8).toString('hex');
        const keySecret = crypto.randomBytes(32).toString('hex');
        const apiKey = `sk_${keyId}_${keySecret}`;
        const keyPrefix = `sk_${keyId}`;
        const keyHash = crypto.createHash('sha256').update(apiKey).digest('hex');
        try {
            const apiKeyRecord = await this.prismaService.apiKey.create({
                data: {
                    name: createDto.name,
                    description: createDto.description,
                    keyHash,
                    keyPrefix,
                    permissions: createDto.permissions,
                    expiresAt: createDto.expiresAt,
                    organizationId: createDto.organizationId,
                    createdBy: createDto.createdBy,
                    isActive: true,
                },
            });
            this.logger.audit('API_KEY_CREATED', 'success', {
                userId: createDto.createdBy,
                organizationId: createDto.organizationId,
                resource: 'api_key',
                details: {
                    apiKeyId: apiKeyRecord.id,
                    permissions: createDto.permissions,
                },
            });
            return {
                id: apiKeyRecord.id,
                name: apiKeyRecord.name,
                description: apiKeyRecord.description,
                permissions: apiKeyRecord.permissions,
                key: apiKey,
                keyPrefix: apiKeyRecord.keyPrefix,
                expiresAt: apiKeyRecord.expiresAt,
                isActive: apiKeyRecord.isActive,
                lastUsedAt: apiKeyRecord.lastUsedAt,
                createdAt: apiKeyRecord.createdAt,
            };
        }
        catch (error) {
            this.logger.error('Failed to create API key:', error);
            throw new common_1.BadRequestException('Failed to create API key');
        }
    }
    async validateApiKey(apiKey) {
        if (!apiKey || !apiKey.startsWith('sk_')) {
            throw new common_1.UnauthorizedException('Invalid API key format');
        }
        const keyPrefix = apiKey.substring(0, 11);
        const keyHash = crypto.createHash('sha256').update(apiKey).digest('hex');
        try {
            const apiKeyRecord = await this.prismaService.apiKey.findFirst({
                where: {
                    keyHash,
                    keyPrefix,
                    isActive: true,
                },
                include: {
                    organization: {
                        select: {
                            id: true,
                            name: true,
                            slug: true,
                            isActive: true,
                        },
                    },
                    creator: {
                        select: {
                            id: true,
                            email: true,
                            firstName: true,
                            lastName: true,
                        },
                    },
                },
            });
            if (!apiKeyRecord || !apiKeyRecord.organization.isActive) {
                throw new common_1.UnauthorizedException('Invalid or inactive API key');
            }
            if (apiKeyRecord.expiresAt && apiKeyRecord.expiresAt < new Date()) {
                throw new common_1.UnauthorizedException('API key has expired');
            }
            await this.prismaService.apiKey.update({
                where: { id: apiKeyRecord.id },
                data: { lastUsedAt: new Date() },
            });
            return {
                apiKeyId: apiKeyRecord.id,
                organizationId: apiKeyRecord.organizationId,
                permissions: apiKeyRecord.permissions,
                organization: apiKeyRecord.organization,
                creator: apiKeyRecord.creator,
            };
        }
        catch (error) {
            if (error instanceof common_1.UnauthorizedException) {
                throw error;
            }
            this.logger.error('API key validation failed:', error);
            throw new common_1.UnauthorizedException('Invalid API key');
        }
    }
    async listApiKeys(organizationId, userId) {
        const apiKeys = await this.prismaService.apiKey.findMany({
            where: {
                organizationId,
            },
            select: {
                id: true,
                name: true,
                description: true,
                permissions: true,
                keyPrefix: true,
                expiresAt: true,
                isActive: true,
                lastUsedAt: true,
                createdAt: true,
            },
            orderBy: {
                createdAt: 'desc',
            },
        });
        return apiKeys.map(key => ({
            ...key,
            permissions: key.permissions,
        }));
    }
    async revokeApiKey(apiKeyId, organizationId, userId) {
        try {
            const apiKey = await this.prismaService.apiKey.findFirst({
                where: {
                    id: apiKeyId,
                    organizationId,
                },
            });
            if (!apiKey) {
                throw new common_1.BadRequestException('API key not found');
            }
            await this.prismaService.apiKey.update({
                where: { id: apiKeyId },
                data: { isActive: false },
            });
            this.logger.audit('API_KEY_REVOKED', 'success', {
                userId,
                organizationId,
                resource: 'api_key',
                details: {
                    apiKeyId,
                },
            });
        }
        catch (error) {
            this.logger.error('Failed to revoke API key:', error);
            throw new common_1.BadRequestException('Failed to revoke API key');
        }
    }
    async getApiKeyStats(organizationId) {
        const stats = await this.prismaService.apiKey.groupBy({
            by: ['isActive'],
            where: { organizationId },
            _count: true,
        });
        const totalKeys = stats.reduce((sum, stat) => sum + stat._count, 0);
        const activeKeys = stats.find(stat => stat.isActive)?._count || 0;
        const inactiveKeys = totalKeys - activeKeys;
        return {
            total: totalKeys,
            active: activeKeys,
            inactive: inactiveKeys,
        };
    }
};
exports.ApiKeyService = ApiKeyService;
exports.ApiKeyService = ApiKeyService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        logger_service_1.LoggerService])
], ApiKeyService);
//# sourceMappingURL=api-key.service.js.map