/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5Claragon%5Cwww%5Cmax%5Ctrae%5Ckilo-teset%5CaugsyanapseAI%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5Cmax%5Ctrae%5Ckilo-teset%5CaugsyanapseAI%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5Claragon%5Cwww%5Cmax%5Ctrae%5Ckilo-teset%5CaugsyanapseAI%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5Cmax%5Ctrae%5Ckilo-teset%5CaugsyanapseAI%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?41d8\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/page.tsx */ \"(rsc)/./app/dashboard/page.tsx\")), \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5Claragon%5Cwww%5Cmax%5Ctrae%5Ckilo-teset%5CaugsyanapseAI%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5Cmax%5Ctrae%5Ckilo-teset%5CaugsyanapseAI%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cfrontend%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cfrontend%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/page.tsx */ \"(ssr)/./app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDbGFyYWdvbiU1QyU1Q3d3dyU1QyU1Q21heCU1QyU1Q3RyYWUlNUMlNUNraWxvLXRlc2V0JTVDJTVDYXVnc3lhbmFwc2VBSSU1QyU1Q2Zyb250ZW5kJTVDJTVDYXBwJTVDJTVDZGFzaGJvYXJkJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRKQUErSCIsInNvdXJjZXMiOlsid2VicGFjazovL3N5bmFwc2VhaS1mcm9udGVuZC8/NjM1MSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXGxhcmFnb25cXFxcd3d3XFxcXG1heFxcXFx0cmFlXFxcXGtpbG8tdGVzZXRcXFxcYXVnc3lhbmFwc2VBSVxcXFxmcm9udGVuZFxcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cfrontend%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cfrontend%5C%5Ccomponents%5C%5Caccessibility%5C%5CA11yProvider.tsx%22%2C%22ids%22%3A%5B%22A11yProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cfrontend%5C%5Ccomponents%5C%5Cauth%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cfrontend%5C%5Ccomponents%5C%5Corganization%5C%5COrganizationProvider.tsx%22%2C%22ids%22%3A%5B%22OrganizationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cfrontend%5C%5Ccomponents%5C%5Cproviders%5C%5CQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cfrontend%5C%5Ccomponents%5C%5Cproviders%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cfrontend%5C%5Ccomponents%5C%5Cproviders%5C%5CToastProvider.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cfrontend%5C%5Ccomponents%5C%5Caccessibility%5C%5CA11yProvider.tsx%22%2C%22ids%22%3A%5B%22A11yProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cfrontend%5C%5Ccomponents%5C%5Cauth%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cfrontend%5C%5Ccomponents%5C%5Corganization%5C%5COrganizationProvider.tsx%22%2C%22ids%22%3A%5B%22OrganizationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cfrontend%5C%5Ccomponents%5C%5Cproviders%5C%5CQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cfrontend%5C%5Ccomponents%5C%5Cproviders%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cfrontend%5C%5Ccomponents%5C%5Cproviders%5C%5CToastProvider.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/accessibility/A11yProvider.tsx */ \"(ssr)/./components/accessibility/A11yProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/auth/AuthProvider.tsx */ \"(ssr)/./components/auth/AuthProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/organization/OrganizationProvider.tsx */ \"(ssr)/./components/organization/OrganizationProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers/QueryProvider.tsx */ \"(ssr)/./components/providers/QueryProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers/ThemeProvider.tsx */ \"(ssr)/./components/providers/ThemeProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers/ToastProvider.tsx */ \"(ssr)/./components/providers/ToastProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cfrontend%5C%5Ccomponents%5C%5Caccessibility%5C%5CA11yProvider.tsx%22%2C%22ids%22%3A%5B%22A11yProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cfrontend%5C%5Ccomponents%5C%5Cauth%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cfrontend%5C%5Ccomponents%5C%5Corganization%5C%5COrganizationProvider.tsx%22%2C%22ids%22%3A%5B%22OrganizationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cfrontend%5C%5Ccomponents%5C%5Cproviders%5C%5CQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cfrontend%5C%5Ccomponents%5C%5Cproviders%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cfrontend%5C%5Ccomponents%5C%5Cproviders%5C%5CToastProvider.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Ckilo-teset%5C%5CaugsyanapseAI%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/../node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction DashboardPage() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (status === \"loading\") return;\n        if (!session) {\n            router.push(\"/auth/signin\");\n            return;\n        }\n        setIsLoading(false);\n    }, [\n        session,\n        status,\n        router\n    ]);\n    if (isLoading || status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 26,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: \"\\uD83E\\uDDE0 SynapseAI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: [\n                                            \"Welcome, \",\n                                            session?.user?.name || session?.user?.email\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push(\"/api/auth/signout\"),\n                                        className: \"bg-red-600 text-white px-4 py-2 rounded-md text-sm hover:bg-red-700\",\n                                        children: \"Sign Out\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-4 py-6 sm:px-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-4 border-dashed border-gray-200 rounded-lg p-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                    children: \"\\uD83C\\uDF89 SynapseAI Dashboard - Now Working!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-8\",\n                                    children: \"You have successfully logged in. This is a working production-ready dashboard.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mt-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white p-6 rounded-lg shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold mb-2\",\n                                                    children: \"\\uD83E\\uDD16 AI Agents\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Create and manage intelligent AI agents\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 66,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                                        children: \"Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 68,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white p-6 rounded-lg shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold mb-2\",\n                                                    children: \"\\uD83D\\uDEE0️ Tools\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Manage AI tools and integrations\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                        children: \"Ready\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 78,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white p-6 rounded-lg shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold mb-2\",\n                                                    children: \"\\uD83D\\uDCCA Analytics\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Monitor performance and metrics\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800\",\n                                                        children: \"Tracking\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 88,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-8 p-4 bg-green-50 border border-green-200 rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-green-800 font-medium\",\n                                            children: \"✅ System Status: All services are operational\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-green-600 text-sm mt-1\",\n                                            children: \"Authentication working • Dashboard loaded • Ready for production\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/accessibility/A11yProvider.tsx":
/*!***************************************************!*\
  !*** ./components/accessibility/A11yProvider.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   A11yProvider: () => (/* binding */ A11yProvider),\n/* harmony export */   SkipLinks: () => (/* binding */ SkipLinks),\n/* harmony export */   useA11y: () => (/* binding */ useA11y),\n/* harmony export */   useAnnouncements: () => (/* binding */ useAnnouncements),\n/* harmony export */   useFocusManagement: () => (/* binding */ useFocusManagement),\n/* harmony export */   useKeyboardShortcuts: () => (/* binding */ useKeyboardShortcuts),\n/* harmony export */   withA11y: () => (/* binding */ withA11y)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_accessibility_a11y_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/accessibility/a11y-utils */ \"(ssr)/./lib/accessibility/a11y-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ useA11y,A11yProvider,withA11y,useKeyboardShortcuts,useFocusManagement,useAnnouncements,SkipLinks auto */ \n\n\nconst A11yContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useA11y() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(A11yContext);\n    if (context === undefined) {\n        throw new Error(\"useA11y must be used within an A11yProvider\");\n    }\n    return context;\n}\nfunction A11yProvider({ children, config: initialConfig }) {\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        ..._lib_accessibility_a11y_utils__WEBPACK_IMPORTED_MODULE_2__.defaultA11yConfig,\n        ...initialConfig\n    });\n    const [prefersReducedMotion, setPrefersReducedMotion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [highContrastMode, setHighContrastMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize accessibility features\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        (0,_lib_accessibility_a11y_utils__WEBPACK_IMPORTED_MODULE_2__.initializeA11y)(config);\n        // Check for reduced motion preference\n        const checkReducedMotion = ()=>{\n            setPrefersReducedMotion(_lib_accessibility_a11y_utils__WEBPACK_IMPORTED_MODULE_2__.motionPreferences.prefersReducedMotion());\n        };\n        checkReducedMotion();\n        // Listen for changes in motion preferences\n        const mediaQuery = window.matchMedia(\"(prefers-reduced-motion: reduce)\");\n        mediaQuery.addEventListener(\"change\", checkReducedMotion);\n        // Check for high contrast preference\n        const checkHighContrast = ()=>{\n            const highContrast = window.matchMedia(\"(prefers-contrast: high)\").matches || window.matchMedia(\"(-ms-high-contrast: active)\").matches;\n            setHighContrastMode(highContrast);\n        };\n        checkHighContrast();\n        // Listen for changes in contrast preferences\n        const contrastQuery = window.matchMedia(\"(prefers-contrast: high)\");\n        contrastQuery.addEventListener(\"change\", checkHighContrast);\n        return ()=>{\n            mediaQuery.removeEventListener(\"change\", checkReducedMotion);\n            contrastQuery.removeEventListener(\"change\", checkHighContrast);\n        };\n    }, [\n        config\n    ]);\n    // Apply high contrast mode\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (highContrastMode && config.enableHighContrast) {\n            document.documentElement.classList.add(\"high-contrast\");\n        } else {\n            document.documentElement.classList.remove(\"high-contrast\");\n        }\n    }, [\n        highContrastMode,\n        config.enableHighContrast\n    ]);\n    // Apply reduced motion\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (prefersReducedMotion && config.enableReducedMotion) {\n            document.documentElement.classList.add(\"reduce-motion\");\n        } else {\n            document.documentElement.classList.remove(\"reduce-motion\");\n        }\n    }, [\n        prefersReducedMotion,\n        config.enableReducedMotion\n    ]);\n    const updateConfig = (newConfig)=>{\n        setConfig((prev)=>({\n                ...prev,\n                ...newConfig\n            }));\n    };\n    const announce = (message, priority = \"polite\")=>{\n        if (config.announceStatusUpdates) {\n            _lib_accessibility_a11y_utils__WEBPACK_IMPORTED_MODULE_2__.screenReaderAnnouncer.announce(message, priority);\n        }\n    };\n    const announcePageChange = (pageName)=>{\n        if (config.announcePageChanges) {\n            _lib_accessibility_a11y_utils__WEBPACK_IMPORTED_MODULE_2__.screenReaderAnnouncer.announcePageChange(pageName);\n        }\n    };\n    const announceFormErrors = (errors)=>{\n        if (config.announceFormErrors) {\n            _lib_accessibility_a11y_utils__WEBPACK_IMPORTED_MODULE_2__.screenReaderAnnouncer.announceFormErrors(errors);\n        }\n    };\n    const announceStatus = (status, type = \"info\")=>{\n        if (config.announceStatusUpdates) {\n            _lib_accessibility_a11y_utils__WEBPACK_IMPORTED_MODULE_2__.screenReaderAnnouncer.announceStatus(status, type);\n        }\n    };\n    const setFocus = (element, announceText)=>{\n        if (config.enableFocusManagement) {\n            _lib_accessibility_a11y_utils__WEBPACK_IMPORTED_MODULE_2__.focusManager.setFocus(element, announceText);\n        }\n    };\n    const restoreFocus = ()=>{\n        if (config.enableFocusManagement) {\n            _lib_accessibility_a11y_utils__WEBPACK_IMPORTED_MODULE_2__.focusManager.restoreFocus();\n        }\n    };\n    const trapFocus = (container)=>{\n        if (config.enableFocusManagement) {\n            _lib_accessibility_a11y_utils__WEBPACK_IMPORTED_MODULE_2__.focusManager.trapFocus(container);\n        }\n    };\n    const releaseFocusTrap = ()=>{\n        if (config.enableFocusManagement) {\n            _lib_accessibility_a11y_utils__WEBPACK_IMPORTED_MODULE_2__.focusManager.releaseFocusTrap();\n        }\n    };\n    const toggleHighContrast = ()=>{\n        setHighContrastMode((prev)=>!prev);\n    };\n    const checkColorContrast = (color1, color2)=>{\n        return _lib_accessibility_a11y_utils__WEBPACK_IMPORTED_MODULE_2__.colorContrast.meetsWCAG(color1, color2, \"AA\");\n    };\n    const contextValue = {\n        config,\n        updateConfig,\n        announce,\n        announcePageChange,\n        announceFormErrors,\n        announceStatus,\n        setFocus,\n        restoreFocus,\n        trapFocus,\n        releaseFocusTrap,\n        prefersReducedMotion,\n        highContrastMode,\n        toggleHighContrast,\n        checkColorContrast\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(A11yContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\components\\\\accessibility\\\\A11yProvider.tsx\",\n        lineNumber: 189,\n        columnNumber: 5\n    }, this);\n}\n// Higher-order component for accessibility enhancements\nfunction withA11y(Component, options = {}) {\n    return function A11yEnhancedComponent(props) {\n        const { announce, setFocus, trapFocus, releaseFocusTrap } = useA11y();\n        const containerRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef(null);\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            if (options.announceOnMount) {\n                announce(options.announceOnMount);\n            }\n            if (options.focusOnMount && containerRef.current) {\n                setFocus(containerRef.current);\n            }\n            if (options.trapFocus && containerRef.current) {\n                trapFocus(containerRef.current);\n                return ()=>releaseFocusTrap();\n            }\n        }, [\n            announce,\n            setFocus,\n            trapFocus,\n            releaseFocusTrap\n        ]);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            ref: containerRef,\n            className: \"a11y-enhanced\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\components\\\\accessibility\\\\A11yProvider.tsx\",\n                lineNumber: 225,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\components\\\\accessibility\\\\A11yProvider.tsx\",\n            lineNumber: 224,\n            columnNumber: 7\n        }, this);\n    };\n}\n// Hook for keyboard shortcuts\nfunction useKeyboardShortcuts(shortcuts) {\n    const { config } = useA11y();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!config.enableKeyboardNavigation) return;\n        shortcuts.forEach(({ key, handler, ctrl, alt, shift, meta })=>{\n            _lib_accessibility_a11y_utils__WEBPACK_IMPORTED_MODULE_2__.keyboardNavigation.addShortcut(key, handler, {\n                ctrl,\n                alt,\n                shift,\n                meta\n            });\n        });\n        return ()=>{\n            shortcuts.forEach(({ key, ctrl, alt, shift, meta })=>{\n                _lib_accessibility_a11y_utils__WEBPACK_IMPORTED_MODULE_2__.keyboardNavigation.removeShortcut(key, {\n                    ctrl,\n                    alt,\n                    shift,\n                    meta\n                });\n            });\n        };\n    }, [\n        shortcuts,\n        config.enableKeyboardNavigation\n    ]);\n}\n// Hook for focus management\nfunction useFocusManagement() {\n    const { setFocus, restoreFocus, trapFocus, releaseFocusTrap } = useA11y();\n    const focusFirst = (container)=>{\n        const focusableElements = container.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])');\n        const firstElement = focusableElements[0];\n        if (firstElement) {\n            setFocus(firstElement);\n        }\n    };\n    const focusLast = (container)=>{\n        const focusableElements = container.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])');\n        const lastElement = focusableElements[focusableElements.length - 1];\n        if (lastElement) {\n            setFocus(lastElement);\n        }\n    };\n    return {\n        setFocus,\n        restoreFocus,\n        trapFocus,\n        releaseFocusTrap,\n        focusFirst,\n        focusLast\n    };\n}\n// Hook for announcements\nfunction useAnnouncements() {\n    const { announce, announcePageChange, announceFormErrors, announceStatus } = useA11y();\n    const announceLoading = (message = \"Loading...\")=>{\n        announce(message, \"polite\");\n    };\n    const announceError = (error)=>{\n        announce(`Error: ${error}`, \"assertive\");\n    };\n    const announceSuccess = (message)=>{\n        announce(`Success: ${message}`, \"polite\");\n    };\n    return {\n        announce,\n        announcePageChange,\n        announceFormErrors,\n        announceStatus,\n        announceLoading,\n        announceError,\n        announceSuccess\n    };\n}\n// Component for skip links\nfunction SkipLinks() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"skip-links\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                href: \"#main-content\",\n                className: \"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-primary-600 focus:text-white focus:rounded focus:shadow-lg\",\n                children: \"Skip to main content\"\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\components\\\\accessibility\\\\A11yProvider.tsx\",\n                lineNumber: 325,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                href: \"#navigation\",\n                className: \"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-32 focus:z-50 focus:px-4 focus:py-2 focus:bg-primary-600 focus:text-white focus:rounded focus:shadow-lg\",\n                children: \"Skip to navigation\"\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\components\\\\accessibility\\\\A11yProvider.tsx\",\n                lineNumber: 331,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\components\\\\accessibility\\\\A11yProvider.tsx\",\n        lineNumber: 324,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/accessibility/A11yProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/auth/AuthProvider.tsx":
/*!******************************************!*\
  !*** ./components/auth/AuthProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/../node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ AuthProvider auto */ \n\nfunction AuthProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\components\\\\auth\\\\AuthProvider.tsx\",\n        lineNumber: 11,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL2F1dGgvQXV0aFByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFa0Q7QUFPM0MsU0FBU0MsYUFBYSxFQUFFQyxRQUFRLEVBQXFCO0lBQzFELHFCQUFPLDhEQUFDRiw0REFBZUE7a0JBQUVFOzs7Ozs7QUFDM0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zeW5hcHNlYWktZnJvbnRlbmQvLi9jb21wb25lbnRzL2F1dGgvQXV0aFByb3ZpZGVyLnRzeD81ZDM3Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0JztcbmltcG9ydCB7IFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcblxuaW50ZXJmYWNlIEF1dGhQcm92aWRlclByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIEF1dGhQcm92aWRlcih7IGNoaWxkcmVuIH06IEF1dGhQcm92aWRlclByb3BzKSB7XG4gIHJldHVybiA8U2Vzc2lvblByb3ZpZGVyPntjaGlsZHJlbn08L1Nlc3Npb25Qcm92aWRlcj47XG59XG4iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiQXV0aFByb3ZpZGVyIiwiY2hpbGRyZW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/auth/AuthProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/organization/OrganizationProvider.tsx":
/*!**********************************************************!*\
  !*** ./components/organization/OrganizationProvider.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrganizationProvider: () => (/* binding */ OrganizationProvider),\n/* harmony export */   useOrganizationPermissions: () => (/* binding */ useOrganizationPermissions),\n/* harmony export */   useOrganizationSwitcher: () => (/* binding */ useOrganizationSwitcher)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/../node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_organization_organization_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/organization/organization-context */ \"(ssr)/./lib/organization/organization-context.ts\");\n/* harmony import */ var _components_accessibility_A11yProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/accessibility/A11yProvider */ \"(ssr)/./components/accessibility/A11yProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ OrganizationProvider,useOrganizationSwitcher,useOrganizationPermissions auto */ \n\n\n\n\nfunction OrganizationProvider({ children }) {\n    const { data: session, update: updateSession } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const { announceStatus } = (0,_components_accessibility_A11yProvider__WEBPACK_IMPORTED_MODULE_4__.useA11y)();\n    // State\n    const [organization, setOrganization] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [members, setMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [invites, setInvites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [usage, setUsage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [billing, setBilling] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isUpdating, setIsUpdating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Organization manager\n    const [orgManager] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _lib_organization_organization_context__WEBPACK_IMPORTED_MODULE_3__.OrganizationManager(\"http://localhost:3000\" || 0));\n    const user = session?.user;\n    const currentOrganizationId = user?.organizationId;\n    // Update access token when session changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (session?.accessToken) {\n            orgManager.setAccessToken(session.accessToken);\n        }\n    }, [\n        session?.accessToken,\n        orgManager\n    ]);\n    // Load organization data when organization ID changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentOrganizationId) {\n            loadOrganizationData();\n        } else {\n            // Clear data if no organization\n            setOrganization(null);\n            setMembers([]);\n            setInvites([]);\n            setUsage(null);\n            setBilling(null);\n        }\n    }, [\n        currentOrganizationId\n    ]);\n    const loadOrganizationData = async ()=>{\n        if (!currentOrganizationId) return;\n        setIsLoading(true);\n        setError(null);\n        try {\n            // Load organization details\n            const orgData = await orgManager.getOrganization(currentOrganizationId);\n            setOrganization(orgData);\n            // Load members and invites in parallel\n            const [membersData, invitesData, usageData, billingData] = await Promise.allSettled([\n                orgManager.getMembers(currentOrganizationId),\n                orgManager.getInvites(currentOrganizationId),\n                orgManager.getUsage(currentOrganizationId),\n                orgManager.getBilling(currentOrganizationId)\n            ]);\n            if (membersData.status === \"fulfilled\") {\n                setMembers(membersData.value);\n            }\n            if (invitesData.status === \"fulfilled\") {\n                setInvites(invitesData.value);\n            }\n            if (usageData.status === \"fulfilled\") {\n                setUsage(usageData.value);\n            }\n            if (billingData.status === \"fulfilled\") {\n                setBilling(billingData.value);\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Failed to load organization data\";\n            setError(errorMessage);\n            announceStatus(`Error: ${errorMessage}`, \"error\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const switchOrganization = async (organizationId)=>{\n        if (organizationId === currentOrganizationId) return;\n        setIsUpdating(true);\n        setError(null);\n        try {\n            const { accessToken, user: updatedUser } = await orgManager.switchOrganization(organizationId);\n            // Update session with new access token and user data\n            await updateSession({\n                accessToken,\n                user: updatedUser\n            });\n            announceStatus(\"Organization switched successfully\", \"success\");\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Failed to switch organization\";\n            setError(errorMessage);\n            announceStatus(`Error: ${errorMessage}`, \"error\");\n            throw error;\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    const updateOrganization = async (updates)=>{\n        if (!currentOrganizationId) throw new Error(\"No organization selected\");\n        setIsUpdating(true);\n        setError(null);\n        try {\n            const updatedOrg = await orgManager.updateOrganization(currentOrganizationId, updates);\n            setOrganization(updatedOrg);\n            announceStatus(\"Organization updated successfully\", \"success\");\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Failed to update organization\";\n            setError(errorMessage);\n            announceStatus(`Error: ${errorMessage}`, \"error\");\n            throw error;\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    const deleteOrganization = async ()=>{\n        if (!currentOrganizationId) throw new Error(\"No organization selected\");\n        setIsUpdating(true);\n        setError(null);\n        try {\n            await orgManager.deleteOrganization(currentOrganizationId);\n            // Clear organization data\n            setOrganization(null);\n            setMembers([]);\n            setInvites([]);\n            setUsage(null);\n            setBilling(null);\n            announceStatus(\"Organization deleted successfully\", \"success\");\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Failed to delete organization\";\n            setError(errorMessage);\n            announceStatus(`Error: ${errorMessage}`, \"error\");\n            throw error;\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    const addMember = async (email, role, permissions)=>{\n        if (!currentOrganizationId) throw new Error(\"No organization selected\");\n        setIsUpdating(true);\n        setError(null);\n        try {\n            const newMember = await orgManager.addMember(currentOrganizationId, {\n                email,\n                role,\n                permissions\n            });\n            setMembers((prev)=>[\n                    ...prev,\n                    newMember\n                ]);\n            announceStatus(\"Member added successfully\", \"success\");\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Failed to add member\";\n            setError(errorMessage);\n            announceStatus(`Error: ${errorMessage}`, \"error\");\n            throw error;\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    const updateMemberRole = async (memberId, role, permissions)=>{\n        if (!currentOrganizationId) throw new Error(\"No organization selected\");\n        setIsUpdating(true);\n        setError(null);\n        try {\n            const updatedMember = await orgManager.updateMember(currentOrganizationId, memberId, {\n                role,\n                permissions\n            });\n            setMembers((prev)=>prev.map((member)=>member.id === memberId ? updatedMember : member));\n            announceStatus(\"Member role updated successfully\", \"success\");\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Failed to update member role\";\n            setError(errorMessage);\n            announceStatus(`Error: ${errorMessage}`, \"error\");\n            throw error;\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    const removeMember = async (memberId)=>{\n        if (!currentOrganizationId) throw new Error(\"No organization selected\");\n        setIsUpdating(true);\n        setError(null);\n        try {\n            await orgManager.removeMember(currentOrganizationId, memberId);\n            setMembers((prev)=>prev.filter((member)=>member.id !== memberId));\n            announceStatus(\"Member removed successfully\", \"success\");\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Failed to remove member\";\n            setError(errorMessage);\n            announceStatus(`Error: ${errorMessage}`, \"error\");\n            throw error;\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    const inviteMember = async (email, role, permissions)=>{\n        if (!currentOrganizationId) throw new Error(\"No organization selected\");\n        setIsUpdating(true);\n        setError(null);\n        try {\n            const newInvite = await orgManager.inviteMember(currentOrganizationId, {\n                email,\n                role,\n                permissions\n            });\n            setInvites((prev)=>[\n                    ...prev,\n                    newInvite\n                ]);\n            announceStatus(\"Invitation sent successfully\", \"success\");\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Failed to send invitation\";\n            setError(errorMessage);\n            announceStatus(`Error: ${errorMessage}`, \"error\");\n            throw error;\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    const revokeInvite = async (inviteId)=>{\n        if (!currentOrganizationId) throw new Error(\"No organization selected\");\n        setIsUpdating(true);\n        setError(null);\n        try {\n            await orgManager.revokeInvite(currentOrganizationId, inviteId);\n            setInvites((prev)=>prev.filter((invite)=>invite.id !== inviteId));\n            announceStatus(\"Invitation revoked successfully\", \"success\");\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Failed to revoke invitation\";\n            setError(errorMessage);\n            announceStatus(`Error: ${errorMessage}`, \"error\");\n            throw error;\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    const resendInvite = async (inviteId)=>{\n        if (!currentOrganizationId) throw new Error(\"No organization selected\");\n        setIsUpdating(true);\n        setError(null);\n        try {\n            const updatedInvite = await orgManager.resendInvite(currentOrganizationId, inviteId);\n            setInvites((prev)=>prev.map((invite)=>invite.id === inviteId ? updatedInvite : invite));\n            announceStatus(\"Invitation resent successfully\", \"success\");\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Failed to resend invitation\";\n            setError(errorMessage);\n            announceStatus(`Error: ${errorMessage}`, \"error\");\n            throw error;\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    const updateBilling = async (planId)=>{\n        if (!currentOrganizationId) throw new Error(\"No organization selected\");\n        setIsUpdating(true);\n        setError(null);\n        try {\n            const updatedBilling = await orgManager.updateBilling(currentOrganizationId, {\n                planId\n            });\n            setBilling(updatedBilling);\n            announceStatus(\"Billing updated successfully\", \"success\");\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Failed to update billing\";\n            setError(errorMessage);\n            announceStatus(`Error: ${errorMessage}`, \"error\");\n            throw error;\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Permission helpers\n    const hasPermission = (permission)=>{\n        if (!user) return false;\n        return user.permissions?.includes(permission) || false;\n    };\n    const hasRole = (role)=>{\n        if (!user) return false;\n        return user.organizationRole === role;\n    };\n    const canManageMembers = ()=>{\n        return hasPermission(\"members:invite\") || hasPermission(\"members:update\") || hasPermission(\"members:remove\");\n    };\n    const canManageBilling = ()=>{\n        return hasPermission(\"billing:update\") || hasRole(\"owner\");\n    };\n    const canManageSettings = ()=>{\n        return hasPermission(\"organization:update\") || hasRole(\"owner\") || hasRole(\"admin\");\n    };\n    const clearError = ()=>{\n        setError(null);\n    };\n    const contextValue = {\n        organization,\n        switchOrganization,\n        updateOrganization,\n        deleteOrganization,\n        members,\n        invites,\n        addMember,\n        updateMemberRole,\n        removeMember,\n        inviteMember,\n        revokeInvite,\n        resendInvite,\n        usage,\n        billing,\n        updateBilling,\n        hasPermission,\n        hasRole,\n        canManageMembers,\n        canManageBilling,\n        canManageSettings,\n        isLoading,\n        isUpdating,\n        error,\n        clearError\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_organization_organization_context__WEBPACK_IMPORTED_MODULE_3__.OrganizationContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\components\\\\organization\\\\OrganizationProvider.tsx\",\n        lineNumber: 396,\n        columnNumber: 5\n    }, this);\n}\n// Hook for organization switching\nfunction useOrganizationSwitcher() {\n    const { switchOrganization, isUpdating } = useOrganization();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const availableOrganizations = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo(()=>{\n        // This would typically come from the user's session or a separate API call\n        // For now, we'll return an empty array\n        return [];\n    }, [\n        session\n    ]);\n    return {\n        availableOrganizations,\n        switchOrganization,\n        isUpdating\n    };\n}\n// Hook for organization permissions\nfunction useOrganizationPermissions() {\n    const { hasPermission, hasRole, canManageMembers, canManageBilling, canManageSettings } = useOrganization();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const user = session?.user;\n    return {\n        user,\n        hasPermission,\n        hasRole,\n        canManageMembers,\n        canManageBilling,\n        canManageSettings,\n        isOwner: hasRole(\"owner\"),\n        isAdmin: hasRole(\"admin\") || hasRole(\"owner\"),\n        isMember: hasRole(\"member\")\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/organization/OrganizationProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/providers/QueryProvider.tsx":
/*!************************************************!*\
  !*** ./components/providers/QueryProvider.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryProvider: () => (/* binding */ QueryProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(ssr)/../node_modules/@tanstack/react-query-devtools/build/modern/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ QueryProvider auto */ \n\n\n\nfunction QueryProvider({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.QueryClient({\n            defaultOptions: {\n                queries: {\n                    staleTime: 60 * 1000,\n                    gcTime: 10 * 60 * 1000,\n                    retry: (failureCount, error)=>{\n                        // Don't retry on 4xx errors except 408, 429\n                        if (error?.status >= 400 && error?.status < 500 && ![\n                            408,\n                            429\n                        ].includes(error.status)) {\n                            return false;\n                        }\n                        // Retry up to 3 times for other errors\n                        return failureCount < 3;\n                    },\n                    retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000)\n                },\n                mutations: {\n                    retry: (failureCount, error)=>{\n                        // Don't retry mutations on 4xx errors\n                        if (error?.status >= 400 && error?.status < 500) {\n                            return false;\n                        }\n                        return failureCount < 2;\n                    }\n                }\n            }\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClientProvider, {\n        client: queryClient,\n        children: [\n            children,\n             true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_4__.ReactQueryDevtools, {\n                initialIsOpen: false\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\components\\\\providers\\\\QueryProvider.tsx\",\n                lineNumber: 46,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\components\\\\providers\\\\QueryProvider.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/providers/QueryProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/providers/ThemeProvider.tsx":
/*!************************************************!*\
  !*** ./components/providers/ThemeProvider.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_theme_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/theme/theme-provider */ \"(ssr)/./lib/theme/theme-provider.ts\");\n/* harmony import */ var _lib_theme_theme_system__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/theme/theme-system */ \"(ssr)/./lib/theme/theme-system.ts\");\n/* __next_internal_client_entry_do_not_use__ useTheme,ThemeProvider auto */ \n\n\n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n}\nfunction ThemeProvider({ children, config, enableTransitions = true, transitionDuration = 200 }) {\n    const [themeManager] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const mergedConfig = {\n            ..._lib_theme_theme_system__WEBPACK_IMPORTED_MODULE_3__.defaultThemeConfig,\n            ...config,\n            enableTransitions,\n            transitionDuration\n        };\n        return (0,_lib_theme_theme_provider__WEBPACK_IMPORTED_MODULE_2__.getThemeManager)(mergedConfig);\n    });\n    const [contextValue, setContextValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(themeManager.getContextValue());\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Subscribe to theme changes\n        const unsubscribe = themeManager.subscribe(setContextValue);\n        // Initial theme application\n        setContextValue(themeManager.getContextValue());\n        return unsubscribe;\n    }, [\n        themeManager\n    ]);\n    // Add transition styles to document head\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!enableTransitions) return;\n        const styleId = \"theme-transition-styles\";\n        let styleElement = document.getElementById(styleId);\n        if (!styleElement) {\n            styleElement = document.createElement(\"style\");\n            styleElement.id = styleId;\n            document.head.appendChild(styleElement);\n        }\n        styleElement.textContent = `\n      :root {\n        --theme-transition-duration: ${transitionDuration}ms;\n        --theme-transition-timing: cubic-bezier(0.4, 0, 0.2, 1);\n      }\n\n      .theme-transitioning,\n      .theme-transitioning *,\n      .theme-transitioning *:before,\n      .theme-transitioning *:after {\n        transition:\n          background-color var(--theme-transition-duration) var(--theme-transition-timing),\n          border-color var(--theme-transition-duration) var(--theme-transition-timing),\n          color var(--theme-transition-duration) var(--theme-transition-timing),\n          fill var(--theme-transition-duration) var(--theme-transition-timing),\n          stroke var(--theme-transition-duration) var(--theme-transition-timing),\n          box-shadow var(--theme-transition-duration) var(--theme-transition-timing) !important;\n      }\n\n      .theme-transitioning {\n        pointer-events: none;\n      }\n\n      /* Prevent transition on page load */\n      .preload * {\n        transition: none !important;\n      }\n    `;\n        return ()=>{\n            const element = document.getElementById(styleId);\n            if (element) {\n                element.remove();\n            }\n        };\n    }, [\n        enableTransitions,\n        transitionDuration\n    ]);\n    // Prevent transitions on initial load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const root = document.documentElement;\n        root.classList.add(\"preload\");\n        const timer = setTimeout(()=>{\n            root.classList.remove(\"preload\");\n        }, 100);\n        return ()=>clearTimeout(timer);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\components\\\\providers\\\\ThemeProvider.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/providers/ThemeProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/providers/ToastProvider.tsx":
/*!************************************************!*\
  !*** ./components/providers/ToastProvider.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/../node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider auto */ \n\nfunction ToastProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n                position: \"top-right\",\n                gutter: 8,\n                containerClassName: \"\",\n                containerStyle: {},\n                toastOptions: {\n                    // Default options for all toasts\n                    duration: 4000,\n                    style: {\n                        background: \"#363636\",\n                        color: \"#fff\",\n                        fontSize: \"14px\",\n                        fontWeight: \"500\",\n                        padding: \"12px 16px\",\n                        borderRadius: \"8px\",\n                        boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.15)\"\n                    },\n                    // Success toast styling\n                    success: {\n                        duration: 3000,\n                        style: {\n                            background: \"#10b981\",\n                            color: \"#fff\"\n                        },\n                        iconTheme: {\n                            primary: \"#fff\",\n                            secondary: \"#10b981\"\n                        }\n                    },\n                    // Error toast styling\n                    error: {\n                        duration: 5000,\n                        style: {\n                            background: \"#ef4444\",\n                            color: \"#fff\"\n                        },\n                        iconTheme: {\n                            primary: \"#fff\",\n                            secondary: \"#ef4444\"\n                        }\n                    },\n                    // Loading toast styling\n                    loading: {\n                        style: {\n                            background: \"#3b82f6\",\n                            color: \"#fff\"\n                        },\n                        iconTheme: {\n                            primary: \"#fff\",\n                            secondary: \"#3b82f6\"\n                        }\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\components\\\\providers\\\\ToastProvider.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/providers/ToastProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/accessibility/a11y-utils.ts":
/*!*****************************************!*\
  !*** ./lib/accessibility/a11y-utils.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ColorContrast: () => (/* binding */ ColorContrast),\n/* harmony export */   FocusManager: () => (/* binding */ FocusManager),\n/* harmony export */   KeyboardNavigation: () => (/* binding */ KeyboardNavigation),\n/* harmony export */   MotionPreferences: () => (/* binding */ MotionPreferences),\n/* harmony export */   ScreenReaderAnnouncer: () => (/* binding */ ScreenReaderAnnouncer),\n/* harmony export */   colorContrast: () => (/* binding */ colorContrast),\n/* harmony export */   defaultA11yConfig: () => (/* binding */ defaultA11yConfig),\n/* harmony export */   focusManager: () => (/* binding */ focusManager),\n/* harmony export */   initializeA11y: () => (/* binding */ initializeA11y),\n/* harmony export */   keyboardNavigation: () => (/* binding */ keyboardNavigation),\n/* harmony export */   motionPreferences: () => (/* binding */ motionPreferences),\n/* harmony export */   screenReaderAnnouncer: () => (/* binding */ screenReaderAnnouncer)\n/* harmony export */ });\n/**\n * Accessibility utilities for WCAG 2.1 AA compliance\n */ const defaultA11yConfig = {\n    announcePageChanges: true,\n    announceFormErrors: true,\n    announceStatusUpdates: true,\n    enableKeyboardNavigation: true,\n    enableFocusManagement: true,\n    enableHighContrast: true,\n    enableReducedMotion: true,\n    minimumTouchTarget: 44,\n    minimumColorContrast: 4.5\n};\n/**\n * Screen reader announcements\n */ class ScreenReaderAnnouncer {\n    constructor(){\n        this.liveRegion = null;\n        this.politeRegion = null;\n        if (false) {}\n    }\n    /**\n   * Create ARIA live regions for announcements\n   */ createLiveRegions() {\n        // Assertive live region for urgent announcements\n        this.liveRegion = document.createElement(\"div\");\n        this.liveRegion.setAttribute(\"aria-live\", \"assertive\");\n        this.liveRegion.setAttribute(\"aria-atomic\", \"true\");\n        this.liveRegion.setAttribute(\"class\", \"sr-only\");\n        this.liveRegion.style.cssText = `\n      position: absolute !important;\n      width: 1px !important;\n      height: 1px !important;\n      padding: 0 !important;\n      margin: -1px !important;\n      overflow: hidden !important;\n      clip: rect(0, 0, 0, 0) !important;\n      white-space: nowrap !important;\n      border: 0 !important;\n    `;\n        document.body.appendChild(this.liveRegion);\n        // Polite live region for non-urgent announcements\n        this.politeRegion = document.createElement(\"div\");\n        this.politeRegion.setAttribute(\"aria-live\", \"polite\");\n        this.politeRegion.setAttribute(\"aria-atomic\", \"true\");\n        this.politeRegion.setAttribute(\"class\", \"sr-only\");\n        this.politeRegion.style.cssText = this.liveRegion.style.cssText;\n        document.body.appendChild(this.politeRegion);\n    }\n    /**\n   * Announce message to screen readers\n   */ announce(message, priority = \"polite\") {\n        if (!message.trim()) return;\n        const region = priority === \"assertive\" ? this.liveRegion : this.politeRegion;\n        if (!region) return;\n        // Clear previous message\n        region.textContent = \"\";\n        // Add new message after a brief delay to ensure it's announced\n        setTimeout(()=>{\n            region.textContent = message;\n        }, 100);\n        // Clear message after announcement\n        setTimeout(()=>{\n            region.textContent = \"\";\n        }, 1000);\n    }\n    /**\n   * Announce page change\n   */ announcePageChange(pageName) {\n        this.announce(`Navigated to ${pageName}`, \"polite\");\n    }\n    /**\n   * Announce form errors\n   */ announceFormErrors(errors) {\n        if (errors.length === 0) return;\n        const message = errors.length === 1 ? `Form error: ${errors[0]}` : `Form has ${errors.length} errors: ${errors.join(\", \")}`;\n        this.announce(message, \"assertive\");\n    }\n    /**\n   * Announce status updates\n   */ announceStatus(status, type = \"info\") {\n        const prefix = {\n            success: \"Success:\",\n            error: \"Error:\",\n            warning: \"Warning:\",\n            info: \"Info:\"\n        }[type];\n        this.announce(`${prefix} ${status}`, type === \"error\" ? \"assertive\" : \"polite\");\n    }\n    /**\n   * Cleanup\n   */ destroy() {\n        if (this.liveRegion) {\n            document.body.removeChild(this.liveRegion);\n            this.liveRegion = null;\n        }\n        if (this.politeRegion) {\n            document.body.removeChild(this.politeRegion);\n            this.politeRegion = null;\n        }\n    }\n}\n/**\n * Focus management utilities\n */ class FocusManager {\n    /**\n   * Set focus to element with optional announcement\n   */ setFocus(element, announce) {\n        const target = typeof element === \"string\" ? document.querySelector(element) : element;\n        if (!target) return;\n        // Store previous focus\n        const activeElement = document.activeElement;\n        if (activeElement && activeElement !== target) {\n            this.focusStack.push(activeElement);\n        }\n        // Set focus\n        target.focus();\n        // Announce if provided\n        if (announce) {\n            screenReaderAnnouncer.announce(announce);\n        }\n    }\n    /**\n   * Restore previous focus\n   */ restoreFocus() {\n        const previousElement = this.focusStack.pop();\n        if (previousElement && document.contains(previousElement)) {\n            previousElement.focus();\n        }\n    }\n    /**\n   * Trap focus within container\n   */ trapFocus(container) {\n        this.trapStack.push(container);\n        this.setupFocusTrap(container);\n    }\n    /**\n   * Release focus trap\n   */ releaseFocusTrap() {\n        const container = this.trapStack.pop();\n        if (container) {\n            this.removeFocusTrap(container);\n        }\n    }\n    /**\n   * Setup focus trap for container\n   */ setupFocusTrap(container) {\n        const focusableElements = this.getFocusableElements(container);\n        if (focusableElements.length === 0) return;\n        const firstElement = focusableElements[0];\n        const lastElement = focusableElements[focusableElements.length - 1];\n        const handleKeyDown = (event)=>{\n            if (event.key !== \"Tab\") return;\n            if (event.shiftKey) {\n                // Shift + Tab\n                if (document.activeElement === firstElement) {\n                    event.preventDefault();\n                    lastElement?.focus();\n                }\n            } else {\n                // Tab\n                if (document.activeElement === lastElement) {\n                    event.preventDefault();\n                    firstElement?.focus();\n                }\n            }\n        };\n        container.addEventListener(\"keydown\", handleKeyDown);\n        container.setAttribute(\"data-focus-trap\", \"true\");\n        // Set initial focus\n        firstElement?.focus();\n    }\n    /**\n   * Remove focus trap from container\n   */ removeFocusTrap(container) {\n        const handleKeyDown = container.getAttribute(\"data-focus-trap-handler\");\n        if (handleKeyDown) {\n            container.removeEventListener(\"keydown\", handleKeyDown);\n        }\n        container.removeAttribute(\"data-focus-trap\");\n        container.removeAttribute(\"data-focus-trap-handler\");\n    }\n    /**\n   * Get focusable elements within container\n   */ getFocusableElements(container) {\n        const focusableSelectors = [\n            \"a[href]\",\n            \"button:not([disabled])\",\n            \"input:not([disabled])\",\n            \"select:not([disabled])\",\n            \"textarea:not([disabled])\",\n            '[tabindex]:not([tabindex=\"-1\"])',\n            '[contenteditable=\"true\"]'\n        ].join(\", \");\n        return Array.from(container.querySelectorAll(focusableSelectors)).filter((element)=>{\n            const el = element;\n            return el.offsetWidth > 0 && el.offsetHeight > 0 && !el.hidden;\n        });\n    }\n    /**\n   * Check if element is focusable\n   */ isFocusable(element) {\n        return this.getFocusableElements(document.body).includes(element);\n    }\n    constructor(){\n        this.focusStack = [];\n        this.trapStack = [];\n    }\n}\n/**\n * Keyboard navigation utilities\n */ class KeyboardNavigation {\n    /**\n   * Add keyboard shortcut\n   */ addShortcut(key, handler, options = {}) {\n        const shortcutKey = this.createShortcutKey(key, options);\n        this.keyHandlers.set(shortcutKey, handler);\n    }\n    /**\n   * Remove keyboard shortcut\n   */ removeShortcut(key, options = {}) {\n        const shortcutKey = this.createShortcutKey(key, options);\n        this.keyHandlers.delete(shortcutKey);\n    }\n    /**\n   * Handle keyboard events\n   */ handleKeyDown(event) {\n        if (!event.key) return;\n        const shortcutKey = this.createShortcutKey(event.key, {\n            ctrl: event.ctrlKey,\n            alt: event.altKey,\n            shift: event.shiftKey,\n            meta: event.metaKey\n        });\n        const handler = this.keyHandlers.get(shortcutKey);\n        if (handler) {\n            event.preventDefault();\n            handler(event);\n        }\n    }\n    /**\n   * Create shortcut key string\n   */ createShortcutKey(key, options) {\n        if (!key) return \"\";\n        const modifiers = [];\n        if (options.ctrl) modifiers.push(\"ctrl\");\n        if (options.alt) modifiers.push(\"alt\");\n        if (options.shift) modifiers.push(\"shift\");\n        if (options.meta) modifiers.push(\"meta\");\n        return [\n            ...modifiers,\n            key.toLowerCase()\n        ].join(\"+\");\n    }\n    /**\n   * Setup arrow key navigation for container\n   */ setupArrowNavigation(container, direction = \"vertical\") {\n        const handleKeyDown = (event)=>{\n            const focusableElements = Array.from(container.querySelectorAll('[tabindex]:not([tabindex=\"-1\"]), button, input, select, textarea, a[href]'));\n            const currentIndex = focusableElements.indexOf(document.activeElement);\n            if (currentIndex === -1) return;\n            let nextIndex = currentIndex;\n            switch(direction){\n                case \"horizontal\":\n                    if (event.key === \"ArrowLeft\") nextIndex = Math.max(0, currentIndex - 1);\n                    if (event.key === \"ArrowRight\") nextIndex = Math.min(focusableElements.length - 1, currentIndex + 1);\n                    break;\n                case \"vertical\":\n                    if (event.key === \"ArrowUp\") nextIndex = Math.max(0, currentIndex - 1);\n                    if (event.key === \"ArrowDown\") nextIndex = Math.min(focusableElements.length - 1, currentIndex + 1);\n                    break;\n                case \"grid\":\n                    break;\n            }\n            if (nextIndex !== currentIndex) {\n                event.preventDefault();\n                focusableElements[nextIndex]?.focus();\n            }\n        };\n        container.addEventListener(\"keydown\", handleKeyDown);\n    }\n    constructor(){\n        this.keyHandlers = new Map();\n    }\n}\n/**\n * Color contrast utilities\n */ class ColorContrast {\n    /**\n   * Calculate relative luminance\n   */ getRelativeLuminance(color) {\n        const rgb = this.hexToRgb(color);\n        if (!rgb) return 0;\n        const [r, g, b] = rgb.map((c)=>{\n            c = c / 255;\n            return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);\n        });\n        return 0.2126 * r + 0.7152 * g + 0.0722 * b;\n    }\n    /**\n   * Convert hex color to RGB\n   */ hexToRgb(hex) {\n        const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\n        return result ? [\n            parseInt(result[1], 16),\n            parseInt(result[2], 16),\n            parseInt(result[3], 16)\n        ] : null;\n    }\n    /**\n   * Calculate contrast ratio between two colors\n   */ getContrastRatio(color1, color2) {\n        const lum1 = this.getRelativeLuminance(color1);\n        const lum2 = this.getRelativeLuminance(color2);\n        const lighter = Math.max(lum1, lum2);\n        const darker = Math.min(lum1, lum2);\n        return (lighter + 0.05) / (darker + 0.05);\n    }\n    /**\n   * Check if contrast ratio meets WCAG standards\n   */ meetsWCAG(color1, color2, level = \"AA\", size = \"normal\") {\n        const ratio = this.getContrastRatio(color1, color2);\n        const requirements = {\n            AA: {\n                normal: 4.5,\n                large: 3\n            },\n            AAA: {\n                normal: 7,\n                large: 4.5\n            }\n        };\n        return ratio >= requirements[level][size];\n    }\n    /**\n   * Suggest accessible color alternatives\n   */ suggestAccessibleColor(foreground, background, level = \"AA\") {\n        const targetRatio = level === \"AA\" ? 4.5 : 7;\n        const currentRatio = this.getContrastRatio(foreground, background);\n        if (currentRatio >= targetRatio) return foreground;\n        // Simple approach: darken or lighten the foreground color\n        const rgb = this.hexToRgb(foreground);\n        if (!rgb) return foreground;\n        let [r, g, b] = rgb;\n        const step = currentRatio < targetRatio ? -10 : 10;\n        while(this.getContrastRatio(this.rgbToHex(r, g, b), background) < targetRatio){\n            r = Math.max(0, Math.min(255, r + step));\n            g = Math.max(0, Math.min(255, g + step));\n            b = Math.max(0, Math.min(255, b + step));\n            if (r === 0 && g === 0 && b === 0 || r === 255 && g === 255 && b === 255) {\n                break;\n            }\n        }\n        return this.rgbToHex(r, g, b);\n    }\n    /**\n   * Convert RGB to hex\n   */ rgbToHex(r, g, b) {\n        return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;\n    }\n}\n/**\n * Motion preferences utilities\n */ class MotionPreferences {\n    /**\n   * Check if user prefers reduced motion\n   */ prefersReducedMotion() {\n        if (true) return false;\n        return window.matchMedia(\"(prefers-reduced-motion: reduce)\").matches;\n    }\n    /**\n   * Apply motion preferences to element\n   */ applyMotionPreferences(element) {\n        if (this.prefersReducedMotion()) {\n            element.style.animation = \"none\";\n            element.style.transition = \"none\";\n        }\n    }\n    /**\n   * Get safe animation duration\n   */ getSafeAnimationDuration(defaultDuration) {\n        return this.prefersReducedMotion() ? 0 : defaultDuration;\n    }\n}\n// Global instances\nconst screenReaderAnnouncer = new ScreenReaderAnnouncer();\nconst focusManager = new FocusManager();\nconst keyboardNavigation = new KeyboardNavigation();\nconst colorContrast = new ColorContrast();\nconst motionPreferences = new MotionPreferences();\n/**\n * Initialize accessibility features\n */ function initializeA11y(config = {}) {\n    const mergedConfig = {\n        ...defaultA11yConfig,\n        ...config\n    };\n    if (true) return;\n    // Setup keyboard navigation\n    if (mergedConfig.enableKeyboardNavigation) {\n        document.addEventListener(\"keydown\", (event)=>{\n            keyboardNavigation.handleKeyDown(event);\n        });\n    }\n    // Setup skip links\n    setupSkipLinks();\n    // Setup focus indicators\n    setupFocusIndicators();\n    // Setup reduced motion\n    if (mergedConfig.enableReducedMotion) {\n        setupReducedMotion();\n    }\n}\n/**\n * Setup skip links for keyboard navigation\n */ function setupSkipLinks() {\n    const skipLink = document.createElement(\"a\");\n    skipLink.href = \"#main-content\";\n    skipLink.textContent = \"Skip to main content\";\n    skipLink.className = \"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-primary-600 focus:text-white focus:rounded\";\n    document.body.insertBefore(skipLink, document.body.firstChild);\n}\n/**\n * Setup focus indicators\n */ function setupFocusIndicators() {\n    const style = document.createElement(\"style\");\n    style.textContent = `\n    .focus-visible {\n      outline: 2px solid #3b82f6;\n      outline-offset: 2px;\n    }\n    \n    .focus-visible:not(.focus-visible-force) {\n      outline: none;\n    }\n    \n    .focus-visible-force {\n      outline: 2px solid #3b82f6 !important;\n      outline-offset: 2px !important;\n    }\n  `;\n    document.head.appendChild(style);\n}\n/**\n * Setup reduced motion preferences\n */ function setupReducedMotion() {\n    if (motionPreferences.prefersReducedMotion()) {\n        const style = document.createElement(\"style\");\n        style.textContent = `\n      *, *::before, *::after {\n        animation-duration: 0.01ms !important;\n        animation-iteration-count: 1 !important;\n        transition-duration: 0.01ms !important;\n        scroll-behavior: auto !important;\n      }\n    `;\n        document.head.appendChild(style);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/accessibility/a11y-utils.ts\n");

/***/ }),

/***/ "(ssr)/./lib/organization/organization-context.ts":
/*!**************************************************!*\
  !*** ./lib/organization/organization-context.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ORGANIZATION_PERMISSIONS: () => (/* binding */ ORGANIZATION_PERMISSIONS),\n/* harmony export */   ORGANIZATION_ROLES: () => (/* binding */ ORGANIZATION_ROLES),\n/* harmony export */   OrganizationContext: () => (/* binding */ OrganizationContext),\n/* harmony export */   OrganizationManager: () => (/* binding */ OrganizationManager),\n/* harmony export */   useOrganization: () => (/* binding */ useOrganization)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst OrganizationContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);\nfunction useOrganization() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(OrganizationContext);\n    if (context === undefined) {\n        throw new Error(\"useOrganization must be used within an OrganizationProvider\");\n    }\n    return context;\n}\n/**\n * Organization management class\n */ class OrganizationManager {\n    constructor(apiUrl){\n        this.accessToken = null;\n        this.apiUrl = apiUrl;\n    }\n    setAccessToken(token) {\n        this.accessToken = token;\n    }\n    async request(endpoint, options = {}) {\n        const url = `${this.apiUrl}${endpoint}`;\n        const headers = {\n            \"Content-Type\": \"application/json\",\n            ...this.accessToken && {\n                Authorization: `Bearer ${this.accessToken}`\n            },\n            ...options.headers\n        };\n        const response = await fetch(url, {\n            ...options,\n            headers\n        });\n        if (!response.ok) {\n            const error = await response.json().catch(()=>({\n                    message: \"Request failed\"\n                }));\n            throw new Error(error.message || `HTTP ${response.status}`);\n        }\n        return response.json();\n    }\n    // Organization operations\n    async getOrganization(organizationId) {\n        return this.request(`/api/v1/organizations/${organizationId}`);\n    }\n    async updateOrganization(organizationId, updates) {\n        return this.request(`/api/v1/organizations/${organizationId}`, {\n            method: \"PATCH\",\n            body: JSON.stringify(updates)\n        });\n    }\n    async deleteOrganization(organizationId) {\n        await this.request(`/api/v1/organizations/${organizationId}`, {\n            method: \"DELETE\"\n        });\n    }\n    async switchOrganization(organizationId) {\n        return this.request(\"/api/v1/auth/switch-organization\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                organizationId\n            })\n        });\n    }\n    // Members operations\n    async getMembers(organizationId) {\n        return this.request(`/api/v1/organizations/${organizationId}/members`);\n    }\n    async addMember(organizationId, data) {\n        return this.request(`/api/v1/organizations/${organizationId}/members`, {\n            method: \"POST\",\n            body: JSON.stringify(data)\n        });\n    }\n    async updateMember(organizationId, memberId, updates) {\n        return this.request(`/api/v1/organizations/${organizationId}/members/${memberId}`, {\n            method: \"PATCH\",\n            body: JSON.stringify(updates)\n        });\n    }\n    async removeMember(organizationId, memberId) {\n        await this.request(`/api/v1/organizations/${organizationId}/members/${memberId}`, {\n            method: \"DELETE\"\n        });\n    }\n    // Invites operations\n    async getInvites(organizationId) {\n        return this.request(`/api/v1/organizations/${organizationId}/invites`);\n    }\n    async inviteMember(organizationId, data) {\n        return this.request(`/api/v1/organizations/${organizationId}/invites`, {\n            method: \"POST\",\n            body: JSON.stringify(data)\n        });\n    }\n    async revokeInvite(organizationId, inviteId) {\n        await this.request(`/api/v1/organizations/${organizationId}/invites/${inviteId}`, {\n            method: \"DELETE\"\n        });\n    }\n    async resendInvite(organizationId, inviteId) {\n        return this.request(`/api/v1/organizations/${organizationId}/invites/${inviteId}/resend`, {\n            method: \"POST\"\n        });\n    }\n    // Usage operations\n    async getUsage(organizationId) {\n        return this.request(`/api/v1/organizations/${organizationId}/usage`);\n    }\n    // Billing operations\n    async getBilling(organizationId) {\n        return this.request(`/api/v1/organizations/${organizationId}/billing`);\n    }\n    async updateBilling(organizationId, data) {\n        return this.request(`/api/v1/organizations/${organizationId}/billing`, {\n            method: \"PATCH\",\n            body: JSON.stringify(data)\n        });\n    }\n    async createCheckoutSession(organizationId, data) {\n        return this.request(`/api/v1/organizations/${organizationId}/billing/checkout`, {\n            method: \"POST\",\n            body: JSON.stringify(data)\n        });\n    }\n    async createPortalSession(organizationId, data) {\n        return this.request(`/api/v1/organizations/${organizationId}/billing/portal`, {\n            method: \"POST\",\n            body: JSON.stringify(data)\n        });\n    }\n}\n/**\n * Permission utilities\n */ const ORGANIZATION_PERMISSIONS = {\n    // Organization management\n    \"organization:read\": \"View organization details\",\n    \"organization:update\": \"Update organization settings\",\n    \"organization:delete\": \"Delete organization\",\n    // Member management\n    \"members:read\": \"View organization members\",\n    \"members:invite\": \"Invite new members\",\n    \"members:update\": \"Update member roles and permissions\",\n    \"members:remove\": \"Remove members\",\n    // Billing management\n    \"billing:read\": \"View billing information\",\n    \"billing:update\": \"Update billing and subscription\",\n    // Agent management\n    \"agents:create\": \"Create new agents\",\n    \"agents:read\": \"View agents\",\n    \"agents:update\": \"Update agents\",\n    \"agents:delete\": \"Delete agents\",\n    \"agents:deploy\": \"Deploy agents\",\n    // Workflow management\n    \"workflows:create\": \"Create new workflows\",\n    \"workflows:read\": \"View workflows\",\n    \"workflows:update\": \"Update workflows\",\n    \"workflows:delete\": \"Delete workflows\",\n    \"workflows:execute\": \"Execute workflows\",\n    // Tool management\n    \"tools:create\": \"Create new tools\",\n    \"tools:read\": \"View tools\",\n    \"tools:update\": \"Update tools\",\n    \"tools:delete\": \"Delete tools\",\n    // Analytics\n    \"analytics:read\": \"View analytics and reports\"\n};\nconst ORGANIZATION_ROLES = {\n    owner: {\n        name: \"Owner\",\n        description: \"Full access to all organization features\",\n        permissions: Object.keys(ORGANIZATION_PERMISSIONS)\n    },\n    admin: {\n        name: \"Admin\",\n        description: \"Manage organization, members, and all resources\",\n        permissions: [\n            \"organization:read\",\n            \"organization:update\",\n            \"members:read\",\n            \"members:invite\",\n            \"members:update\",\n            \"members:remove\",\n            \"billing:read\",\n            \"agents:create\",\n            \"agents:read\",\n            \"agents:update\",\n            \"agents:delete\",\n            \"agents:deploy\",\n            \"workflows:create\",\n            \"workflows:read\",\n            \"workflows:update\",\n            \"workflows:delete\",\n            \"workflows:execute\",\n            \"tools:create\",\n            \"tools:read\",\n            \"tools:update\",\n            \"tools:delete\",\n            \"analytics:read\"\n        ]\n    },\n    member: {\n        name: \"Member\",\n        description: \"Create and manage own resources\",\n        permissions: [\n            \"organization:read\",\n            \"members:read\",\n            \"agents:create\",\n            \"agents:read\",\n            \"agents:update\",\n            \"agents:delete\",\n            \"agents:deploy\",\n            \"workflows:create\",\n            \"workflows:read\",\n            \"workflows:update\",\n            \"workflows:delete\",\n            \"workflows:execute\",\n            \"tools:create\",\n            \"tools:read\",\n            \"tools:update\",\n            \"tools:delete\"\n        ]\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/organization/organization-context.ts\n");

/***/ }),

/***/ "(ssr)/./lib/theme/theme-provider.ts":
/*!*************************************!*\
  !*** ./lib/theme/theme-provider.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeContext: () => (/* binding */ ThemeContext),\n/* harmony export */   ThemeManager: () => (/* binding */ ThemeManager),\n/* harmony export */   destroyThemeManager: () => (/* binding */ destroyThemeManager),\n/* harmony export */   getThemeManager: () => (/* binding */ getThemeManager),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _theme_system__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./theme-system */ \"(ssr)/./lib/theme/theme-system.ts\");\n\n\nconst ThemeContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n}\nclass ThemeManager {\n    constructor(config = _theme_system__WEBPACK_IMPORTED_MODULE_1__.defaultThemeConfig){\n        this.listeners = new Set();\n        this.isTransitioning = false;\n        this.mediaQuery = null;\n        this.config = config;\n        this.currentMode = this.getStoredMode() || config.defaultTheme;\n        this.resolvedMode = this.resolveMode(this.currentMode);\n        if (false) {}\n    }\n    /**\n   * Get current theme context value\n   */ getContextValue() {\n        return {\n            theme: this.getCurrentTheme(),\n            mode: this.currentMode,\n            resolvedMode: this.resolvedMode,\n            setMode: this.setMode.bind(this),\n            toggleMode: this.toggleMode.bind(this),\n            config: this.config,\n            isTransitioning: this.isTransitioning\n        };\n    }\n    /**\n   * Set theme mode\n   */ setMode(mode) {\n        if (this.currentMode === mode) return;\n        this.currentMode = mode;\n        const newResolvedMode = this.resolveMode(mode);\n        if (newResolvedMode !== this.resolvedMode) {\n            this.resolvedMode = newResolvedMode;\n            this.applyTheme();\n        }\n        this.storeMode(mode);\n        this.notifyListeners();\n    }\n    /**\n   * Toggle between light and dark modes\n   */ toggleMode() {\n        if (this.currentMode === \"system\") {\n            // If system mode, toggle to opposite of current resolved mode\n            this.setMode(this.resolvedMode === \"light\" ? \"dark\" : \"light\");\n        } else {\n            // Toggle between light and dark\n            this.setMode(this.currentMode === \"light\" ? \"dark\" : \"light\");\n        }\n    }\n    /**\n   * Subscribe to theme changes\n   */ subscribe(listener) {\n        this.listeners.add(listener);\n        return ()=>{\n            this.listeners.delete(listener);\n        };\n    }\n    /**\n   * Update theme configuration\n   */ updateConfig(newConfig) {\n        this.config = {\n            ...this.config,\n            ...newConfig\n        };\n        this.applyTheme();\n        this.notifyListeners();\n    }\n    /**\n   * Get current theme object\n   */ getCurrentTheme() {\n        return this.config.themes[this.resolvedMode];\n    }\n    /**\n   * Apply theme to DOM\n   */ applyTheme() {\n        if (true) return;\n        const theme = this.getCurrentTheme();\n        const root = document.documentElement;\n        // Start transition if enabled\n        if (this.config.enableTransitions) {\n            this.startTransition();\n        }\n        // Apply theme mode class\n        root.classList.remove(\"light\", \"dark\");\n        root.classList.add(this.resolvedMode);\n        // Apply CSS custom properties\n        this.applyCSSVariables(theme);\n        // Apply meta theme-color for mobile browsers\n        this.applyMetaThemeColor(theme);\n        // End transition after duration\n        if (this.config.enableTransitions) {\n            setTimeout(()=>{\n                this.endTransition();\n            }, this.config.transitionDuration);\n        }\n    }\n    /**\n   * Apply CSS custom properties\n   */ applyCSSVariables(theme) {\n        const root = document.documentElement;\n        const { colors, spacing, typography, shadows, radii, transitions } = theme;\n        // Apply color variables\n        Object.entries(colors).forEach(([category, categoryColors])=>{\n            if (typeof categoryColors === \"object\" && categoryColors !== null) {\n                Object.entries(categoryColors).forEach(([shade, value])=>{\n                    root.style.setProperty(`--color-${category}-${shade}`, value);\n                });\n            }\n        });\n        // Apply spacing variables\n        Object.entries(spacing).forEach(([key, value])=>{\n            root.style.setProperty(`--spacing-${key}`, value);\n        });\n        // Apply typography variables\n        Object.entries(typography.fontSize).forEach(([key, [size, { lineHeight }]])=>{\n            root.style.setProperty(`--font-size-${key}`, size);\n            root.style.setProperty(`--line-height-${key}`, lineHeight);\n        });\n        Object.entries(typography.fontWeight).forEach(([key, value])=>{\n            root.style.setProperty(`--font-weight-${key}`, value);\n        });\n        Object.entries(typography.letterSpacing).forEach(([key, value])=>{\n            root.style.setProperty(`--letter-spacing-${key}`, value);\n        });\n        // Apply shadow variables\n        Object.entries(shadows).forEach(([key, value])=>{\n            root.style.setProperty(`--shadow-${key}`, value);\n        });\n        // Apply radius variables\n        Object.entries(radii).forEach(([key, value])=>{\n            root.style.setProperty(`--radius-${key}`, value);\n        });\n        // Apply transition variables\n        Object.entries(transitions.duration).forEach(([key, value])=>{\n            root.style.setProperty(`--duration-${key}`, value);\n        });\n        Object.entries(transitions.timing).forEach(([key, value])=>{\n            root.style.setProperty(`--timing-${key}`, value);\n        });\n    }\n    /**\n   * Apply meta theme color for mobile browsers\n   */ applyMetaThemeColor(theme) {\n        let metaThemeColor = document.querySelector('meta[name=\"theme-color\"]');\n        if (!metaThemeColor) {\n            metaThemeColor = document.createElement(\"meta\");\n            metaThemeColor.setAttribute(\"name\", \"theme-color\");\n            document.head.appendChild(metaThemeColor);\n        }\n        metaThemeColor.setAttribute(\"content\", theme.colors.background.primary);\n    }\n    /**\n   * Setup media query listener for system theme\n   */ setupMediaQuery() {\n        if (!this.config.enableSystemTheme) return;\n        this.mediaQuery = window.matchMedia(\"(prefers-color-scheme: dark)\");\n        this.mediaQuery.addEventListener(\"change\", this.handleMediaQueryChange.bind(this));\n    }\n    /**\n   * Handle system theme change\n   */ handleMediaQueryChange() {\n        if (this.currentMode === \"system\") {\n            const newResolvedMode = this.resolveMode(\"system\");\n            if (newResolvedMode !== this.resolvedMode) {\n                this.resolvedMode = newResolvedMode;\n                this.applyTheme();\n                this.notifyListeners();\n            }\n        }\n    }\n    /**\n   * Resolve mode to actual theme\n   */ resolveMode(mode) {\n        if (mode === \"system\") {\n            if (false) {}\n            return \"light\"; // Default fallback\n        }\n        return mode;\n    }\n    /**\n   * Get stored theme mode\n   */ getStoredMode() {\n        if (true) return null;\n        try {\n            const stored = localStorage.getItem(this.config.storageKey);\n            if (stored && [\n                \"light\",\n                \"dark\",\n                \"system\"\n            ].includes(stored)) {\n                return stored;\n            }\n        } catch (error) {\n            console.warn(\"Failed to read theme from localStorage:\", error);\n        }\n        return null;\n    }\n    /**\n   * Store theme mode\n   */ storeMode(mode) {\n        if (true) return;\n        try {\n            localStorage.setItem(this.config.storageKey, mode);\n        } catch (error) {\n            console.warn(\"Failed to store theme in localStorage:\", error);\n        }\n    }\n    /**\n   * Start theme transition\n   */ startTransition() {\n        this.isTransitioning = true;\n        document.documentElement.classList.add(\"theme-transitioning\");\n        this.notifyListeners();\n    }\n    /**\n   * End theme transition\n   */ endTransition() {\n        this.isTransitioning = false;\n        document.documentElement.classList.remove(\"theme-transitioning\");\n        this.notifyListeners();\n    }\n    /**\n   * Notify all listeners of theme change\n   */ notifyListeners() {\n        const contextValue = this.getContextValue();\n        this.listeners.forEach((listener)=>listener(contextValue));\n    }\n    /**\n   * Cleanup\n   */ destroy() {\n        if (this.mediaQuery) {\n            this.mediaQuery.removeEventListener(\"change\", this.handleMediaQueryChange.bind(this));\n        }\n        this.listeners.clear();\n    }\n}\n// Global theme manager instance\nlet globalThemeManager = null;\nfunction getThemeManager(config) {\n    if (!globalThemeManager) {\n        globalThemeManager = new ThemeManager(config);\n    }\n    return globalThemeManager;\n}\nfunction destroyThemeManager() {\n    if (globalThemeManager) {\n        globalThemeManager.destroy();\n        globalThemeManager = null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/theme/theme-provider.ts\n");

/***/ }),

/***/ "(ssr)/./lib/theme/theme-system.ts":
/*!***********************************!*\
  !*** ./lib/theme/theme-system.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultThemeConfig: () => (/* binding */ defaultThemeConfig)\n/* harmony export */ });\n// Default theme configuration\nconst defaultThemeConfig = {\n    themes: {\n        light: {\n            name: \"SynapseAI Light\",\n            mode: \"light\",\n            colors: {\n                primary: {\n                    50: \"#eff6ff\",\n                    100: \"#dbeafe\",\n                    200: \"#bfdbfe\",\n                    300: \"#93c5fd\",\n                    400: \"#60a5fa\",\n                    500: \"#3b82f6\",\n                    600: \"#2563eb\",\n                    700: \"#1d4ed8\",\n                    800: \"#1e40af\",\n                    900: \"#1e3a8a\",\n                    950: \"#172554\"\n                },\n                success: {\n                    50: \"#f0fdf4\",\n                    100: \"#dcfce7\",\n                    200: \"#bbf7d0\",\n                    300: \"#86efac\",\n                    400: \"#4ade80\",\n                    500: \"#22c55e\",\n                    600: \"#16a34a\",\n                    700: \"#15803d\",\n                    800: \"#166534\",\n                    900: \"#14532d\",\n                    950: \"#052e16\"\n                },\n                warning: {\n                    50: \"#fffbeb\",\n                    100: \"#fef3c7\",\n                    200: \"#fde68a\",\n                    300: \"#fcd34d\",\n                    400: \"#fbbf24\",\n                    500: \"#f59e0b\",\n                    600: \"#d97706\",\n                    700: \"#b45309\",\n                    800: \"#92400e\",\n                    900: \"#78350f\",\n                    950: \"#451a03\"\n                },\n                error: {\n                    50: \"#fef2f2\",\n                    100: \"#fee2e2\",\n                    200: \"#fecaca\",\n                    300: \"#fca5a5\",\n                    400: \"#f87171\",\n                    500: \"#ef4444\",\n                    600: \"#dc2626\",\n                    700: \"#b91c1c\",\n                    800: \"#991b1b\",\n                    900: \"#7f1d1d\",\n                    950: \"#450a0a\"\n                },\n                info: {\n                    50: \"#f0f9ff\",\n                    100: \"#e0f2fe\",\n                    200: \"#bae6fd\",\n                    300: \"#7dd3fc\",\n                    400: \"#38bdf8\",\n                    500: \"#0ea5e9\",\n                    600: \"#0284c7\",\n                    700: \"#0369a1\",\n                    800: \"#075985\",\n                    900: \"#0c4a6e\",\n                    950: \"#082f49\"\n                },\n                gray: {\n                    50: \"#f9fafb\",\n                    100: \"#f3f4f6\",\n                    200: \"#e5e7eb\",\n                    300: \"#d1d5db\",\n                    400: \"#9ca3af\",\n                    500: \"#6b7280\",\n                    600: \"#4b5563\",\n                    700: \"#374151\",\n                    800: \"#1f2937\",\n                    900: \"#111827\",\n                    950: \"#030712\"\n                },\n                background: {\n                    primary: \"#ffffff\",\n                    secondary: \"#f9fafb\",\n                    tertiary: \"#f3f4f6\",\n                    inverse: \"#111827\"\n                },\n                surface: {\n                    primary: \"#ffffff\",\n                    secondary: \"#f9fafb\",\n                    tertiary: \"#f3f4f6\",\n                    inverse: \"#111827\",\n                    overlay: \"rgba(0, 0, 0, 0.5)\"\n                },\n                text: {\n                    primary: \"#111827\",\n                    secondary: \"#374151\",\n                    tertiary: \"#6b7280\",\n                    inverse: \"#ffffff\",\n                    disabled: \"#9ca3af\"\n                },\n                border: {\n                    primary: \"#e5e7eb\",\n                    secondary: \"#d1d5db\",\n                    tertiary: \"#9ca3af\",\n                    inverse: \"#374151\",\n                    focus: \"#3b82f6\"\n                }\n            },\n            spacing: {\n                0: \"0px\",\n                px: \"1px\",\n                0.5: \"0.125rem\",\n                1: \"0.25rem\",\n                1.5: \"0.375rem\",\n                2: \"0.5rem\",\n                2.5: \"0.625rem\",\n                3: \"0.75rem\",\n                3.5: \"0.875rem\",\n                4: \"1rem\",\n                5: \"1.25rem\",\n                6: \"1.5rem\",\n                7: \"1.75rem\",\n                8: \"2rem\",\n                9: \"2.25rem\",\n                10: \"2.5rem\",\n                11: \"2.75rem\",\n                12: \"3rem\",\n                14: \"3.5rem\",\n                16: \"4rem\",\n                20: \"5rem\",\n                24: \"6rem\",\n                28: \"7rem\",\n                32: \"8rem\",\n                36: \"9rem\",\n                40: \"10rem\",\n                44: \"11rem\",\n                48: \"12rem\",\n                52: \"13rem\",\n                56: \"14rem\",\n                60: \"15rem\",\n                64: \"16rem\",\n                72: \"18rem\",\n                80: \"20rem\",\n                96: \"24rem\"\n            },\n            typography: {\n                fontFamily: {\n                    sans: [\n                        \"Inter\",\n                        \"ui-sans-serif\",\n                        \"system-ui\",\n                        \"sans-serif\"\n                    ],\n                    serif: [\n                        \"ui-serif\",\n                        \"Georgia\",\n                        \"Cambria\",\n                        \"serif\"\n                    ],\n                    mono: [\n                        \"ui-monospace\",\n                        \"SFMono-Regular\",\n                        \"Menlo\",\n                        \"Monaco\",\n                        \"Consolas\",\n                        \"monospace\"\n                    ]\n                },\n                fontSize: {\n                    xs: [\n                        \"0.75rem\",\n                        {\n                            lineHeight: \"1rem\"\n                        }\n                    ],\n                    sm: [\n                        \"0.875rem\",\n                        {\n                            lineHeight: \"1.25rem\"\n                        }\n                    ],\n                    base: [\n                        \"1rem\",\n                        {\n                            lineHeight: \"1.5rem\"\n                        }\n                    ],\n                    lg: [\n                        \"1.125rem\",\n                        {\n                            lineHeight: \"1.75rem\"\n                        }\n                    ],\n                    xl: [\n                        \"1.25rem\",\n                        {\n                            lineHeight: \"1.75rem\"\n                        }\n                    ],\n                    \"2xl\": [\n                        \"1.5rem\",\n                        {\n                            lineHeight: \"2rem\"\n                        }\n                    ],\n                    \"3xl\": [\n                        \"1.875rem\",\n                        {\n                            lineHeight: \"2.25rem\"\n                        }\n                    ],\n                    \"4xl\": [\n                        \"2.25rem\",\n                        {\n                            lineHeight: \"2.5rem\"\n                        }\n                    ],\n                    \"5xl\": [\n                        \"3rem\",\n                        {\n                            lineHeight: \"1\"\n                        }\n                    ],\n                    \"6xl\": [\n                        \"3.75rem\",\n                        {\n                            lineHeight: \"1\"\n                        }\n                    ],\n                    \"7xl\": [\n                        \"4.5rem\",\n                        {\n                            lineHeight: \"1\"\n                        }\n                    ],\n                    \"8xl\": [\n                        \"6rem\",\n                        {\n                            lineHeight: \"1\"\n                        }\n                    ],\n                    \"9xl\": [\n                        \"8rem\",\n                        {\n                            lineHeight: \"1\"\n                        }\n                    ]\n                },\n                fontWeight: {\n                    thin: \"100\",\n                    extralight: \"200\",\n                    light: \"300\",\n                    normal: \"400\",\n                    medium: \"500\",\n                    semibold: \"600\",\n                    bold: \"700\",\n                    extrabold: \"800\",\n                    black: \"900\"\n                },\n                lineHeight: {\n                    none: \"1\",\n                    tight: \"1.25\",\n                    snug: \"1.375\",\n                    normal: \"1.5\",\n                    relaxed: \"1.625\",\n                    loose: \"2\"\n                },\n                letterSpacing: {\n                    tighter: \"-0.05em\",\n                    tight: \"-0.025em\",\n                    normal: \"0em\",\n                    wide: \"0.025em\",\n                    wider: \"0.05em\",\n                    widest: \"0.1em\"\n                }\n            },\n            breakpoints: {\n                sm: \"640px\",\n                md: \"768px\",\n                lg: \"1024px\",\n                xl: \"1280px\",\n                \"2xl\": \"1536px\"\n            },\n            shadows: {\n                sm: \"0 1px 2px 0 rgb(0 0 0 / 0.05)\",\n                base: \"0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)\",\n                md: \"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)\",\n                lg: \"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)\",\n                xl: \"0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)\",\n                \"2xl\": \"0 25px 50px -12px rgb(0 0 0 / 0.25)\",\n                inner: \"inset 0 2px 4px 0 rgb(0 0 0 / 0.05)\",\n                none: \"0 0 #0000\"\n            },\n            radii: {\n                none: \"0px\",\n                sm: \"0.125rem\",\n                base: \"0.25rem\",\n                md: \"0.375rem\",\n                lg: \"0.5rem\",\n                xl: \"0.75rem\",\n                \"2xl\": \"1rem\",\n                \"3xl\": \"1.5rem\",\n                full: \"9999px\"\n            },\n            transitions: {\n                duration: {\n                    75: \"75ms\",\n                    100: \"100ms\",\n                    150: \"150ms\",\n                    200: \"200ms\",\n                    300: \"300ms\",\n                    500: \"500ms\",\n                    700: \"700ms\",\n                    1000: \"1000ms\"\n                },\n                timing: {\n                    linear: \"linear\",\n                    in: \"cubic-bezier(0.4, 0, 1, 1)\",\n                    out: \"cubic-bezier(0, 0, 0.2, 1)\",\n                    \"in-out\": \"cubic-bezier(0.4, 0, 0.2, 1)\"\n                },\n                property: {\n                    none: \"none\",\n                    all: \"all\",\n                    default: \"color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter\",\n                    colors: \"color, background-color, border-color, text-decoration-color, fill, stroke\",\n                    opacity: \"opacity\",\n                    shadow: \"box-shadow\",\n                    transform: \"transform\"\n                }\n            }\n        },\n        dark: {\n            name: \"SynapseAI Dark\",\n            mode: \"dark\",\n            colors: {\n                primary: {\n                    50: \"#eff6ff\",\n                    100: \"#dbeafe\",\n                    200: \"#bfdbfe\",\n                    300: \"#93c5fd\",\n                    400: \"#60a5fa\",\n                    500: \"#3b82f6\",\n                    600: \"#2563eb\",\n                    700: \"#1d4ed8\",\n                    800: \"#1e40af\",\n                    900: \"#1e3a8a\",\n                    950: \"#172554\"\n                },\n                success: {\n                    50: \"#f0fdf4\",\n                    100: \"#dcfce7\",\n                    200: \"#bbf7d0\",\n                    300: \"#86efac\",\n                    400: \"#4ade80\",\n                    500: \"#22c55e\",\n                    600: \"#16a34a\",\n                    700: \"#15803d\",\n                    800: \"#166534\",\n                    900: \"#14532d\",\n                    950: \"#052e16\"\n                },\n                warning: {\n                    50: \"#fffbeb\",\n                    100: \"#fef3c7\",\n                    200: \"#fde68a\",\n                    300: \"#fcd34d\",\n                    400: \"#fbbf24\",\n                    500: \"#f59e0b\",\n                    600: \"#d97706\",\n                    700: \"#b45309\",\n                    800: \"#92400e\",\n                    900: \"#78350f\",\n                    950: \"#451a03\"\n                },\n                error: {\n                    50: \"#fef2f2\",\n                    100: \"#fee2e2\",\n                    200: \"#fecaca\",\n                    300: \"#fca5a5\",\n                    400: \"#f87171\",\n                    500: \"#ef4444\",\n                    600: \"#dc2626\",\n                    700: \"#b91c1c\",\n                    800: \"#991b1b\",\n                    900: \"#7f1d1d\",\n                    950: \"#450a0a\"\n                },\n                info: {\n                    50: \"#f0f9ff\",\n                    100: \"#e0f2fe\",\n                    200: \"#bae6fd\",\n                    300: \"#7dd3fc\",\n                    400: \"#38bdf8\",\n                    500: \"#0ea5e9\",\n                    600: \"#0284c7\",\n                    700: \"#0369a1\",\n                    800: \"#075985\",\n                    900: \"#0c4a6e\",\n                    950: \"#082f49\"\n                },\n                gray: {\n                    50: \"#f9fafb\",\n                    100: \"#f3f4f6\",\n                    200: \"#e5e7eb\",\n                    300: \"#d1d5db\",\n                    400: \"#9ca3af\",\n                    500: \"#6b7280\",\n                    600: \"#4b5563\",\n                    700: \"#374151\",\n                    800: \"#1f2937\",\n                    900: \"#111827\",\n                    950: \"#030712\"\n                },\n                background: {\n                    primary: \"#030712\",\n                    secondary: \"#111827\",\n                    tertiary: \"#1f2937\",\n                    inverse: \"#ffffff\"\n                },\n                surface: {\n                    primary: \"#111827\",\n                    secondary: \"#1f2937\",\n                    tertiary: \"#374151\",\n                    inverse: \"#ffffff\",\n                    overlay: \"rgba(0, 0, 0, 0.8)\"\n                },\n                text: {\n                    primary: \"#f9fafb\",\n                    secondary: \"#e5e7eb\",\n                    tertiary: \"#9ca3af\",\n                    inverse: \"#111827\",\n                    disabled: \"#6b7280\"\n                },\n                border: {\n                    primary: \"#374151\",\n                    secondary: \"#4b5563\",\n                    tertiary: \"#6b7280\",\n                    inverse: \"#e5e7eb\",\n                    focus: \"#3b82f6\"\n                }\n            },\n            spacing: {\n                0: \"0px\",\n                px: \"1px\",\n                0.5: \"0.125rem\",\n                1: \"0.25rem\",\n                1.5: \"0.375rem\",\n                2: \"0.5rem\",\n                2.5: \"0.625rem\",\n                3: \"0.75rem\",\n                3.5: \"0.875rem\",\n                4: \"1rem\",\n                5: \"1.25rem\",\n                6: \"1.5rem\",\n                7: \"1.75rem\",\n                8: \"2rem\",\n                9: \"2.25rem\",\n                10: \"2.5rem\",\n                11: \"2.75rem\",\n                12: \"3rem\",\n                14: \"3.5rem\",\n                16: \"4rem\",\n                20: \"5rem\",\n                24: \"6rem\",\n                28: \"7rem\",\n                32: \"8rem\",\n                36: \"9rem\",\n                40: \"10rem\",\n                44: \"11rem\",\n                48: \"12rem\",\n                52: \"13rem\",\n                56: \"14rem\",\n                60: \"15rem\",\n                64: \"16rem\",\n                72: \"18rem\",\n                80: \"20rem\",\n                96: \"24rem\"\n            },\n            typography: {\n                fontFamily: {\n                    sans: [\n                        \"Inter\",\n                        \"ui-sans-serif\",\n                        \"system-ui\",\n                        \"sans-serif\"\n                    ],\n                    serif: [\n                        \"ui-serif\",\n                        \"Georgia\",\n                        \"Cambria\",\n                        \"serif\"\n                    ],\n                    mono: [\n                        \"ui-monospace\",\n                        \"SFMono-Regular\",\n                        \"Menlo\",\n                        \"Monaco\",\n                        \"Consolas\",\n                        \"monospace\"\n                    ]\n                },\n                fontSize: {\n                    xs: [\n                        \"0.75rem\",\n                        {\n                            lineHeight: \"1rem\"\n                        }\n                    ],\n                    sm: [\n                        \"0.875rem\",\n                        {\n                            lineHeight: \"1.25rem\"\n                        }\n                    ],\n                    base: [\n                        \"1rem\",\n                        {\n                            lineHeight: \"1.5rem\"\n                        }\n                    ],\n                    lg: [\n                        \"1.125rem\",\n                        {\n                            lineHeight: \"1.75rem\"\n                        }\n                    ],\n                    xl: [\n                        \"1.25rem\",\n                        {\n                            lineHeight: \"1.75rem\"\n                        }\n                    ],\n                    \"2xl\": [\n                        \"1.5rem\",\n                        {\n                            lineHeight: \"2rem\"\n                        }\n                    ],\n                    \"3xl\": [\n                        \"1.875rem\",\n                        {\n                            lineHeight: \"2.25rem\"\n                        }\n                    ],\n                    \"4xl\": [\n                        \"2.25rem\",\n                        {\n                            lineHeight: \"2.5rem\"\n                        }\n                    ],\n                    \"5xl\": [\n                        \"3rem\",\n                        {\n                            lineHeight: \"1\"\n                        }\n                    ],\n                    \"6xl\": [\n                        \"3.75rem\",\n                        {\n                            lineHeight: \"1\"\n                        }\n                    ],\n                    \"7xl\": [\n                        \"4.5rem\",\n                        {\n                            lineHeight: \"1\"\n                        }\n                    ],\n                    \"8xl\": [\n                        \"6rem\",\n                        {\n                            lineHeight: \"1\"\n                        }\n                    ],\n                    \"9xl\": [\n                        \"8rem\",\n                        {\n                            lineHeight: \"1\"\n                        }\n                    ]\n                },\n                fontWeight: {\n                    thin: \"100\",\n                    extralight: \"200\",\n                    light: \"300\",\n                    normal: \"400\",\n                    medium: \"500\",\n                    semibold: \"600\",\n                    bold: \"700\",\n                    extrabold: \"800\",\n                    black: \"900\"\n                },\n                lineHeight: {\n                    none: \"1\",\n                    tight: \"1.25\",\n                    snug: \"1.375\",\n                    normal: \"1.5\",\n                    relaxed: \"1.625\",\n                    loose: \"2\"\n                },\n                letterSpacing: {\n                    tighter: \"-0.05em\",\n                    tight: \"-0.025em\",\n                    normal: \"0em\",\n                    wide: \"0.025em\",\n                    wider: \"0.05em\",\n                    widest: \"0.1em\"\n                }\n            },\n            breakpoints: {\n                sm: \"640px\",\n                md: \"768px\",\n                lg: \"1024px\",\n                xl: \"1280px\",\n                \"2xl\": \"1536px\"\n            },\n            shadows: {\n                sm: \"0 1px 2px 0 rgb(0 0 0 / 0.05)\",\n                base: \"0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)\",\n                md: \"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)\",\n                lg: \"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)\",\n                xl: \"0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)\",\n                \"2xl\": \"0 25px 50px -12px rgb(0 0 0 / 0.25)\",\n                inner: \"inset 0 2px 4px 0 rgb(0 0 0 / 0.05)\",\n                none: \"0 0 #0000\"\n            },\n            radii: {\n                none: \"0px\",\n                sm: \"0.125rem\",\n                base: \"0.25rem\",\n                md: \"0.375rem\",\n                lg: \"0.5rem\",\n                xl: \"0.75rem\",\n                \"2xl\": \"1rem\",\n                \"3xl\": \"1.5rem\",\n                full: \"9999px\"\n            },\n            transitions: {\n                duration: {\n                    75: \"75ms\",\n                    100: \"100ms\",\n                    150: \"150ms\",\n                    200: \"200ms\",\n                    300: \"300ms\",\n                    500: \"500ms\",\n                    700: \"700ms\",\n                    1000: \"1000ms\"\n                },\n                timing: {\n                    linear: \"linear\",\n                    in: \"cubic-bezier(0.4, 0, 1, 1)\",\n                    out: \"cubic-bezier(0, 0, 0.2, 1)\",\n                    \"in-out\": \"cubic-bezier(0.4, 0, 0.2, 1)\"\n                },\n                property: {\n                    none: \"none\",\n                    all: \"all\",\n                    default: \"color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter\",\n                    colors: \"color, background-color, border-color, text-decoration-color, fill, stroke\",\n                    opacity: \"opacity\",\n                    shadow: \"box-shadow\",\n                    transform: \"transform\"\n                }\n            }\n        }\n    },\n    defaultTheme: \"system\",\n    enableSystemTheme: true,\n    enableTransitions: true,\n    transitionDuration: 200,\n    storageKey: \"synapseai-theme\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/theme/theme-system.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1b8a84273663\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zeW5hcHNlYWktZnJvbnRlbmQvLi9hcHAvZ2xvYmFscy5jc3M/NGIyYSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjFiOGE4NDI3MzY2M1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\app\dashboard\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_auth_AuthProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/auth/AuthProvider */ \"(rsc)/./components/auth/AuthProvider.tsx\");\n/* harmony import */ var _components_providers_QueryProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/providers/QueryProvider */ \"(rsc)/./components/providers/QueryProvider.tsx\");\n/* harmony import */ var _components_providers_ToastProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/providers/ToastProvider */ \"(rsc)/./components/providers/ToastProvider.tsx\");\n/* harmony import */ var _components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/providers/ThemeProvider */ \"(rsc)/./components/providers/ThemeProvider.tsx\");\n/* harmony import */ var _components_accessibility_A11yProvider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/accessibility/A11yProvider */ \"(rsc)/./components/accessibility/A11yProvider.tsx\");\n/* harmony import */ var _components_organization_OrganizationProvider__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/organization/OrganizationProvider */ \"(rsc)/./components/organization/OrganizationProvider.tsx\");\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: {\n        default: \"SynapseAI - Universal AI Orchestration Platform\",\n        template: \"%s | SynapseAI\"\n    },\n    description: \"Build, deploy, and manage AI agents, tools, and workflows with SynapseAI's no-code platform.\",\n    keywords: [\n        \"AI\",\n        \"artificial intelligence\",\n        \"automation\",\n        \"no-code\",\n        \"agents\",\n        \"workflows\",\n        \"orchestration\",\n        \"SynapseAI\"\n    ],\n    authors: [\n        {\n            name: \"SynapseAI Team\"\n        }\n    ],\n    creator: \"SynapseAI\",\n    publisher: \"SynapseAI\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(\"http://localhost:3001\" || 0),\n    alternates: {\n        canonical: \"/\"\n    },\n    openGraph: {\n        type: \"website\",\n        locale: \"en_US\",\n        url: \"/\",\n        title: \"SynapseAI - Universal AI Orchestration Platform\",\n        description: \"Build, deploy, and manage AI agents, tools, and workflows with SynapseAI's no-code platform.\",\n        siteName: \"SynapseAI\",\n        images: [\n            {\n                url: \"/og-image.png\",\n                width: 1200,\n                height: 630,\n                alt: \"SynapseAI Platform\"\n            }\n        ]\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"SynapseAI - Universal AI Orchestration Platform\",\n        description: \"Build, deploy, and manage AI agents, tools, and workflows with SynapseAI's no-code platform.\",\n        images: [\n            \"/og-image.png\"\n        ],\n        creator: \"@synapseai\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: process.env.GOOGLE_SITE_VERIFICATION || undefined\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_8___default().variable),\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\",\n                        sizes: \"any\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.svg\",\n                        type: \"image/svg+xml\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#0ea5e9\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1, maximum-scale=5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"min-h-screen bg-background font-sans antialiased\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_5__.ThemeProvider, {\n                    enableTransitions: true,\n                    transitionDuration: 200,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_accessibility_A11yProvider__WEBPACK_IMPORTED_MODULE_6__.A11yProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_AuthProvider__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_organization_OrganizationProvider__WEBPACK_IMPORTED_MODULE_7__.OrganizationProvider, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_QueryProvider__WEBPACK_IMPORTED_MODULE_3__.QueryProvider, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_ToastProvider__WEBPACK_IMPORTED_MODULE_4__.ToastProvider, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex min-h-screen flex-col\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: children\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\layout.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\layout.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\layout.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\layout.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\layout.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\layout.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\layout.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\kilo-teset\\\\augsyanapseAI\\\\frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 90,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQVVNQTtBQVJpQjtBQUN1QztBQUNPO0FBQ0E7QUFDQTtBQUNFO0FBQ2U7QUFRL0UsTUFBTU8sV0FBcUI7SUFDaENDLE9BQU87UUFDTEMsU0FBUztRQUNUQyxVQUFVO0lBQ1o7SUFDQUMsYUFBYTtJQUNiQyxVQUFVO1FBQ1I7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtLQUNEO0lBQ0RDLFNBQVM7UUFBQztZQUFFQyxNQUFNO1FBQWlCO0tBQUU7SUFDckNDLFNBQVM7SUFDVEMsV0FBVztJQUNYQyxpQkFBaUI7UUFDZkMsT0FBTztRQUNQQyxTQUFTO1FBQ1RDLFdBQVc7SUFDYjtJQUNBQyxjQUFjLElBQUlDLElBQUlDLHVCQUErQixJQUFJO0lBQ3pERyxZQUFZO1FBQ1ZDLFdBQVc7SUFDYjtJQUNBQyxXQUFXO1FBQ1RDLE1BQU07UUFDTkMsUUFBUTtRQUNSQyxLQUFLO1FBQ0x2QixPQUFPO1FBQ1BHLGFBQWE7UUFDYnFCLFVBQVU7UUFDVkMsUUFBUTtZQUNOO2dCQUNFRixLQUFLO2dCQUNMRyxPQUFPO2dCQUNQQyxRQUFRO2dCQUNSQyxLQUFLO1lBQ1A7U0FDRDtJQUNIO0lBQ0FDLFNBQVM7UUFDUEMsTUFBTTtRQUNOOUIsT0FBTztRQUNQRyxhQUFhO1FBQ2JzQixRQUFRO1lBQUM7U0FBZ0I7UUFDekJsQixTQUFTO0lBQ1g7SUFDQXdCLFFBQVE7UUFDTkMsT0FBTztRQUNQQyxRQUFRO1FBQ1JDLFdBQVc7WUFDVEYsT0FBTztZQUNQQyxRQUFRO1lBQ1IscUJBQXFCLENBQUM7WUFDdEIscUJBQXFCO1lBQ3JCLGVBQWUsQ0FBQztRQUNsQjtJQUNGO0lBQ0FFLGNBQWM7UUFDWkMsUUFBUXJCLFFBQVFDLEdBQUcsQ0FBQ3FCLHdCQUF3QixJQUFJQztJQUNsRDtBQUNGLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztRQUFLQyxXQUFXbkQsMkxBQWM7UUFBRXFELHdCQUF3Qjs7MEJBQ2pFLDhEQUFDQzs7a0NBQ0MsOERBQUNDO3dCQUFLQyxLQUFJO3dCQUFPQyxNQUFLO3dCQUFlQyxPQUFNOzs7Ozs7a0NBQzNDLDhEQUFDSDt3QkFBS0MsS0FBSTt3QkFBT0MsTUFBSzt3QkFBZTVCLE1BQUs7Ozs7OztrQ0FDMUMsOERBQUMwQjt3QkFBS0MsS0FBSTt3QkFBbUJDLE1BQUs7Ozs7OztrQ0FDbEMsOERBQUNGO3dCQUFLQyxLQUFJO3dCQUFXQyxNQUFLOzs7Ozs7a0NBQzFCLDhEQUFDRTt3QkFBSzdDLE1BQUs7d0JBQWM4QyxTQUFROzs7Ozs7a0NBQ2pDLDhEQUFDRDt3QkFBSzdDLE1BQUs7d0JBQVc4QyxTQUFROzs7Ozs7Ozs7Ozs7MEJBRWhDLDhEQUFDQztnQkFBS1YsV0FBVTswQkFDZCw0RUFBQy9DLDhFQUFhQTtvQkFDWjBELG1CQUFtQjtvQkFDbkJDLG9CQUFvQjs4QkFFcEIsNEVBQUMxRCxnRkFBWUE7a0NBQ1gsNEVBQUNKLHVFQUFZQTtzQ0FDWCw0RUFBQ0ssK0ZBQW9CQTswQ0FDbkIsNEVBQUNKLDhFQUFhQTs4Q0FDWiw0RUFBQ0MsOEVBQWFBO2tEQUNaLDRFQUFDNkQ7NENBQUliLFdBQVU7c0RBQ2IsNEVBQUNhO2dEQUFJYixXQUFVOzBEQUNaSDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVl6QiIsInNvdXJjZXMiOlsid2VicGFjazovL3N5bmFwc2VhaS1mcm9udGVuZC8uL2FwcC9sYXlvdXQudHN4Pzk5ODgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnO1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJztcbmltcG9ydCAnLi9nbG9iYWxzLmNzcyc7XG5pbXBvcnQgeyBBdXRoUHJvdmlkZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvYXV0aC9BdXRoUHJvdmlkZXInO1xuaW1wb3J0IHsgUXVlcnlQcm92aWRlciB9IGZyb20gJ0AvY29tcG9uZW50cy9wcm92aWRlcnMvUXVlcnlQcm92aWRlcic7XG5pbXBvcnQgeyBUb2FzdFByb3ZpZGVyIH0gZnJvbSAnQC9jb21wb25lbnRzL3Byb3ZpZGVycy9Ub2FzdFByb3ZpZGVyJztcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvcHJvdmlkZXJzL1RoZW1lUHJvdmlkZXInO1xuaW1wb3J0IHsgQTExeVByb3ZpZGVyIH0gZnJvbSAnQC9jb21wb25lbnRzL2FjY2Vzc2liaWxpdHkvQTExeVByb3ZpZGVyJztcbmltcG9ydCB7IE9yZ2FuaXphdGlvblByb3ZpZGVyIH0gZnJvbSAnQC9jb21wb25lbnRzL29yZ2FuaXphdGlvbi9Pcmdhbml6YXRpb25Qcm92aWRlcic7XG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBcbiAgc3Vic2V0czogWydsYXRpbiddLFxuICB2YXJpYWJsZTogJy0tZm9udC1pbnRlcicsXG4gIGRpc3BsYXk6ICdzd2FwJyxcbn0pO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZToge1xuICAgIGRlZmF1bHQ6ICdTeW5hcHNlQUkgLSBVbml2ZXJzYWwgQUkgT3JjaGVzdHJhdGlvbiBQbGF0Zm9ybScsXG4gICAgdGVtcGxhdGU6ICclcyB8IFN5bmFwc2VBSScsXG4gIH0sXG4gIGRlc2NyaXB0aW9uOiAnQnVpbGQsIGRlcGxveSwgYW5kIG1hbmFnZSBBSSBhZ2VudHMsIHRvb2xzLCBhbmQgd29ya2Zsb3dzIHdpdGggU3luYXBzZUFJXFwncyBuby1jb2RlIHBsYXRmb3JtLicsXG4gIGtleXdvcmRzOiBbXG4gICAgJ0FJJyxcbiAgICAnYXJ0aWZpY2lhbCBpbnRlbGxpZ2VuY2UnLFxuICAgICdhdXRvbWF0aW9uJyxcbiAgICAnbm8tY29kZScsXG4gICAgJ2FnZW50cycsXG4gICAgJ3dvcmtmbG93cycsXG4gICAgJ29yY2hlc3RyYXRpb24nLFxuICAgICdTeW5hcHNlQUknLFxuICBdLFxuICBhdXRob3JzOiBbeyBuYW1lOiAnU3luYXBzZUFJIFRlYW0nIH1dLFxuICBjcmVhdG9yOiAnU3luYXBzZUFJJyxcbiAgcHVibGlzaGVyOiAnU3luYXBzZUFJJyxcbiAgZm9ybWF0RGV0ZWN0aW9uOiB7XG4gICAgZW1haWw6IGZhbHNlLFxuICAgIGFkZHJlc3M6IGZhbHNlLFxuICAgIHRlbGVwaG9uZTogZmFsc2UsXG4gIH0sXG4gIG1ldGFkYXRhQmFzZTogbmV3IFVSTChwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUFBfVVJMIHx8ICdodHRwOi8vbG9jYWxob3N0OjMwMDEnKSxcbiAgYWx0ZXJuYXRlczoge1xuICAgIGNhbm9uaWNhbDogJy8nLFxuICB9LFxuICBvcGVuR3JhcGg6IHtcbiAgICB0eXBlOiAnd2Vic2l0ZScsXG4gICAgbG9jYWxlOiAnZW5fVVMnLFxuICAgIHVybDogJy8nLFxuICAgIHRpdGxlOiAnU3luYXBzZUFJIC0gVW5pdmVyc2FsIEFJIE9yY2hlc3RyYXRpb24gUGxhdGZvcm0nLFxuICAgIGRlc2NyaXB0aW9uOiAnQnVpbGQsIGRlcGxveSwgYW5kIG1hbmFnZSBBSSBhZ2VudHMsIHRvb2xzLCBhbmQgd29ya2Zsb3dzIHdpdGggU3luYXBzZUFJXFwncyBuby1jb2RlIHBsYXRmb3JtLicsXG4gICAgc2l0ZU5hbWU6ICdTeW5hcHNlQUknLFxuICAgIGltYWdlczogW1xuICAgICAge1xuICAgICAgICB1cmw6ICcvb2ctaW1hZ2UucG5nJyxcbiAgICAgICAgd2lkdGg6IDEyMDAsXG4gICAgICAgIGhlaWdodDogNjMwLFxuICAgICAgICBhbHQ6ICdTeW5hcHNlQUkgUGxhdGZvcm0nLFxuICAgICAgfSxcbiAgICBdLFxuICB9LFxuICB0d2l0dGVyOiB7XG4gICAgY2FyZDogJ3N1bW1hcnlfbGFyZ2VfaW1hZ2UnLFxuICAgIHRpdGxlOiAnU3luYXBzZUFJIC0gVW5pdmVyc2FsIEFJIE9yY2hlc3RyYXRpb24gUGxhdGZvcm0nLFxuICAgIGRlc2NyaXB0aW9uOiAnQnVpbGQsIGRlcGxveSwgYW5kIG1hbmFnZSBBSSBhZ2VudHMsIHRvb2xzLCBhbmQgd29ya2Zsb3dzIHdpdGggU3luYXBzZUFJXFwncyBuby1jb2RlIHBsYXRmb3JtLicsXG4gICAgaW1hZ2VzOiBbJy9vZy1pbWFnZS5wbmcnXSxcbiAgICBjcmVhdG9yOiAnQHN5bmFwc2VhaScsXG4gIH0sXG4gIHJvYm90czoge1xuICAgIGluZGV4OiB0cnVlLFxuICAgIGZvbGxvdzogdHJ1ZSxcbiAgICBnb29nbGVCb3Q6IHtcbiAgICAgIGluZGV4OiB0cnVlLFxuICAgICAgZm9sbG93OiB0cnVlLFxuICAgICAgJ21heC12aWRlby1wcmV2aWV3JzogLTEsXG4gICAgICAnbWF4LWltYWdlLXByZXZpZXcnOiAnbGFyZ2UnLFxuICAgICAgJ21heC1zbmlwcGV0JzogLTEsXG4gICAgfSxcbiAgfSxcbiAgdmVyaWZpY2F0aW9uOiB7XG4gICAgZ29vZ2xlOiBwcm9jZXNzLmVudi5HT09HTEVfU0lURV9WRVJJRklDQVRJT04gfHwgdW5kZWZpbmVkLFxuICB9LFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiIGNsYXNzTmFtZT17aW50ZXIudmFyaWFibGV9IHN1cHByZXNzSHlkcmF0aW9uV2FybmluZz5cbiAgICAgIDxoZWFkPlxuICAgICAgICA8bGluayByZWw9XCJpY29uXCIgaHJlZj1cIi9mYXZpY29uLmljb1wiIHNpemVzPVwiYW55XCIgLz5cbiAgICAgICAgPGxpbmsgcmVsPVwiaWNvblwiIGhyZWY9XCIvZmF2aWNvbi5zdmdcIiB0eXBlPVwiaW1hZ2Uvc3ZnK3htbFwiIC8+XG4gICAgICAgIDxsaW5rIHJlbD1cImFwcGxlLXRvdWNoLWljb25cIiBocmVmPVwiL2FwcGxlLXRvdWNoLWljb24ucG5nXCIgLz5cbiAgICAgICAgPGxpbmsgcmVsPVwibWFuaWZlc3RcIiBocmVmPVwiL21hbmlmZXN0Lmpzb25cIiAvPlxuICAgICAgICA8bWV0YSBuYW1lPVwidGhlbWUtY29sb3JcIiBjb250ZW50PVwiIzBlYTVlOVwiIC8+XG4gICAgICAgIDxtZXRhIG5hbWU9XCJ2aWV3cG9ydFwiIGNvbnRlbnQ9XCJ3aWR0aD1kZXZpY2Utd2lkdGgsIGluaXRpYWwtc2NhbGU9MSwgbWF4aW11bS1zY2FsZT01XCIgLz5cbiAgICAgIDwvaGVhZD5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1iYWNrZ3JvdW5kIGZvbnQtc2FucyBhbnRpYWxpYXNlZFwiPlxuICAgICAgICA8VGhlbWVQcm92aWRlclxuICAgICAgICAgIGVuYWJsZVRyYW5zaXRpb25zPXt0cnVlfVxuICAgICAgICAgIHRyYW5zaXRpb25EdXJhdGlvbj17MjAwfVxuICAgICAgICA+XG4gICAgICAgICAgPEExMXlQcm92aWRlcj5cbiAgICAgICAgICAgIDxBdXRoUHJvdmlkZXI+XG4gICAgICAgICAgICAgIDxPcmdhbml6YXRpb25Qcm92aWRlcj5cbiAgICAgICAgICAgICAgICA8UXVlcnlQcm92aWRlcj5cbiAgICAgICAgICAgICAgICAgIDxUb2FzdFByb3ZpZGVyPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGZsZXggbWluLWgtc2NyZWVuIGZsZXgtY29sXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L1RvYXN0UHJvdmlkZXI+XG4gICAgICAgICAgICAgICAgPC9RdWVyeVByb3ZpZGVyPlxuICAgICAgICAgICAgICA8L09yZ2FuaXphdGlvblByb3ZpZGVyPlxuICAgICAgICAgICAgPC9BdXRoUHJvdmlkZXI+XG4gICAgICAgICAgPC9BMTF5UHJvdmlkZXI+XG4gICAgICAgIDwvVGhlbWVQcm92aWRlcj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiaW50ZXIiLCJBdXRoUHJvdmlkZXIiLCJRdWVyeVByb3ZpZGVyIiwiVG9hc3RQcm92aWRlciIsIlRoZW1lUHJvdmlkZXIiLCJBMTF5UHJvdmlkZXIiLCJPcmdhbml6YXRpb25Qcm92aWRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZWZhdWx0IiwidGVtcGxhdGUiLCJkZXNjcmlwdGlvbiIsImtleXdvcmRzIiwiYXV0aG9ycyIsIm5hbWUiLCJjcmVhdG9yIiwicHVibGlzaGVyIiwiZm9ybWF0RGV0ZWN0aW9uIiwiZW1haWwiLCJhZGRyZXNzIiwidGVsZXBob25lIiwibWV0YWRhdGFCYXNlIiwiVVJMIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FQUF9VUkwiLCJhbHRlcm5hdGVzIiwiY2Fub25pY2FsIiwib3BlbkdyYXBoIiwidHlwZSIsImxvY2FsZSIsInVybCIsInNpdGVOYW1lIiwiaW1hZ2VzIiwid2lkdGgiLCJoZWlnaHQiLCJhbHQiLCJ0d2l0dGVyIiwiY2FyZCIsInJvYm90cyIsImluZGV4IiwiZm9sbG93IiwiZ29vZ2xlQm90IiwidmVyaWZpY2F0aW9uIiwiZ29vZ2xlIiwiR09PR0xFX1NJVEVfVkVSSUZJQ0FUSU9OIiwidW5kZWZpbmVkIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJjbGFzc05hbWUiLCJ2YXJpYWJsZSIsInN1cHByZXNzSHlkcmF0aW9uV2FybmluZyIsImhlYWQiLCJsaW5rIiwicmVsIiwiaHJlZiIsInNpemVzIiwibWV0YSIsImNvbnRlbnQiLCJib2R5IiwiZW5hYmxlVHJhbnNpdGlvbnMiLCJ0cmFuc2l0aW9uRHVyYXRpb24iLCJkaXYiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/accessibility/A11yProvider.tsx":
/*!***************************************************!*\
  !*** ./components/accessibility/A11yProvider.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A11yProvider: () => (/* binding */ e1),
/* harmony export */   SkipLinks: () => (/* binding */ e6),
/* harmony export */   useA11y: () => (/* binding */ e0),
/* harmony export */   useAnnouncements: () => (/* binding */ e5),
/* harmony export */   useFocusManagement: () => (/* binding */ e4),
/* harmony export */   useKeyboardShortcuts: () => (/* binding */ e3),
/* harmony export */   withA11y: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\components\accessibility\A11yProvider.tsx#useA11y`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\components\accessibility\A11yProvider.tsx#A11yProvider`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\components\accessibility\A11yProvider.tsx#withA11y`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\components\accessibility\A11yProvider.tsx#useKeyboardShortcuts`);

const e4 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\components\accessibility\A11yProvider.tsx#useFocusManagement`);

const e5 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\components\accessibility\A11yProvider.tsx#useAnnouncements`);

const e6 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\components\accessibility\A11yProvider.tsx#SkipLinks`);


/***/ }),

/***/ "(rsc)/./components/auth/AuthProvider.tsx":
/*!******************************************!*\
  !*** ./components/auth/AuthProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\components\auth\AuthProvider.tsx#AuthProvider`);


/***/ }),

/***/ "(rsc)/./components/organization/OrganizationProvider.tsx":
/*!**********************************************************!*\
  !*** ./components/organization/OrganizationProvider.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   OrganizationProvider: () => (/* binding */ e0),
/* harmony export */   useOrganizationPermissions: () => (/* binding */ e2),
/* harmony export */   useOrganizationSwitcher: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\components\organization\OrganizationProvider.tsx#OrganizationProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\components\organization\OrganizationProvider.tsx#useOrganizationSwitcher`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\components\organization\OrganizationProvider.tsx#useOrganizationPermissions`);


/***/ }),

/***/ "(rsc)/./components/providers/QueryProvider.tsx":
/*!************************************************!*\
  !*** ./components/providers/QueryProvider.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   QueryProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\components\providers\QueryProvider.tsx#QueryProvider`);


/***/ }),

/***/ "(rsc)/./components/providers/ThemeProvider.tsx":
/*!************************************************!*\
  !*** ./components/providers/ThemeProvider.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e1),
/* harmony export */   useTheme: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\components\providers\ThemeProvider.tsx#useTheme`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\components\providers\ThemeProvider.tsx#ThemeProvider`);


/***/ }),

/***/ "(rsc)/./components/providers/ToastProvider.tsx":
/*!************************************************!*\
  !*** ./components/providers/ToastProvider.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ToastProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\components\providers\ToastProvider.tsx#ToastProvider`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@tanstack","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/react-hot-toast","vendor-chunks/goober"], () => (__webpack_exec__("(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5Claragon%5Cwww%5Cmax%5Ctrae%5Ckilo-teset%5CaugsyanapseAI%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5Cmax%5Ctrae%5Ckilo-teset%5CaugsyanapseAI%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();