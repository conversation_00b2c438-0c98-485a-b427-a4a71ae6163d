import { apiClient } from '@/lib/api';

export interface ConfigSuggestion {
  id: string;
  type: 'field' | 'section' | 'workflow' | 'optimization';
  title: string;
  description: string;
  confidence: number; // 0-1
  impact: 'low' | 'medium' | 'high';
  category: string;
  action: {
    type: 'set_value' | 'add_field' | 'remove_field' | 'reorder' | 'template';
    target: string;
    value?: any;
    template?: string;
  };
  reasoning: string;
  examples?: string[];
}

export interface ConfigContext {
  currentConfig: Record<string, any>;
  userIntent?: string;
  domain: 'agent' | 'tool' | 'workflow' | 'template';
  userLevel: 'beginner' | 'intermediate' | 'advanced';
  organizationPreferences?: Record<string, any>;
  previousConfigurations?: Record<string, any>[];
}

export interface ConfigAssistantResponse {
  suggestions: ConfigSuggestion[];
  explanation: string;
  nextSteps: string[];
  confidence: number;
  estimatedTime: string;
}

class ConfigAssistant {
  private baseUrl: string;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';
  }

  /**
   * Get AI-powered configuration suggestions based on context
   */
  async getSuggestions(context: ConfigContext): Promise<ConfigAssistantResponse> {
    try {
      const response = await apiClient.post('/api/v1/ai/config-suggestions', context);
      return response.data;
    } catch (error) {
      console.error('Failed to get config suggestions:', error);
      return this.getFallbackSuggestions(context);
    }
  }

  /**
   * Analyze user intent from natural language input
   */
  async analyzeIntent(input: string, context: Partial<ConfigContext>): Promise<{
    intent: string;
    entities: Record<string, any>;
    confidence: number;
    suggestedActions: string[];
  }> {
    try {
      const response = await apiClient.post('/api/v1/ai/analyze-intent', {
        input,
        context,
      });
      return response.data;
    } catch (error) {
      console.error('Failed to analyze intent:', error);
      return this.getFallbackIntent(input);
    }
  }

  /**
   * Get smart defaults for a configuration type
   */
  async getSmartDefaults(
    type: string,
    userPreferences?: Record<string, any>
  ): Promise<Record<string, any>> {
    try {
      const response = await apiClient.post('/api/v1/ai/smart-defaults', {
        type,
        userPreferences,
      });
      return response.data;
    } catch (error) {
      console.error('Failed to get smart defaults:', error);
      return this.getStaticDefaults(type);
    }
  }

  /**
   * Validate configuration and suggest improvements
   */
  async validateConfig(
    config: Record<string, any>,
    type: string
  ): Promise<{
    isValid: boolean;
    errors: Array<{
      field: string;
      message: string;
      severity: 'error' | 'warning' | 'info';
      suggestion?: string;
    }>;
    optimizations: ConfigSuggestion[];
  }> {
    try {
      const response = await apiClient.post('/api/v1/ai/validate-config', {
        config,
        type,
      });
      return response.data;
    } catch (error) {
      console.error('Failed to validate config:', error);
      return {
        isValid: true,
        errors: [],
        optimizations: [],
      };
    }
  }

  /**
   * Get contextual help for a specific field or section
   */
  async getContextualHelp(
    field: string,
    context: Partial<ConfigContext>
  ): Promise<{
    title: string;
    description: string;
    examples: string[];
    bestPractices: string[];
    commonMistakes: string[];
    relatedFields: string[];
  }> {
    try {
      const response = await apiClient.post('/api/v1/ai/contextual-help', {
        field,
        context,
      });
      return response.data;
    } catch (error) {
      console.error('Failed to get contextual help:', error);
      return this.getStaticHelp(field);
    }
  }

  /**
   * Generate configuration from natural language description
   */
  async generateFromDescription(
    description: string,
    type: string,
    constraints?: Record<string, any>
  ): Promise<{
    config: Record<string, any>;
    explanation: string;
    confidence: number;
    alternatives: Array<{
      config: Record<string, any>;
      description: string;
      pros: string[];
      cons: string[];
    }>;
  }> {
    try {
      const response = await apiClient.post('/api/v1/ai/generate-config', {
        description,
        type,
        constraints,
      });
      return response.data;
    } catch (error) {
      console.error('Failed to generate config:', error);
      return {
        config: this.getStaticDefaults(type),
        explanation: 'Generated basic configuration due to AI service unavailability.',
        confidence: 0.5,
        alternatives: [],
      };
    }
  }

  /**
   * Fallback suggestions when AI service is unavailable
   */
  private getFallbackSuggestions(context: ConfigContext): ConfigAssistantResponse {
    const suggestions: ConfigSuggestion[] = [];

    // Add basic suggestions based on domain
    if (context.domain === 'agent') {
      suggestions.push({
        id: 'agent-name',
        type: 'field',
        title: 'Add descriptive name',
        description: 'Give your agent a clear, descriptive name',
        confidence: 0.8,
        impact: 'medium',
        category: 'basic',
        action: {
          type: 'set_value',
          target: 'name',
          value: 'My AI Agent',
        },
        reasoning: 'A clear name helps identify and manage your agent',
        examples: ['Customer Support Agent', 'Data Analysis Assistant'],
      });
    }

    return {
      suggestions,
      explanation: 'Basic configuration suggestions (AI assistant unavailable)',
      nextSteps: ['Review suggested changes', 'Test configuration'],
      confidence: 0.6,
      estimatedTime: '5-10 minutes',
    };
  }

  /**
   * Fallback intent analysis
   */
  private getFallbackIntent(input: string): {
    intent: string;
    entities: Record<string, any>;
    confidence: number;
    suggestedActions: string[];
  } {
    const lowercaseInput = input.toLowerCase();
    
    if (lowercaseInput.includes('create') || lowercaseInput.includes('build')) {
      return {
        intent: 'create',
        entities: {},
        confidence: 0.7,
        suggestedActions: ['Start with a template', 'Use configuration wizard'],
      };
    }

    if (lowercaseInput.includes('help') || lowercaseInput.includes('how')) {
      return {
        intent: 'help',
        entities: {},
        confidence: 0.8,
        suggestedActions: ['Show documentation', 'Open tutorial'],
      };
    }

    return {
      intent: 'unknown',
      entities: {},
      confidence: 0.3,
      suggestedActions: ['Try rephrasing your request', 'Use the visual builder'],
    };
  }

  /**
   * Static defaults for different configuration types
   */
  private getStaticDefaults(type: string): Record<string, any> {
    const defaults: Record<string, Record<string, any>> = {
      agent: {
        name: 'New Agent',
        description: '',
        model: 'gpt-4',
        temperature: 0.7,
        maxTokens: 1000,
        systemPrompt: 'You are a helpful AI assistant.',
      },
      tool: {
        name: 'New Tool',
        description: '',
        type: 'api',
        timeout: 30000,
        retries: 3,
      },
      workflow: {
        name: 'New Workflow',
        description: '',
        steps: [],
        triggers: [],
      },
    };

    return defaults[type] || {};
  }

  /**
   * Static help content
   */
  private getStaticHelp(field: string): {
    title: string;
    description: string;
    examples: string[];
    bestPractices: string[];
    commonMistakes: string[];
    relatedFields: string[];
  } {
    const helpContent: Record<string, any> = {
      name: {
        title: 'Name',
        description: 'A unique identifier for your configuration',
        examples: ['Customer Support Agent', 'Data Processor'],
        bestPractices: ['Use descriptive names', 'Avoid special characters'],
        commonMistakes: ['Using generic names like "Agent1"'],
        relatedFields: ['description', 'tags'],
      },
    };

    return helpContent[field] || {
      title: field,
      description: 'No help available for this field',
      examples: [],
      bestPractices: [],
      commonMistakes: [],
      relatedFields: [],
    };
  }
}

export const configAssistant = new ConfigAssistant();
