#!/bin/bash

# SynapseAI Database Backup and Recovery Script
# Specialized backup for PostgreSQL with tenant-aware restoration

set -euo pipefail

# Configuration
BACKUP_DIR="/var/backups/synapseai/database"
S3_BUCKET="${S3_BUCKET:-synapseai-backups}"
RETENTION_DAYS="${RETENTION_DAYS:-30}"
LOG_FILE="/var/log/synapseai/database-backup.log"

# Database configuration
DB_HOST="${POSTGRES_HOST:-localhost}"
DB_PORT="${POSTGRES_PORT:-5432}"
DB_NAME="${POSTGRES_DB:-synapseai}"
DB_USER="${POSTGRES_USER:-synapseai}"
PGPASSWORD="${POSTGRES_PASSWORD}"

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

error_exit() {
    log "ERROR: $1"
    exit 1
}

# Create backup directories
create_directories() {
    log "Creating backup directories..."
    mkdir -p "$BACKUP_DIR"/{full,schema,data,tenant}
    chmod 750 "$BACKUP_DIR"
}

# Full database backup
backup_full_database() {
    log "Starting full database backup..."
    
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local backup_file="$BACKUP_DIR/full/synapseai_full_${timestamp}.sql"
    
    # Create full backup with custom format for faster restoration
    pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        --verbose --clean --if-exists --create \
        --format=custom --compress=9 \
        --file="$backup_file" || error_exit "Full database backup failed"
    
    # Verify backup integrity
    pg_restore --list "$backup_file" > /dev/null || error_exit "Backup verification failed"
    
    local backup_size=$(du -h "$backup_file" | cut -f1)
    log "Full database backup completed: $backup_file ($backup_size)"
    
    echo "$backup_file"
}

# Schema-only backup
backup_schema_only() {
    log "Starting schema-only backup..."
    
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local backup_file="$BACKUP_DIR/schema/synapseai_schema_${timestamp}.sql"
    
    # Create schema-only backup
    pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        --verbose --schema-only --clean --if-exists \
        --format=plain \
        --file="$backup_file" || error_exit "Schema backup failed"
    
    # Compress the schema backup
    gzip "$backup_file"
    
    local backup_size=$(du -h "${backup_file}.gz" | cut -f1)
    log "Schema backup completed: ${backup_file}.gz ($backup_size)"
    
    echo "${backup_file}.gz"
}

# Data-only backup
backup_data_only() {
    log "Starting data-only backup..."
    
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local backup_file="$BACKUP_DIR/data/synapseai_data_${timestamp}.sql"
    
    # Create data-only backup
    pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        --verbose --data-only \
        --format=custom --compress=9 \
        --file="$backup_file" || error_exit "Data backup failed"
    
    local backup_size=$(du -h "$backup_file" | cut -f1)
    log "Data backup completed: $backup_file ($backup_size)"
    
    echo "$backup_file"
}

# Tenant-specific backup
backup_organization() {
    local org_id="$1"
    local org_slug="$2"
    
    log "Starting backup for organization: $org_slug ($org_id)"
    
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local backup_file="$BACKUP_DIR/tenant/${org_slug}_${timestamp}.sql"
    
    # Create tenant-specific backup using custom script
    cat > /tmp/tenant_backup.sql << EOF
-- Tenant backup for organization: $org_slug ($org_id)
-- Generated on: $(date)

BEGIN;

-- Organizations
COPY (SELECT * FROM organizations WHERE id = '$org_id') TO STDOUT;

-- Users
COPY (SELECT * FROM users WHERE "organizationId" = '$org_id') TO STDOUT;

-- Roles
COPY (SELECT * FROM roles WHERE "organizationId" = '$org_id') TO STDOUT;

-- Sessions
COPY (SELECT * FROM sessions WHERE "organizationId" = '$org_id') TO STDOUT;

-- Templates
COPY (SELECT * FROM templates WHERE "organizationId" = '$org_id') TO STDOUT;

-- Agents
COPY (SELECT * FROM agents WHERE "organizationId" = '$org_id') TO STDOUT;

-- Tools
COPY (SELECT * FROM tools WHERE "organizationId" = '$org_id') TO STDOUT;

-- Workflows
COPY (SELECT * FROM workflows WHERE "organizationId" = '$org_id') TO STDOUT;

-- All execution tables
COPY (SELECT * FROM agent_executions WHERE "organizationId" = '$org_id') TO STDOUT;
COPY (SELECT * FROM tool_executions WHERE "organizationId" = '$org_id') TO STDOUT;
COPY (SELECT * FROM workflow_executions WHERE "organizationId" = '$org_id') TO STDOUT;

-- Analytics and billing
COPY (SELECT * FROM analytics WHERE "organizationId" = '$org_id') TO STDOUT;
COPY (SELECT * FROM billing WHERE "organizationId" = '$org_id') TO STDOUT;
COPY (SELECT * FROM usage_meters WHERE "organizationId" = '$org_id') TO STDOUT;

COMMIT;
EOF

    # Execute tenant backup
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        -f /tmp/tenant_backup.sql > "$backup_file" 2>/dev/null || error_exit "Tenant backup failed"
    
    # Clean up temp file
    rm -f /tmp/tenant_backup.sql
    
    # Compress tenant backup
    gzip "$backup_file"
    
    local backup_size=$(du -h "${backup_file}.gz" | cut -f1)
    log "Tenant backup completed: ${backup_file}.gz ($backup_size)"
    
    echo "${backup_file}.gz"
}

# Restore full database
restore_full_database() {
    local backup_file="$1"
    local target_db="${2:-${DB_NAME}_restored}"
    
    log "Restoring full database from: $backup_file"
    
    # Create target database
    createdb -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" "$target_db" || true
    
    # Restore database
    pg_restore -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$target_db" \
        --verbose --clean --if-exists \
        "$backup_file" || error_exit "Database restoration failed"
    
    log "Database restored successfully to: $target_db"
}

# Restore organization data
restore_organization() {
    local backup_file="$1"
    local target_org_id="$2"
    
    log "Restoring organization data from: $backup_file"
    
    # Decompress if needed
    local restore_file="$backup_file"
    if [[ "$backup_file" == *.gz ]]; then
        restore_file="/tmp/$(basename "$backup_file" .gz)"
        gunzip -c "$backup_file" > "$restore_file"
    fi
    
    # Create restoration script
    cat > /tmp/tenant_restore.sql << EOF
-- Tenant restoration script
-- Target organization ID: $target_org_id

BEGIN;

-- Update organization IDs in the backup data
-- This is a simplified example - in practice, you'd need more sophisticated ID mapping

-- Restore the data
\i $restore_file

COMMIT;
EOF

    # Execute restoration
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        -f /tmp/tenant_restore.sql || error_exit "Tenant restoration failed"
    
    # Clean up
    rm -f /tmp/tenant_restore.sql
    if [[ "$restore_file" != "$backup_file" ]]; then
        rm -f "$restore_file"
    fi
    
    log "Organization data restored successfully"
}

# Point-in-time recovery setup
setup_point_in_time_recovery() {
    log "Setting up point-in-time recovery..."
    
    # Enable WAL archiving
    cat > /tmp/postgresql_pitr.conf << EOF
# Point-in-time recovery configuration
wal_level = replica
archive_mode = on
archive_command = 'cp %p /var/lib/postgresql/wal_archive/%f'
max_wal_senders = 3
wal_keep_segments = 32
EOF

    log "PITR configuration created. Apply to postgresql.conf and restart PostgreSQL."
}

# Backup verification
verify_backup() {
    local backup_file="$1"
    
    log "Verifying backup: $backup_file"
    
    if [[ "$backup_file" == *.gz ]]; then
        # Verify compressed file
        if gzip -t "$backup_file"; then
            log "Compressed backup verification passed"
        else
            error_exit "Compressed backup verification failed"
        fi
    else
        # Verify PostgreSQL custom format
        if pg_restore --list "$backup_file" > /dev/null 2>&1; then
            log "PostgreSQL backup verification passed"
        else
            error_exit "PostgreSQL backup verification failed"
        fi
    fi
}

# Upload to S3
upload_to_s3() {
    local file="$1"
    local s3_path="$2"
    
    log "Uploading $file to S3..."
    
    if command -v aws >/dev/null 2>&1; then
        aws s3 cp "$file" "s3://$S3_BUCKET/$s3_path" \
            --storage-class STANDARD_IA \
            --server-side-encryption AES256 || error_exit "S3 upload failed"
        
        log "Successfully uploaded to S3: s3://$S3_BUCKET/$s3_path"
    else
        log "AWS CLI not found, skipping S3 upload"
    fi
}

# Cleanup old backups
cleanup_old_backups() {
    log "Cleaning up old backups..."
    
    find "$BACKUP_DIR" -type f -name "*.sql" -mtime +$RETENTION_DAYS -delete
    find "$BACKUP_DIR" -type f -name "*.gz" -mtime +$RETENTION_DAYS -delete
    
    log "Cleanup completed"
}

# Main function
main() {
    local command="${1:-full}"
    
    log "Starting database backup process..."
    log "Command: $command"
    
    create_directories
    
    case "$command" in
        "full")
            local backup_file=$(backup_full_database)
            verify_backup "$backup_file"
            upload_to_s3 "$backup_file" "database/full/$(basename "$backup_file")"
            ;;
        
        "schema")
            local backup_file=$(backup_schema_only)
            verify_backup "$backup_file"
            upload_to_s3 "$backup_file" "database/schema/$(basename "$backup_file")"
            ;;
        
        "data")
            local backup_file=$(backup_data_only)
            verify_backup "$backup_file"
            upload_to_s3 "$backup_file" "database/data/$(basename "$backup_file")"
            ;;
        
        "tenant")
            local org_id="${2:-}"
            local org_slug="${3:-}"
            if [[ -z "$org_id" || -z "$org_slug" ]]; then
                error_exit "Usage: $0 tenant <org_id> <org_slug>"
            fi
            local backup_file=$(backup_organization "$org_id" "$org_slug")
            verify_backup "$backup_file"
            upload_to_s3 "$backup_file" "database/tenant/$(basename "$backup_file")"
            ;;
        
        "restore")
            local backup_file="${2:-}"
            local target_db="${3:-}"
            if [[ -z "$backup_file" ]]; then
                error_exit "Usage: $0 restore <backup_file> [target_db]"
            fi
            restore_full_database "$backup_file" "$target_db"
            ;;
        
        "restore-tenant")
            local backup_file="${2:-}"
            local target_org_id="${3:-}"
            if [[ -z "$backup_file" || -z "$target_org_id" ]]; then
                error_exit "Usage: $0 restore-tenant <backup_file> <target_org_id>"
            fi
            restore_organization "$backup_file" "$target_org_id"
            ;;
        
        "cleanup")
            cleanup_old_backups
            ;;
        
        *)
            echo "Usage: $0 {full|schema|data|tenant|restore|restore-tenant|cleanup}"
            echo ""
            echo "Commands:"
            echo "  full                           - Full database backup"
            echo "  schema                         - Schema-only backup"
            echo "  data                          - Data-only backup"
            echo "  tenant <org_id> <org_slug>    - Tenant-specific backup"
            echo "  restore <backup_file> [db]    - Restore full database"
            echo "  restore-tenant <file> <org>   - Restore tenant data"
            echo "  cleanup                       - Clean old backups"
            exit 1
            ;;
    esac
    
    log "Database backup process completed successfully"
}

# Run main function
main "$@"
