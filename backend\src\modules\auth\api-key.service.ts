import { Injectable, UnauthorizedException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '@modules/prisma/prisma.service';
import { LoggerService } from '@services/logger/logger.service';
import * as crypto from 'crypto';

export interface CreateApiKeyDto {
  name: string;
  description?: string;
  permissions: string[];
  expiresAt?: Date;
  organizationId: string;
  createdBy: string;
}

export interface ApiKeyResponse {
  id: string;
  name: string;
  description?: string;
  permissions: string[];
  key: string; // Only returned on creation
  keyPrefix: string;
  expiresAt?: Date;
  isActive: boolean;
  lastUsedAt?: Date;
  createdAt: Date;
}

@Injectable()
export class ApiKeyService {
  constructor(
    private prismaService: PrismaService,
    private logger: LoggerService,
  ) {}

  /**
   * Create a new API key
   */
  async createApiKey(createDto: CreateApiKeyDto): Promise<ApiKeyResponse> {
    // Generate API key
    const keyId = crypto.randomBytes(8).toString('hex');
    const keySecret = crypto.randomBytes(32).toString('hex');
    const apiKey = `sk_${keyId}_${keySecret}`;
    const keyPrefix = `sk_${keyId}`;

    // Hash the key for storage
    const keyHash = crypto.createHash('sha256').update(apiKey).digest('hex');

    try {
      // Create API key in database
      const apiKeyRecord = await this.prismaService.apiKey.create({
        data: {
          name: createDto.name,
          description: createDto.description,
          keyHash,
          keyPrefix,
          permissions: createDto.permissions,
          expiresAt: createDto.expiresAt,
          organizationId: createDto.organizationId,
          createdBy: createDto.createdBy,
          isActive: true,
        },
      });

      this.logger.audit('API_KEY_CREATED', 'success', {
        userId: createDto.createdBy,
        organizationId: createDto.organizationId,
        resource: 'api_key',
        details: {
          apiKeyId: apiKeyRecord.id,
          permissions: createDto.permissions,
        },
      });

      return {
        id: apiKeyRecord.id,
        name: apiKeyRecord.name,
        description: apiKeyRecord.description,
        permissions: apiKeyRecord.permissions as string[],
        key: apiKey, // Only returned on creation
        keyPrefix: apiKeyRecord.keyPrefix,
        expiresAt: apiKeyRecord.expiresAt,
        isActive: apiKeyRecord.isActive,
        lastUsedAt: apiKeyRecord.lastUsedAt,
        createdAt: apiKeyRecord.createdAt,
      };
    } catch (error) {
      this.logger.error('Failed to create API key:', error);
      throw new BadRequestException('Failed to create API key');
    }
  }

  /**
   * Validate API key and return associated data
   */
  async validateApiKey(apiKey: string): Promise<any> {
    if (!apiKey || !apiKey.startsWith('sk_')) {
      throw new UnauthorizedException('Invalid API key format');
    }

    // Extract key prefix
    const keyPrefix = apiKey.substring(0, 11); // sk_ + 8 chars

    // Hash the provided key
    const keyHash = crypto.createHash('sha256').update(apiKey).digest('hex');

    try {
      const apiKeyRecord = await this.prismaService.apiKey.findFirst({
        where: {
          keyHash,
          keyPrefix,
          isActive: true,
        },
        include: {
          organization: {
            select: {
              id: true,
              name: true,
              slug: true,
              isActive: true,
            },
          },
          creator: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      if (!apiKeyRecord || !apiKeyRecord.organization.isActive) {
        throw new UnauthorizedException('Invalid or inactive API key');
      }

      // Check if key is expired
      if (apiKeyRecord.expiresAt && apiKeyRecord.expiresAt < new Date()) {
        throw new UnauthorizedException('API key has expired');
      }

      // Update last used timestamp
      await this.prismaService.apiKey.update({
        where: { id: apiKeyRecord.id },
        data: { lastUsedAt: new Date() },
      });

      return {
        apiKeyId: apiKeyRecord.id,
        organizationId: apiKeyRecord.organizationId,
        permissions: apiKeyRecord.permissions as string[],
        organization: apiKeyRecord.organization,
        creator: apiKeyRecord.creator,
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      this.logger.error('API key validation failed:', error);
      throw new UnauthorizedException('Invalid API key');
    }
  }

  /**
   * List API keys for an organization
   */
  async listApiKeys(organizationId: string, userId: string): Promise<Omit<ApiKeyResponse, 'key'>[]> {
    const apiKeys = await this.prismaService.apiKey.findMany({
      where: {
        organizationId,
        // Users can only see their own API keys unless they're admin
        // This would need to be enhanced with proper permission checking
      },
      select: {
        id: true,
        name: true,
        description: true,
        permissions: true,
        keyPrefix: true,
        expiresAt: true,
        isActive: true,
        lastUsedAt: true,
        createdAt: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return apiKeys.map(key => ({
      ...key,
      permissions: key.permissions as string[],
    }));
  }

  /**
   * Revoke an API key
   */
  async revokeApiKey(apiKeyId: string, organizationId: string, userId: string): Promise<void> {
    try {
      const apiKey = await this.prismaService.apiKey.findFirst({
        where: {
          id: apiKeyId,
          organizationId,
          // Add permission check here
        },
      });

      if (!apiKey) {
        throw new BadRequestException('API key not found');
      }

      await this.prismaService.apiKey.update({
        where: { id: apiKeyId },
        data: { isActive: false },
      });

      this.logger.audit('API_KEY_REVOKED', 'success', {
        userId,
        organizationId,
        resource: 'api_key',
        details: {
          apiKeyId,
        },
      });
    } catch (error) {
      this.logger.error('Failed to revoke API key:', error);
      throw new BadRequestException('Failed to revoke API key');
    }
  }

  /**
   * Get API key usage statistics
   */
  async getApiKeyStats(organizationId: string): Promise<any> {
    const stats = await this.prismaService.apiKey.groupBy({
      by: ['isActive'],
      where: { organizationId },
      _count: true,
    });

    const totalKeys = stats.reduce((sum, stat) => sum + stat._count, 0);
    const activeKeys = stats.find(stat => stat.isActive)?._count || 0;
    const inactiveKeys = totalKeys - activeKeys;

    return {
      total: totalKeys,
      active: activeKeys,
      inactive: inactiveKeys,
    };
  }
}
