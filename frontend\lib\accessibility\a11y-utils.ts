/**
 * Accessibility utilities for WCAG 2.1 AA compliance
 */

export interface A11yConfig {
  announcePageChanges: boolean;
  announceFormErrors: boolean;
  announceStatusUpdates: boolean;
  enableKeyboardNavigation: boolean;
  enableFocusManagement: boolean;
  enableHighContrast: boolean;
  enableReducedMotion: boolean;
  minimumTouchTarget: number;
  minimumColorContrast: number;
}

export const defaultA11yConfig: A11yConfig = {
  announcePageChanges: true,
  announceFormErrors: true,
  announceStatusUpdates: true,
  enableKeyboardNavigation: true,
  enableFocusManagement: true,
  enableHighContrast: true,
  enableReducedMotion: true,
  minimumTouchTarget: 44,
  minimumColorContrast: 4.5,
};

/**
 * Screen reader announcements
 */
export class ScreenReaderAnnouncer {
  private liveRegion: HTMLElement | null = null;
  private politeRegion: HTMLElement | null = null;

  constructor() {
    if (typeof window !== 'undefined') {
      this.createLiveRegions();
    }
  }

  /**
   * Create ARIA live regions for announcements
   */
  private createLiveRegions(): void {
    // Assertive live region for urgent announcements
    this.liveRegion = document.createElement('div');
    this.liveRegion.setAttribute('aria-live', 'assertive');
    this.liveRegion.setAttribute('aria-atomic', 'true');
    this.liveRegion.setAttribute('class', 'sr-only');
    this.liveRegion.style.cssText = `
      position: absolute !important;
      width: 1px !important;
      height: 1px !important;
      padding: 0 !important;
      margin: -1px !important;
      overflow: hidden !important;
      clip: rect(0, 0, 0, 0) !important;
      white-space: nowrap !important;
      border: 0 !important;
    `;
    document.body.appendChild(this.liveRegion);

    // Polite live region for non-urgent announcements
    this.politeRegion = document.createElement('div');
    this.politeRegion.setAttribute('aria-live', 'polite');
    this.politeRegion.setAttribute('aria-atomic', 'true');
    this.politeRegion.setAttribute('class', 'sr-only');
    this.politeRegion.style.cssText = this.liveRegion.style.cssText;
    document.body.appendChild(this.politeRegion);
  }

  /**
   * Announce message to screen readers
   */
  announce(message: string, priority: 'polite' | 'assertive' = 'polite'): void {
    if (!message.trim()) return;

    const region = priority === 'assertive' ? this.liveRegion : this.politeRegion;
    if (!region) return;

    // Clear previous message
    region.textContent = '';

    // Add new message after a brief delay to ensure it's announced
    setTimeout(() => {
      region.textContent = message;
    }, 100);

    // Clear message after announcement
    setTimeout(() => {
      region.textContent = '';
    }, 1000);
  }

  /**
   * Announce page change
   */
  announcePageChange(pageName: string): void {
    this.announce(`Navigated to ${pageName}`, 'polite');
  }

  /**
   * Announce form errors
   */
  announceFormErrors(errors: string[]): void {
    if (errors.length === 0) return;

    const message = errors.length === 1
      ? `Form error: ${errors[0]}`
      : `Form has ${errors.length} errors: ${errors.join(', ')}`;

    this.announce(message, 'assertive');
  }

  /**
   * Announce status updates
   */
  announceStatus(status: string, type: 'success' | 'error' | 'warning' | 'info' = 'info'): void {
    const prefix = {
      success: 'Success:',
      error: 'Error:',
      warning: 'Warning:',
      info: 'Info:',
    }[type];

    this.announce(`${prefix} ${status}`, type === 'error' ? 'assertive' : 'polite');
  }

  /**
   * Cleanup
   */
  destroy(): void {
    if (this.liveRegion) {
      document.body.removeChild(this.liveRegion);
      this.liveRegion = null;
    }
    if (this.politeRegion) {
      document.body.removeChild(this.politeRegion);
      this.politeRegion = null;
    }
  }
}

/**
 * Focus management utilities
 */
export class FocusManager {
  private focusStack: HTMLElement[] = [];
  private trapStack: HTMLElement[] = [];

  /**
   * Set focus to element with optional announcement
   */
  setFocus(element: HTMLElement | string, announce?: string): void {
    const target = typeof element === 'string' 
      ? document.querySelector(element) as HTMLElement
      : element;

    if (!target) return;

    // Store previous focus
    const activeElement = document.activeElement as HTMLElement;
    if (activeElement && activeElement !== target) {
      this.focusStack.push(activeElement);
    }

    // Set focus
    target.focus();

    // Announce if provided
    if (announce) {
      screenReaderAnnouncer.announce(announce);
    }
  }

  /**
   * Restore previous focus
   */
  restoreFocus(): void {
    const previousElement = this.focusStack.pop();
    if (previousElement && document.contains(previousElement)) {
      previousElement.focus();
    }
  }

  /**
   * Trap focus within container
   */
  trapFocus(container: HTMLElement): void {
    this.trapStack.push(container);
    this.setupFocusTrap(container);
  }

  /**
   * Release focus trap
   */
  releaseFocusTrap(): void {
    const container = this.trapStack.pop();
    if (container) {
      this.removeFocusTrap(container);
    }
  }

  /**
   * Setup focus trap for container
   */
  private setupFocusTrap(container: HTMLElement): void {
    const focusableElements = this.getFocusableElements(container);
    if (focusableElements.length === 0) return;

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key !== 'Tab') return;

      if (event.shiftKey) {
        // Shift + Tab
        if (document.activeElement === firstElement) {
          event.preventDefault();
          lastElement?.focus();
        }
      } else {
        // Tab
        if (document.activeElement === lastElement) {
          event.preventDefault();
          firstElement?.focus();
        }
      }
    };

    container.addEventListener('keydown', handleKeyDown);
    container.setAttribute('data-focus-trap', 'true');

    // Set initial focus
    firstElement?.focus();
  }

  /**
   * Remove focus trap from container
   */
  private removeFocusTrap(container: HTMLElement): void {
    const handleKeyDown = container.getAttribute('data-focus-trap-handler');
    if (handleKeyDown) {
      container.removeEventListener('keydown', handleKeyDown as any);
    }
    container.removeAttribute('data-focus-trap');
    container.removeAttribute('data-focus-trap-handler');
  }

  /**
   * Get focusable elements within container
   */
  private getFocusableElements(container: HTMLElement): HTMLElement[] {
    const focusableSelectors = [
      'a[href]',
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]',
    ].join(', ');

    return Array.from(container.querySelectorAll(focusableSelectors))
      .filter(element => {
        const el = element as HTMLElement;
        return el.offsetWidth > 0 && el.offsetHeight > 0 && !el.hidden;
      }) as HTMLElement[];
  }

  /**
   * Check if element is focusable
   */
  isFocusable(element: HTMLElement): boolean {
    return this.getFocusableElements(document.body).includes(element);
  }
}

/**
 * Keyboard navigation utilities
 */
export class KeyboardNavigation {
  private keyHandlers: Map<string, (event: KeyboardEvent) => void> = new Map();

  /**
   * Add keyboard shortcut
   */
  addShortcut(
    key: string,
    handler: (event: KeyboardEvent) => void,
    options: {
      ctrl?: boolean;
      alt?: boolean;
      shift?: boolean;
      meta?: boolean;
    } = {}
  ): void {
    const shortcutKey = this.createShortcutKey(key, options);
    this.keyHandlers.set(shortcutKey, handler);
  }

  /**
   * Remove keyboard shortcut
   */
  removeShortcut(key: string, options: {
    ctrl?: boolean;
    alt?: boolean;
    shift?: boolean;
    meta?: boolean;
  } = {}): void {
    const shortcutKey = this.createShortcutKey(key, options);
    this.keyHandlers.delete(shortcutKey);
  }

  /**
   * Handle keyboard events
   */
  handleKeyDown(event: KeyboardEvent): void {
    const shortcutKey = this.createShortcutKey(event.key, {
      ctrl: event.ctrlKey,
      alt: event.altKey,
      shift: event.shiftKey,
      meta: event.metaKey,
    });

    const handler = this.keyHandlers.get(shortcutKey);
    if (handler) {
      event.preventDefault();
      handler(event);
    }
  }

  /**
   * Create shortcut key string
   */
  private createShortcutKey(key: string, options: {
    ctrl?: boolean;
    alt?: boolean;
    shift?: boolean;
    meta?: boolean;
  }): string {
    const modifiers = [];
    if (options.ctrl) modifiers.push('ctrl');
    if (options.alt) modifiers.push('alt');
    if (options.shift) modifiers.push('shift');
    if (options.meta) modifiers.push('meta');
    
    return [...modifiers, key.toLowerCase()].join('+');
  }

  /**
   * Setup arrow key navigation for container
   */
  setupArrowNavigation(
    container: HTMLElement,
    direction: 'horizontal' | 'vertical' | 'grid' = 'vertical'
  ): void {
    const handleKeyDown = (event: KeyboardEvent) => {
      const focusableElements = Array.from(
        container.querySelectorAll('[tabindex]:not([tabindex="-1"]), button, input, select, textarea, a[href]')
      ) as HTMLElement[];

      const currentIndex = focusableElements.indexOf(document.activeElement as HTMLElement);
      if (currentIndex === -1) return;

      let nextIndex = currentIndex;

      switch (direction) {
        case 'horizontal':
          if (event.key === 'ArrowLeft') nextIndex = Math.max(0, currentIndex - 1);
          if (event.key === 'ArrowRight') nextIndex = Math.min(focusableElements.length - 1, currentIndex + 1);
          break;

        case 'vertical':
          if (event.key === 'ArrowUp') nextIndex = Math.max(0, currentIndex - 1);
          if (event.key === 'ArrowDown') nextIndex = Math.min(focusableElements.length - 1, currentIndex + 1);
          break;

        case 'grid':
          // Implement grid navigation based on container layout
          // This would need more complex logic based on the actual grid structure
          break;
      }

      if (nextIndex !== currentIndex) {
        event.preventDefault();
        focusableElements[nextIndex]?.focus();
      }
    };

    container.addEventListener('keydown', handleKeyDown);
  }
}

/**
 * Color contrast utilities
 */
export class ColorContrast {
  /**
   * Calculate relative luminance
   */
  private getRelativeLuminance(color: string): number {
    const rgb = this.hexToRgb(color);
    if (!rgb) return 0;

    const [r, g, b] = rgb.map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });

    return 0.2126 * r! + 0.7152 * g! + 0.0722 * b!;
  }

  /**
   * Convert hex color to RGB
   */
  private hexToRgb(hex: string): [number, number, number] | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? [ 
      parseInt(result[1], 16),
      parseInt(result[2], 16),
      parseInt(result[3], 16)
    ] : null;
  }

  /**
   * Calculate contrast ratio between two colors
   */
  getContrastRatio(color1: string, color2: string): number {
    const lum1 = this.getRelativeLuminance(color1);
    const lum2 = this.getRelativeLuminance(color2);
    
    const lighter = Math.max(lum1, lum2);
    const darker = Math.min(lum1, lum2);
    
    return (lighter + 0.05) / (darker + 0.05);
  }

  /**
   * Check if contrast ratio meets WCAG standards
   */
  meetsWCAG(
    color1: string,
    color2: string,
    level: 'AA' | 'AAA' = 'AA',
    size: 'normal' | 'large' = 'normal'
  ): boolean {
    const ratio = this.getContrastRatio(color1, color2);
    
    const requirements = {
      AA: { normal: 4.5, large: 3 },
      AAA: { normal: 7, large: 4.5 },
    };
    
    return ratio >= requirements[level][size];
  }

  /**
   * Suggest accessible color alternatives
   */
  suggestAccessibleColor(
    foreground: string,
    background: string,
    level: 'AA' | 'AAA' = 'AA'
  ): string {
    const targetRatio = level === 'AA' ? 4.5 : 7;
    const currentRatio = this.getContrastRatio(foreground, background);
    
    if (currentRatio >= targetRatio) return foreground;
    
    // Simple approach: darken or lighten the foreground color
    const rgb = this.hexToRgb(foreground);
    if (!rgb) return foreground;
    
    let [r, g, b] = rgb;
    const step = currentRatio < targetRatio ? -10 : 10;
    
    while (this.getContrastRatio(this.rgbToHex(r, g, b), background) < targetRatio) {
      r = Math.max(0, Math.min(255, r + step));
      g = Math.max(0, Math.min(255, g + step));
      b = Math.max(0, Math.min(255, b + step));
      
      if ((r === 0 && g === 0 && b === 0) || (r === 255 && g === 255 && b === 255)) {
        break;
      }
    }
    
    return this.rgbToHex(r, g, b);
  }

  /**
   * Convert RGB to hex
   */
  private rgbToHex(r: number, g: number, b: number): string {
    return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
  }
}

/**
 * Motion preferences utilities
 */
export class MotionPreferences {
  /**
   * Check if user prefers reduced motion
   */
  prefersReducedMotion(): boolean {
    if (typeof window === 'undefined') return false;
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  }

  /**
   * Apply motion preferences to element
   */
  applyMotionPreferences(element: HTMLElement): void {
    if (this.prefersReducedMotion()) {
      element.style.animation = 'none';
      element.style.transition = 'none';
    }
  }

  /**
   * Get safe animation duration
   */
  getSafeAnimationDuration(defaultDuration: number): number {
    return this.prefersReducedMotion() ? 0 : defaultDuration;
  }
}

// Global instances
export const screenReaderAnnouncer = new ScreenReaderAnnouncer();
export const focusManager = new FocusManager();
export const keyboardNavigation = new KeyboardNavigation();
export const colorContrast = new ColorContrast();
export const motionPreferences = new MotionPreferences();

/**
 * Initialize accessibility features
 */
export function initializeA11y(config: Partial<A11yConfig> = {}): void {
  const mergedConfig = { ...defaultA11yConfig, ...config };

  if (typeof window === 'undefined') return;

  // Setup keyboard navigation
  if (mergedConfig.enableKeyboardNavigation) {
    document.addEventListener('keydown', (event) => {
      keyboardNavigation.handleKeyDown(event);
    });
  }

  // Setup skip links
  setupSkipLinks();

  // Setup focus indicators
  setupFocusIndicators();

  // Setup reduced motion
  if (mergedConfig.enableReducedMotion) {
    setupReducedMotion();
  }
}

/**
 * Setup skip links for keyboard navigation
 */
function setupSkipLinks(): void {
  const skipLink = document.createElement('a');
  skipLink.href = '#main-content';
  skipLink.textContent = 'Skip to main content';
  skipLink.className = 'sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-primary-600 focus:text-white focus:rounded';
  
  document.body.insertBefore(skipLink, document.body.firstChild);
}

/**
 * Setup focus indicators
 */
function setupFocusIndicators(): void {
  const style = document.createElement('style');
  style.textContent = `
    .focus-visible {
      outline: 2px solid #3b82f6;
      outline-offset: 2px;
    }
    
    .focus-visible:not(.focus-visible-force) {
      outline: none;
    }
    
    .focus-visible-force {
      outline: 2px solid #3b82f6 !important;
      outline-offset: 2px !important;
    }
  `;
  document.head.appendChild(style);
}

/**
 * Setup reduced motion preferences
 */
function setupReducedMotion(): void {
  if (motionPreferences.prefersReducedMotion()) {
    const style = document.createElement('style');
    style.textContent = `
      *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
      }
    `;
    document.head.appendChild(style);
  }
}
