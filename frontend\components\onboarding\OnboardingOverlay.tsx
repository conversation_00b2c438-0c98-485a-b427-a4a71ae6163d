'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { OnboardingEngine, OnboardingStep, OnboardingFlow } from '@/lib/onboarding/onboarding-engine';
import { useAppStore } from '@/lib/store';
import {
  XMarkIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  PlayIcon,
  PauseIcon,
  CheckIcon,
  LightBulbIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';

interface OnboardingOverlayProps {
  engine: OnboardingEngine;
  onComplete?: (flowId: string) => void;
  onSkip?: () => void;
  onClose?: () => void;
  className?: string;
}

export function OnboardingOverlay({
  engine,
  onComplete,
  onSkip,
  onClose,
  className,
}: OnboardingOverlayProps) {
  const [currentStep, setCurrentStep] = useState<OnboardingStep | null>(null);
  const [currentFlow, setCurrentFlow] = useState<OnboardingFlow | null>(null);
  const [progress, setProgress] = useState({ current: 0, total: 0, percentage: 0 });
  const [isPlaying, setIsPlaying] = useState(true);
  const [showTips, setShowTips] = useState(true);
  const [stepPosition, setStepPosition] = useState<{ x: number; y: number } | null>(null);

  const overlayRef = useRef<HTMLDivElement>(null);
  const { addNotification } = useAppStore();

  useEffect(() => {
    // Set up engine event listeners
    const handleStepShown = (data: { step: OnboardingStep }) => {
      setCurrentStep(data.step);
      updateProgress();
      calculateStepPosition(data.step);
    };

    const handleStepCompleted = (data: { step: OnboardingStep }) => {
      addNotification({
        type: 'success',
        title: 'Step Complete',
        message: data.step.title,
      });
      updateProgress();
    };

    const handleFlowCompleted = (data: { flowId: string; flow: OnboardingFlow }) => {
      setCurrentFlow(data.flow);
      addNotification({
        type: 'success',
        title: 'Tutorial Complete!',
        message: `You've completed "${data.flow.name}"`,
      });
      onComplete?.(data.flowId);
    };

    const handleFlowStarted = (data: { flowId: string; flow: OnboardingFlow }) => {
      setCurrentFlow(data.flow);
      updateProgress();
    };

    engine.on('step_shown', handleStepShown);
    engine.on('step_completed', handleStepCompleted);
    engine.on('flow_completed', handleFlowCompleted);
    engine.on('flow_started', handleFlowStarted);

    // Initial state
    updateProgress();
    const step = engine.getCurrentStep();
    if (step) {
      setCurrentStep(step);
      calculateStepPosition(step);
    }

    return () => {
      engine.off('step_shown', handleStepShown);
      engine.off('step_completed', handleStepCompleted);
      engine.off('flow_completed', handleFlowCompleted);
      engine.off('flow_started', handleFlowStarted);
    };
  }, [engine, onComplete, addNotification]);

  const updateProgress = () => {
    const newProgress = engine.getProgress();
    setProgress(newProgress);
  };

  const calculateStepPosition = (step: OnboardingStep) => {
    if (!step.target) {
      setStepPosition(null);
      return;
    }

    const targetElement = document.querySelector(step.target);
    if (!targetElement) {
      setStepPosition(null);
      return;
    }

    const rect = targetElement.getBoundingClientRect();
    const position = step.position || 'bottom';

    let x = rect.left + rect.width / 2;
    let y = rect.bottom + 20;

    switch (position) {
      case 'top':
        y = rect.top - 20;
        break;
      case 'left':
        x = rect.left - 20;
        y = rect.top + rect.height / 2;
        break;
      case 'right':
        x = rect.right + 20;
        y = rect.top + rect.height / 2;
        break;
      case 'center':
        x = window.innerWidth / 2;
        y = window.innerHeight / 2;
        break;
    }

    // Ensure tooltip stays within viewport
    x = Math.max(20, Math.min(x, window.innerWidth - 320));
    y = Math.max(20, Math.min(y, window.innerHeight - 200));

    setStepPosition({ x, y });
  };

  const handleNext = () => {
    if (engine.nextStep()) {
      updateProgress();
    }
  };

  const handlePrevious = () => {
    if (engine.previousStep()) {
      updateProgress();
    }
  };

  const handleSkip = () => {
    if (currentStep?.skippable) {
      engine.skipStep();
      updateProgress();
    } else {
      onSkip?.();
    }
  };

  const handlePause = () => {
    if (isPlaying) {
      engine.pauseFlow();
      setIsPlaying(false);
    } else {
      engine.resumeFlow();
      setIsPlaying(true);
    }
  };

  const handleClose = () => {
    engine.stopFlow();
    onClose?.();
  };

  const getStepTypeIcon = (type: OnboardingStep['type']) => {
    switch (type) {
      case 'intro': return <PlayIcon className="h-4 w-4" />;
      case 'action': return <CheckIcon className="h-4 w-4" />;
      case 'explanation': return <LightBulbIcon className="h-4 w-4" />;
      case 'practice': return <PlayIcon className="h-4 w-4" />;
      case 'completion': return <CheckIcon className="h-4 w-4" />;
      default: return <LightBulbIcon className="h-4 w-4" />;
    }
  };

  const getStepTypeColor = (type: OnboardingStep['type']) => {
    switch (type) {
      case 'intro': return 'info';
      case 'action': return 'warning';
      case 'explanation': return 'secondary';
      case 'practice': return 'success';
      case 'completion': return 'success';
      default: return 'secondary';
    }
  };

  if (!currentStep || !currentFlow) {
    return null;
  }

  return (
    <>
      {/* Backdrop */}
      <div className="fixed inset-0 bg-black/50 z-40" />

      {/* Step Tooltip */}
      {stepPosition && (
        <Card
          ref={overlayRef}
          className="fixed z-50 w-80 shadow-xl border-2 border-primary-200"
          style={{
            left: stepPosition.x - 160, // Center the 320px wide card
            top: stepPosition.y,
            transform: currentStep.position === 'center' ? 'translate(-50%, -50%)' : undefined,
          }}
        >
          <CardContent className="p-6">
            {/* Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-2">
                <Badge variant={getStepTypeColor(currentStep.type)} size="sm">
                  <span className="flex items-center space-x-1">
                    {getStepTypeIcon(currentStep.type)}
                    <span className="capitalize">{currentStep.type}</span>
                  </span>
                </Badge>
                <Badge variant="outline" size="sm">
                  {progress.current + 1} of {progress.total}
                </Badge>
              </div>
              
              <div className="flex items-center space-x-1">
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={handlePause}
                  leftIcon={isPlaying ? <PauseIcon className="h-3 w-3" /> : <PlayIcon className="h-3 w-3" />}
                />
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={handleClose}
                  leftIcon={<XMarkIcon className="h-3 w-3" />}
                />
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mb-4">
              <div className="flex items-center justify-between text-xs text-gray-600 mb-1">
                <span>{currentFlow.name}</span>
                <span>{progress.percentage}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress.percentage}%` }}
                />
              </div>
            </div>

            {/* Content */}
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold text-lg text-gray-900 mb-2">
                  {currentStep.title}
                </h3>
                <p className="text-sm text-gray-600 mb-3">
                  {currentStep.description}
                </p>
                <div className="text-sm text-gray-700">
                  {currentStep.content.text}
                </div>
              </div>

              {/* Media */}
              {currentStep.content.media && (
                <div className="rounded-lg overflow-hidden">
                  {currentStep.content.media.type === 'image' && (
                    <img
                      src={currentStep.content.media.url}
                      alt={currentStep.content.media.alt}
                      className="w-full h-auto"
                    />
                  )}
                  {currentStep.content.media.type === 'video' && (
                    <video
                      src={currentStep.content.media.url}
                      controls
                      className="w-full h-auto"
                    />
                  )}
                  {currentStep.content.media.type === 'gif' && (
                    <img
                      src={currentStep.content.media.url}
                      alt={currentStep.content.media.alt}
                      className="w-full h-auto"
                    />
                  )}
                </div>
              )}

              {/* Tips */}
              {showTips && currentStep.content.tips && currentStep.content.tips.length > 0 && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <div className="flex items-center space-x-2 mb-2">
                    <LightBulbIcon className="h-4 w-4 text-blue-600" />
                    <span className="font-medium text-blue-900 text-sm">Tips</span>
                  </div>
                  <ul className="text-xs text-blue-800 space-y-1">
                    {currentStep.content.tips.map((tip, index) => (
                      <li key={index} className="flex items-start space-x-1">
                        <span>•</span>
                        <span>{tip}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Actions */}
              {currentStep.content.actions && currentStep.content.actions.length > 0 && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <h4 className="font-medium text-yellow-900 text-sm mb-2">Action Required</h4>
                  <div className="space-y-2">
                    {currentStep.content.actions.map((action, index) => (
                      <div key={index} className="text-xs text-yellow-800">
                        <span className="font-medium">{index + 1}.</span> {action.description}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Navigation */}
            <div className="flex items-center justify-between pt-4 border-t border-gray-200">
              <div className="flex items-center space-x-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handlePrevious}
                  disabled={progress.current === 0}
                  leftIcon={<ChevronLeftIcon className="h-4 w-4" />}
                >
                  Previous
                </Button>
                
                {currentStep.skippable && (
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={handleSkip}
                  >
                    Skip
                  </Button>
                )}
              </div>

              <div className="flex items-center space-x-2">
                {currentFlow.estimatedTime && (
                  <div className="flex items-center space-x-1 text-xs text-gray-500">
                    <ClockIcon className="h-3 w-3" />
                    <span>{currentFlow.estimatedTime}</span>
                  </div>
                )}
                
                <Button
                  size="sm"
                  onClick={handleNext}
                  disabled={currentStep.content.nextButton?.disabled}
                  rightIcon={<ChevronRightIcon className="h-4 w-4" />}
                >
                  {currentStep.content.nextButton?.text || 
                   (progress.current === progress.total - 1 ? 'Complete' : 'Next')}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Floating Controls */}
      <div className="fixed bottom-6 right-6 z-50">
        <Card className="p-3">
          <div className="flex items-center space-x-2">
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setShowTips(!showTips)}
              leftIcon={<LightBulbIcon className="h-4 w-4" />}
            >
              {showTips ? 'Hide' : 'Show'} Tips
            </Button>
            
            <div className="w-px h-6 bg-gray-300" />
            
            <Button
              size="sm"
              variant="ghost"
              onClick={handleClose}
            >
              Exit Tutorial
            </Button>
          </div>
        </Card>
      </div>
    </>
  );
}
