# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

- Nothing yet!

## [0.1.1] - 2023-03-31

### Fixed

- Ensure types are published to npm ([#11](https://github.com/tailwindlabs/tailwindcss-container-queries/pull/11))

## [0.1.0] - 2022-10-19

### Added

- Add new `@container`, `@container-normal` utilities.
- Add new `@xs`, `@sm`, ..., `@[400px]`, variants.

[unreleased]: https://github.com/tailwindlabs/tailwindcss-container-queries/compare/v0.1.1...HEAD
[0.1.1]: https://github.com/tailwindlabs/tailwindcss-container-queries/compare/v0.1.0...v0.1.1
[0.1.0]: https://github.com/tailwindlabs/tailwindcss-container-queries/releases/tag/v0.1.0
