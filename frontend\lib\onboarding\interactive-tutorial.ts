import { OnboardingFlow, OnboardingStep } from './onboarding-engine';
import { CanvasNode } from '@/lib/visual-builder/canvas';
import { apiClient } from '@/lib/api';

export interface TutorialAgent {
  id: string;
  name: string;
  description: string;
  config: Record<string, any>;
  nodes: CanvasNode[];
  connections: Array<{
    sourceNodeId: string;
    sourceOutputId: string;
    targetNodeId: string;
    targetInputId: string;
  }>;
  testCases: Array<{
    input: string;
    expectedOutput?: string;
    description: string;
  }>;
}

export interface TutorialProgress {
  currentStep: number;
  agentData: Partial<TutorialAgent>;
  userInputs: Record<string, any>;
  validationResults: Record<string, boolean>;
  testResults: Array<{
    testCase: number;
    passed: boolean;
    output?: string;
    error?: string;
  }>;
}

class InteractiveTutorial {
  private progress: TutorialProgress;
  private callbacks: {
    onStepComplete?: (step: number, data: any) => void;
    onAgentCreated?: (agent: TutorialAgent) => void;
    onTestComplete?: (results: any) => void;
  } = {};

  constructor() {
    this.progress = {
      currentStep: 0,
      agentData: {},
      userInputs: {},
      validationResults: {},
      testResults: [],
    };
  }

  /**
   * Get the complete interactive tutorial flow
   */
  getTutorialFlow(): OnboardingFlow {
    return {
      id: 'interactive_agent_tutorial',
      name: 'Build Your First AI Agent',
      description: 'Create a working customer support agent step by step',
      category: 'getting_started',
      estimatedTime: '15 minutes',
      difficulty: 'beginner',
      steps: [
        this.getWelcomeStep(),
        this.getAgentNameStep(),
        this.getAgentPurposeStep(),
        this.getSystemPromptStep(),
        this.getModelSelectionStep(),
        this.getParameterTuningStep(),
        this.getCanvasIntroStep(),
        this.getNodeCreationStep(),
        this.getConnectionStep(),
        this.getTestingStep(),
        this.getDeploymentStep(),
        this.getCelebrationStep(),
      ],
      completion: {
        badge: 'Agent Builder',
        certificate: 'First AI Agent',
        unlocks: ['advanced_agents', 'tool_integration'],
        nextFlows: ['tool_integration_tutorial'],
      },
    };
  }

  /**
   * Set tutorial callbacks
   */
  setCallbacks(callbacks: typeof this.callbacks): void {
    this.callbacks = callbacks;
  }

  /**
   * Validate step completion
   */
  async validateStep(stepId: string, data: any): Promise<boolean> {
    switch (stepId) {
      case 'agent_name':
        return this.validateAgentName(data.name);
      case 'agent_purpose':
        return this.validateAgentPurpose(data.purpose);
      case 'system_prompt':
        return this.validateSystemPrompt(data.prompt);
      case 'model_selection':
        return this.validateModelSelection(data.model);
      case 'parameter_tuning':
        return this.validateParameters(data.parameters);
      case 'node_creation':
        return this.validateNodeCreation(data.nodes);
      case 'connection':
        return this.validateConnections(data.connections);
      case 'testing':
        return await this.validateTesting(data.testInputs);
      default:
        return true;
    }
  }

  /**
   * Execute step action
   */
  async executeStepAction(stepId: string, data: any): Promise<any> {
    switch (stepId) {
      case 'agent_name':
        return this.setAgentName(data.name);
      case 'agent_purpose':
        return this.setAgentPurpose(data.purpose);
      case 'system_prompt':
        return this.setSystemPrompt(data.prompt);
      case 'model_selection':
        return this.setModel(data.model);
      case 'parameter_tuning':
        return this.setParameters(data.parameters);
      case 'node_creation':
        return this.createNodes(data.nodes);
      case 'connection':
        return this.createConnections(data.connections);
      case 'testing':
        return await this.testAgent(data.testInputs);
      case 'deployment':
        return await this.deployAgent();
      default:
        return {};
    }
  }

  /**
   * Get current tutorial progress
   */
  getProgress(): TutorialProgress {
    return { ...this.progress };
  }

  /**
   * Reset tutorial progress
   */
  reset(): void {
    this.progress = {
      currentStep: 0,
      agentData: {},
      userInputs: {},
      validationResults: {},
      testResults: [],
    };
  }

  /**
   * Individual step definitions
   */
  private getWelcomeStep(): OnboardingStep {
    return {
      id: 'welcome',
      title: 'Welcome to Agent Building!',
      description: 'Let\'s create your first AI agent together',
      type: 'intro',
      content: {
        text: 'In this tutorial, you\'ll build a real customer support agent that can answer questions, help with orders, and escalate complex issues. By the end, you\'ll have a working agent you can actually use!',
        media: {
          type: 'gif',
          url: '/tutorials/agent-preview.gif',
          alt: 'Preview of the agent we\'ll build',
        },
        tips: [
          'This tutorial takes about 15 minutes',
          'You can pause and resume anytime',
          'The agent you build will be saved to your account',
        ],
        nextButton: { text: 'Let\'s Build!' },
      },
      skippable: false,
    };
  }

  private getAgentNameStep(): OnboardingStep {
    return {
      id: 'agent_name',
      title: 'Name Your Agent',
      description: 'Give your agent a memorable name',
      type: 'action',
      target: '#agent-name-input',
      content: {
        text: 'Every great agent needs a great name! This will help you identify your agent in your dashboard and when sharing with others.',
        actions: [
          {
            type: 'input',
            target: '#agent-name-input',
            description: 'Type a name for your agent (e.g., "Support Helper", "Order Assistant")',
          },
        ],
        tips: [
          'Choose something descriptive and memorable',
          'You can always change this later',
          'Avoid special characters',
        ],
        nextButton: { text: 'Set Name', disabled: true },
      },
      validation: {
        type: 'custom',
        validator: () => {
          const input = document.querySelector('#agent-name-input') as HTMLInputElement;
          return input?.value?.length >= 3;
        },
      },
      skippable: false,
    };
  }

  private getAgentPurposeStep(): OnboardingStep {
    return {
      id: 'agent_purpose',
      title: 'Define the Purpose',
      description: 'What will your agent help with?',
      type: 'action',
      target: '#agent-purpose-input',
      content: {
        text: 'Describe what your agent should do. This helps the AI understand its role and respond appropriately to different situations.',
        actions: [
          {
            type: 'input',
            target: '#agent-purpose-input',
            description: 'Describe your agent\'s main purpose (e.g., "Help customers with order questions and product information")',
          },
        ],
        tips: [
          'Be specific about the main tasks',
          'Think about your target users',
          'This will guide the AI\'s behavior',
        ],
        nextButton: { text: 'Set Purpose', disabled: true },
      },
      validation: {
        type: 'custom',
        validator: () => {
          const input = document.querySelector('#agent-purpose-input') as HTMLInputElement;
          return input?.value?.length >= 10;
        },
      },
      skippable: false,
    };
  }

  private getSystemPromptStep(): OnboardingStep {
    return {
      id: 'system_prompt',
      title: 'Craft the System Prompt',
      description: 'This is your agent\'s personality and instructions',
      type: 'action',
      target: '#system-prompt-textarea',
      content: {
        text: 'The system prompt is like giving your agent a job description and personality guide. It tells the AI how to behave, what tone to use, and what it should or shouldn\'t do.',
        actions: [
          {
            type: 'input',
            target: '#system-prompt-textarea',
            description: 'Write instructions for your agent\'s behavior and personality',
          },
        ],
        tips: [
          'Be clear about the agent\'s role',
          'Specify the tone (friendly, professional, etc.)',
          'Include any important guidelines or restrictions',
        ],
        nextButton: { text: 'Set Instructions', disabled: true },
      },
      validation: {
        type: 'custom',
        validator: () => {
          const textarea = document.querySelector('#system-prompt-textarea') as HTMLTextAreaElement;
          return textarea?.value?.length >= 50;
        },
      },
      skippable: false,
    };
  }

  private getModelSelectionStep(): OnboardingStep {
    return {
      id: 'model_selection',
      title: 'Choose AI Model',
      description: 'Select the brain for your agent',
      type: 'action',
      target: '#model-select',
      content: {
        text: 'Different AI models have different strengths. GPT-4 is great for complex reasoning, while GPT-3.5 is faster and more cost-effective for simpler tasks.',
        actions: [
          {
            type: 'click',
            target: '#model-select',
            description: 'Choose an AI model from the dropdown',
          },
        ],
        tips: [
          'GPT-4: Best for complex tasks, slower, more expensive',
          'GPT-3.5: Good for simple tasks, faster, cheaper',
          'You can change this later based on performance',
        ],
        nextButton: { text: 'Select Model', disabled: true },
      },
      validation: {
        type: 'custom',
        validator: () => {
          const select = document.querySelector('#model-select') as HTMLSelectElement;
          return select?.value !== '';
        },
      },
      skippable: false,
    };
  }

  private getParameterTuningStep(): OnboardingStep {
    return {
      id: 'parameter_tuning',
      title: 'Tune Parameters',
      description: 'Fine-tune your agent\'s behavior',
      type: 'action',
      target: '#parameters-section',
      content: {
        text: 'These parameters control how creative or focused your agent is. Temperature affects creativity, while max tokens limits response length.',
        actions: [
          {
            type: 'input',
            target: '#temperature-slider',
            description: 'Adjust the temperature slider (0.7 is a good starting point)',
          },
          {
            type: 'input',
            target: '#max-tokens-input',
            description: 'Set maximum response length (500-1000 tokens is typical)',
          },
        ],
        tips: [
          'Temperature 0.1-0.3: Very focused and consistent',
          'Temperature 0.7-0.9: More creative and varied',
          'Max tokens: 1 token ≈ 0.75 words',
        ],
        nextButton: { text: 'Set Parameters' },
      },
      skippable: true,
    };
  }

  private getCanvasIntroStep(): OnboardingStep {
    return {
      id: 'canvas_intro',
      title: 'Meet the Visual Canvas',
      description: 'This is where you build workflows',
      type: 'explanation',
      target: '.visual-canvas',
      position: 'center',
      content: {
        text: 'Now we\'ll create your agent visually! The canvas lets you drag and drop components to build complex AI workflows. We\'ll start simple with just one agent node.',
        tips: [
          'Each box is a "node" that does something',
          'Lines connect nodes to pass data between them',
          'You can zoom and pan to navigate',
        ],
        nextButton: { text: 'Start Building!' },
      },
      skippable: false,
    };
  }

  private getNodeCreationStep(): OnboardingStep {
    return {
      id: 'node_creation',
      title: 'Create Your Agent Node',
      description: 'Add your agent to the canvas',
      type: 'action',
      target: '.component-palette',
      content: {
        text: 'Find the "GPT Agent" component in the palette and drag it onto the canvas. This will create a node with all the settings you\'ve configured.',
        actions: [
          {
            type: 'drag',
            target: '[data-component="gpt-agent"]',
            description: 'Drag the GPT Agent component from the palette to the canvas',
          },
        ],
        tips: [
          'Look for the robot icon 🤖',
          'Drag it anywhere on the canvas',
          'The node will appear with your settings',
        ],
        nextButton: { text: 'Node Created!', disabled: true },
      },
      validation: {
        type: 'custom',
        validator: () => {
          return document.querySelectorAll('.canvas-node[data-type="agent"]').length > 0;
        },
      },
      skippable: false,
    };
  }

  private getConnectionStep(): OnboardingStep {
    return {
      id: 'connection',
      title: 'Add Input and Output',
      description: 'Connect your agent to handle requests',
      type: 'action',
      target: '.visual-canvas',
      content: {
        text: 'Every agent needs input (user messages) and output (responses). We\'ll add trigger and output nodes to complete the workflow.',
        actions: [
          {
            type: 'drag',
            target: '[data-component="input-trigger"]',
            description: 'Add an Input Trigger node',
          },
          {
            type: 'drag',
            target: '[data-component="output-display"]',
            description: 'Add an Output Display node',
          },
          {
            type: 'click',
            description: 'Connect the nodes by clicking and dragging between the connection points',
          },
        ],
        tips: [
          'Input Trigger: Where user messages come in',
          'Output Display: Where responses go out',
          'Click the small circles to connect nodes',
        ],
        nextButton: { text: 'Connections Made!', disabled: true },
      },
      validation: {
        type: 'custom',
        validator: () => {
          return document.querySelectorAll('.canvas-connection').length >= 2;
        },
      },
      skippable: false,
    };
  }

  private getTestingStep(): OnboardingStep {
    return {
      id: 'testing',
      title: 'Test Your Agent',
      description: 'Let\'s see your agent in action!',
      type: 'practice',
      target: '#test-panel',
      content: {
        text: 'Time to test your creation! We\'ll send some sample messages to see how your agent responds. This helps you refine the behavior before going live.',
        actions: [
          {
            type: 'click',
            target: '#test-button',
            description: 'Click "Test Agent" to run the first test',
          },
          {
            type: 'wait',
            description: 'Watch the agent process the request and generate a response',
          },
        ],
        tips: [
          'The first test might take a few seconds',
          'Look for helpful, relevant responses',
          'We\'ll try multiple test cases',
        ],
        nextButton: { text: 'Run Tests', disabled: true },
      },
      validation: {
        type: 'custom',
        validator: () => {
          return this.progress.testResults.length > 0;
        },
      },
      skippable: false,
    };
  }

  private getDeploymentStep(): OnboardingStep {
    return {
      id: 'deployment',
      title: 'Deploy Your Agent',
      description: 'Make your agent available for use',
      type: 'action',
      target: '#deploy-button',
      content: {
        text: 'Congratulations! Your agent is working well. Now let\'s deploy it so you can use it in real applications or share it with others.',
        actions: [
          {
            type: 'click',
            target: '#deploy-button',
            description: 'Click "Deploy Agent" to make it live',
          },
        ],
        tips: [
          'Deployed agents get a unique URL',
          'You can embed them in websites',
          'Monitor usage in the dashboard',
        ],
        nextButton: { text: 'Deploy Now!' },
      },
      skippable: false,
    };
  }

  private getCelebrationStep(): OnboardingStep {
    return {
      id: 'celebration',
      title: '🎉 Congratulations!',
      description: 'You\'ve built your first AI agent!',
      type: 'completion',
      content: {
        text: 'Amazing work! You\'ve successfully created, tested, and deployed your first AI agent. You now have a working customer support agent that can help users with their questions.',
        media: {
          type: 'gif',
          url: '/tutorials/celebration.gif',
          alt: 'Celebration animation',
        },
        tips: [
          'Your agent is now live and ready to use',
          'Check the dashboard to monitor its performance',
          'Try building more complex workflows next',
        ],
        nextButton: { text: 'View My Agent' },
      },
      skippable: false,
    };
  }

  /**
   * Validation methods
   */
  private validateAgentName(name: string): boolean {
    return name && name.length >= 3 && name.length <= 50;
  }

  private validateAgentPurpose(purpose: string): boolean {
    return purpose && purpose.length >= 10 && purpose.length <= 200;
  }

  private validateSystemPrompt(prompt: string): boolean {
    return prompt && prompt.length >= 50 && prompt.length <= 1000;
  }

  private validateModelSelection(model: string): boolean {
    const validModels = ['gpt-4', 'gpt-3.5-turbo', 'claude-3-sonnet'];
    return validModels.includes(model);
  }

  private validateParameters(parameters: any): boolean {
    return (
      parameters.temperature >= 0 &&
      parameters.temperature <= 1 &&
      parameters.maxTokens >= 100 &&
      parameters.maxTokens <= 2000
    );
  }

  private validateNodeCreation(nodes: any[]): boolean {
    return nodes && nodes.length >= 1;
  }

  private validateConnections(connections: any[]): boolean {
    return connections && connections.length >= 2;
  }

  private async validateTesting(testInputs: string[]): Promise<boolean> {
    return testInputs && testInputs.length > 0;
  }

  /**
   * Action methods
   */
  private setAgentName(name: string): any {
    this.progress.agentData.name = name;
    this.progress.userInputs.name = name;
    return { success: true, name };
  }

  private setAgentPurpose(purpose: string): any {
    this.progress.agentData.description = purpose;
    this.progress.userInputs.purpose = purpose;
    return { success: true, purpose };
  }

  private setSystemPrompt(prompt: string): any {
    if (!this.progress.agentData.config) {
      this.progress.agentData.config = {};
    }
    this.progress.agentData.config.systemPrompt = prompt;
    this.progress.userInputs.systemPrompt = prompt;
    return { success: true, prompt };
  }

  private setModel(model: string): any {
    if (!this.progress.agentData.config) {
      this.progress.agentData.config = {};
    }
    this.progress.agentData.config.model = model;
    this.progress.userInputs.model = model;
    return { success: true, model };
  }

  private setParameters(parameters: any): any {
    if (!this.progress.agentData.config) {
      this.progress.agentData.config = {};
    }
    Object.assign(this.progress.agentData.config, parameters);
    this.progress.userInputs.parameters = parameters;
    return { success: true, parameters };
  }

  private createNodes(nodes: any[]): any {
    this.progress.agentData.nodes = nodes;
    return { success: true, nodes };
  }

  private createConnections(connections: any[]): any {
    this.progress.agentData.connections = connections;
    return { success: true, connections };
  }

  private async testAgent(testInputs: string[]): Promise<any> {
    try {
      const results = [];
      
      for (let i = 0; i < testInputs.length; i++) {
        const input = testInputs[i];
        
        // Simulate API call to test the agent
        const response = await apiClient.post('/api/v1/agents/test', {
          config: this.progress.agentData.config,
          input,
        });
        
        results.push({
          testCase: i,
          passed: response.data.success,
          output: response.data.output,
          error: response.data.error,
        });
      }
      
      this.progress.testResults = results;
      this.callbacks.onTestComplete?.(results);
      
      return { success: true, results };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  private async deployAgent(): Promise<any> {
    try {
      const response = await apiClient.post('/api/v1/agents', {
        name: this.progress.agentData.name,
        description: this.progress.agentData.description,
        config: this.progress.agentData.config,
        nodes: this.progress.agentData.nodes,
        connections: this.progress.agentData.connections,
      });
      
      const agent: TutorialAgent = {
        id: response.data.id,
        name: this.progress.agentData.name!,
        description: this.progress.agentData.description!,
        config: this.progress.agentData.config!,
        nodes: this.progress.agentData.nodes!,
        connections: this.progress.agentData.connections!,
        testCases: [],
      };
      
      this.callbacks.onAgentCreated?.(agent);
      
      return { success: true, agent };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
}

export { InteractiveTutorial };
