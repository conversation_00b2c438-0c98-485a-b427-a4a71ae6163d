"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/signup/page",{

/***/ "(app-pages-browser)/./hooks/useAuth.ts":
/*!**************************!*\
  !*** ./hooks/useAuth.ts ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/../node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ useAuth auto */ \n\n\nfunction useAuth() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_0__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const login = async (credentials)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            const result = await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_0__.signIn)(\"credentials\", {\n                email: credentials.email,\n                password: credentials.password,\n                organizationId: credentials.organizationId,\n                redirect: false\n            });\n            if (result === null || result === void 0 ? void 0 : result.error) {\n                setError(\"Invalid credentials\");\n                return false;\n            }\n            router.push(\"/dashboard\");\n            return true;\n        } catch (err) {\n            setError(\"Login failed\");\n            return false;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const register = async (data)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            // For demo purposes, simulate registration validation\n            if (!data.email || !data.password || !data.firstName || !data.lastName) {\n                setError(\"All fields are required\");\n                return false;\n            }\n            if (data.password.length < 8) {\n                setError(\"Password must be at least 8 characters\");\n                return false;\n            }\n            // Simulate successful registration by storing user data locally\n            // In a real app, this would be sent to your backend\n            const userData = {\n                email: data.email,\n                firstName: data.firstName,\n                lastName: data.lastName,\n                organizationId: data.organizationId,\n                role: data.role || \"user\",\n                registeredAt: new Date().toISOString()\n            };\n            // Store in localStorage for demo (in production, this would be in your database)\n            localStorage.setItem(\"user_\".concat(data.email), JSON.stringify(userData));\n            // Auto-login after successful registration\n            const result = await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_0__.signIn)(\"credentials\", {\n                email: data.email,\n                password: data.password,\n                redirect: false\n            });\n            if (result === null || result === void 0 ? void 0 : result.error) {\n                setError(\"Registration successful, but login failed. Please try signing in.\");\n                return false;\n            }\n            if (result === null || result === void 0 ? void 0 : result.ok) {\n                router.push(\"/dashboard\");\n                return true;\n            }\n            setError(\"Registration failed\");\n            return false;\n        } catch (err) {\n            setError(\"Registration failed\");\n            return false;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = async ()=>{\n        setIsLoading(true);\n        try {\n            await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_0__.signOut)({\n                redirect: false\n            });\n            router.push(\"/auth/signin\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const clearError = ()=>setError(null);\n    return {\n        user: session === null || session === void 0 ? void 0 : session.user,\n        accessToken: session === null || session === void 0 ? void 0 : session.accessToken,\n        isAuthenticated: !!session,\n        isLoading: status === \"loading\" || isLoading,\n        error,\n        login,\n        register,\n        logout,\n        clearError\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/useAuth.ts\n"));

/***/ })

});