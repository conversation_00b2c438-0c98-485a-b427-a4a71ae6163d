// Export all stores from a single entry point
export { useAuthStore } from './auth.store';
export { useUIStore } from './ui.store';
export { useAppStore } from './app.store';

// Export store types for external use
// Note: These types are not exported from the store files, so we'll define them here if needed

// Store utilities
export const clearAllStores = () => {
  // Clear all persisted store data
  localStorage.removeItem('synapseai-auth-storage');
  localStorage.removeItem('synapseai-ui-storage');
  localStorage.removeItem('synapseai-app-storage');
  
  // Reload the page to reset all stores
  if (typeof window !== 'undefined') {
    window.location.reload();
  }
};

// Store hydration utility for SSR
export const hydrateStores = () => {
  // This function can be called on the client side to ensure
  // stores are properly hydrated after SSR
  if (typeof window !== 'undefined') {
    // Stores will automatically hydrate from localStorage
    // when they are first accessed
  }
};
