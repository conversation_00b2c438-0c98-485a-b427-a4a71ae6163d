"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OidcStrategy = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const passport_openidconnect_1 = require("passport-openidconnect");
const config_1 = require("@nestjs/config");
let OidcStrategy = class OidcStrategy extends (0, passport_1.PassportStrategy)(passport_openidconnect_1.Strategy, 'oidc') {
    constructor(configService) {
        super({
            issuer: configService.get('OIDC_ISSUER'),
            clientID: configService.get('OIDC_CLIENT_ID'),
            clientSecret: configService.get('OIDC_CLIENT_SECRET'),
            callbackURL: configService.get('OIDC_CALLBACK_URL'),
            scope: ['openid', 'profile', 'email'],
        });
        this.configService = configService;
    }
    async validate(issuer, profile, context, idToken, accessToken, refreshToken, done) {
        const user = {
            email: profile.emails?.[0]?.value || profile.email,
            firstName: profile.name?.givenName || profile.given_name,
            lastName: profile.name?.familyName || profile.family_name,
            groups: profile.groups || [],
            attributes: {
                ...profile,
                idToken,
                accessToken,
            },
        };
        return user;
    }
};
exports.OidcStrategy = OidcStrategy;
exports.OidcStrategy = OidcStrategy = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], OidcStrategy);
//# sourceMappingURL=oidc.strategy.js.map