import { PrismaService } from '@modules/prisma/prisma.service';
import { RedisService } from '@modules/redis/redis.service';
export declare class HealthService {
    private prismaService;
    private redisService;
    constructor(prismaService: PrismaService, redisService: RedisService);
    getDetailedHealth(): Promise<{
        status: string;
        timestamp: string;
        services: {
            database: {
                status: string;
                responseTime: string;
                error?: undefined;
            } | {
                status: string;
                error: any;
                responseTime?: undefined;
            } | {
                status: string;
                usage: string;
                heapUsed: string;
                heapTotal: string;
                error?: undefined;
            } | {
                status: string;
                error: any;
                usage?: undefined;
                heapUsed?: undefined;
                heapTotal?: undefined;
            } | {
                status: string;
                lastModified: string;
                error?: undefined;
            } | {
                status: string;
                error: any;
                lastModified?: undefined;
            };
            redis: {
                status: string;
                responseTime: string;
                error?: undefined;
            } | {
                status: string;
                error: any;
                responseTime?: undefined;
            } | {
                status: string;
                usage: string;
                heapUsed: string;
                heapTotal: string;
                error?: undefined;
            } | {
                status: string;
                error: any;
                usage?: undefined;
                heapUsed?: undefined;
                heapTotal?: undefined;
            } | {
                status: string;
                lastModified: string;
                error?: undefined;
            } | {
                status: string;
                error: any;
                lastModified?: undefined;
            };
            memory: {
                status: string;
                responseTime: string;
                error?: undefined;
            } | {
                status: string;
                error: any;
                responseTime?: undefined;
            } | {
                status: string;
                usage: string;
                heapUsed: string;
                heapTotal: string;
                error?: undefined;
            } | {
                status: string;
                error: any;
                usage?: undefined;
                heapUsed?: undefined;
                heapTotal?: undefined;
            } | {
                status: string;
                lastModified: string;
                error?: undefined;
            } | {
                status: string;
                error: any;
                lastModified?: undefined;
            };
            disk: {
                status: string;
                responseTime: string;
                error?: undefined;
            } | {
                status: string;
                error: any;
                responseTime?: undefined;
            } | {
                status: string;
                usage: string;
                heapUsed: string;
                heapTotal: string;
                error?: undefined;
            } | {
                status: string;
                error: any;
                usage?: undefined;
                heapUsed?: undefined;
                heapTotal?: undefined;
            } | {
                status: string;
                lastModified: string;
                error?: undefined;
            } | {
                status: string;
                error: any;
                lastModified?: undefined;
            };
        };
    }>;
    private checkDatabase;
    private checkRedis;
    private checkMemory;
    private checkDisk;
}
