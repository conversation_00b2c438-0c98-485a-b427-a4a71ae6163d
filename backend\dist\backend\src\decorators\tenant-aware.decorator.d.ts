export declare const Tenant: (...dataOrPipes: (string | import("@nestjs/common").PipeTransform<any, any> | import("@nestjs/common").Type<import("@nestjs/common").PipeTransform<any, any>>)[]) => ParameterDecorator;
export declare const OrganizationId: (...dataOrPipes: unknown[]) => ParameterDecorator;
export declare const UserId: (...dataOrPipes: unknown[]) => ParameterDecorator;
export declare const UserRole: (...dataOrPipes: unknown[]) => ParameterDecorator;
