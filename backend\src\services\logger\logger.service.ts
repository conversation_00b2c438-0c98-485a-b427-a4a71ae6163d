import { Injectable, LoggerService as NestLoggerService } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';

export interface LogContext {
  userId?: string;
  organizationId?: string;
  sessionId?: string;
  requestId?: string;
  module?: string;
  action?: string;
  metadata?: Record<string, any>;
}

@Injectable()
export class LoggerService implements NestLoggerService {
  private logger: winston.Logger;
  private context: string = 'Application';

  constructor(private configService: ConfigService) {
    this.createLogger();
  }

  private createLogger(): void {
    const environment = this.configService.get('NODE_ENV', 'development');
    const logLevel = this.configService.get('LOG_LEVEL', 'info');
    const logDir = this.configService.get('LOG_DIR', './logs');

    // Custom format for structured logging
    const customFormat = winston.format.combine(
      winston.format.timestamp({
        format: 'YYYY-MM-DD HH:mm:ss.SSS',
      }),
      winston.format.errors({ stack: true }),
      winston.format.json(),
      winston.format.printf((info) => {
        const { timestamp, level, message, context, ...meta } = info;
        
        const logEntry = {
          timestamp,
          level: level.toUpperCase(),
          context: context || this.context,
          message,
          ...meta,
        };

        // Add environment and service info
        logEntry['service'] = 'synapseai-backend';
        logEntry['environment'] = environment;
        logEntry['version'] = this.configService.get('APP_VERSION', '1.0.0');

        return JSON.stringify(logEntry);
      }),
    );

    // Console format for development
    const consoleFormat = winston.format.combine(
      winston.format.colorize(),
      winston.format.timestamp({
        format: 'HH:mm:ss.SSS',
      }),
      winston.format.printf((info) => {
        const { timestamp, level, message, context, ...meta } = info;
        const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
        return `${timestamp} [${level}] [${context || this.context}] ${message} ${metaStr}`;
      }),
    );

    // Create transports
    const transports: winston.transport[] = [];

    // Console transport
    if (environment === 'development') {
      transports.push(
        new winston.transports.Console({
          level: logLevel,
          format: consoleFormat,
        }),
      );
    } else {
      transports.push(
        new winston.transports.Console({
          level: logLevel,
          format: customFormat,
        }),
      );
    }

    // File transports for production
    if (environment === 'production') {
      // General application logs
      transports.push(
        new DailyRotateFile({
          filename: `${logDir}/application-%DATE%.log`,
          datePattern: 'YYYY-MM-DD',
          maxSize: '100m',
          maxFiles: '30d',
          level: logLevel,
          format: customFormat,
        }),
      );

      // Error logs
      transports.push(
        new DailyRotateFile({
          filename: `${logDir}/error-%DATE%.log`,
          datePattern: 'YYYY-MM-DD',
          maxSize: '100m',
          maxFiles: '30d',
          level: 'error',
          format: customFormat,
        }),
      );

      // Audit logs for security and compliance
      transports.push(
        new DailyRotateFile({
          filename: `${logDir}/audit-%DATE%.log`,
          datePattern: 'YYYY-MM-DD',
          maxSize: '100m',
          maxFiles: '365d', // Keep audit logs for 1 year
          level: 'info',
          format: customFormat,
        }),
      );

      // Performance logs
      transports.push(
        new DailyRotateFile({
          filename: `${logDir}/performance-%DATE%.log`,
          datePattern: 'YYYY-MM-DD',
          maxSize: '100m',
          maxFiles: '7d',
          level: 'debug',
          format: customFormat,
        }),
      );
    }

    // Create logger instance
    this.logger = winston.createLogger({
      level: logLevel,
      format: customFormat,
      transports,
      exitOnError: false,
      silent: environment === 'test',
    });

    // Handle uncaught exceptions and unhandled rejections
    this.logger.exceptions.handle(
      new winston.transports.File({
        filename: `${logDir}/exceptions.log`,
        format: customFormat,
      }),
    );

    this.logger.rejections.handle(
      new winston.transports.File({
        filename: `${logDir}/rejections.log`,
        format: customFormat,
      }),
    );
  }

  /**
   * Set the context for subsequent log messages
   */
  setContext(context: string): void {
    this.context = context;
  }

  /**
   * Log a message with optional context
   */
  log(message: string, context?: string | LogContext): void {
    this.logger.info(message, this.formatContext(context));
  }

  /**
   * Log an error message
   */
  error(message: string, trace?: string | Error, context?: string | LogContext): void {
    const logContext = this.formatContext(context);
    
    if (trace instanceof Error) {
      logContext.error = {
        name: trace.name,
        message: trace.message,
        stack: trace.stack,
      };
    } else if (trace) {
      logContext.trace = trace;
    }

    this.logger.error(message, logContext);
  }

  /**
   * Log a warning message
   */
  warn(message: string, context?: string | LogContext): void {
    this.logger.warn(message, this.formatContext(context));
  }

  /**
   * Log a debug message
   */
  debug(message: string, context?: string | LogContext): void {
    this.logger.debug(message, this.formatContext(context));
  }

  /**
   * Log a verbose message
   */
  verbose(message: string, context?: string | LogContext): void {
    this.logger.verbose(message, this.formatContext(context));
  }

  /**
   * Log an audit event for security and compliance
   */
  audit(
    action: string,
    result: 'success' | 'failure',
    context: LogContext & {
      resource?: string;
      details?: Record<string, any>;
    },
  ): void {
    const auditLog = {
      action,
      result,
      timestamp: new Date().toISOString(),
      ...context,
    };

    this.logger.info('AUDIT_EVENT', {
      context: 'Audit',
      audit: auditLog,
    });
  }

  /**
   * Log a performance metric
   */
  performance(
    operation: string,
    duration: number,
    context: LogContext & {
      success?: boolean;
      metadata?: Record<string, any>;
    },
  ): void {
    const performanceLog = {
      operation,
      duration,
      timestamp: new Date().toISOString(),
      ...context,
    };

    this.logger.debug('PERFORMANCE_METRIC', {
      context: 'Performance',
      performance: performanceLog,
    });
  }

  /**
   * Log a security event
   */
  security(
    event: string,
    severity: 'low' | 'medium' | 'high' | 'critical',
    context: LogContext & {
      threat?: string;
      action?: string;
      details?: Record<string, any>;
    },
  ): void {
    const securityLog = {
      event,
      severity,
      timestamp: new Date().toISOString(),
      ...context,
    };

    this.logger.warn('SECURITY_EVENT', {
      context: 'Security',
      security: securityLog,
    });
  }

  /**
   * Log a business event for analytics
   */
  business(
    event: string,
    context: LogContext & {
      value?: number;
      currency?: string;
      properties?: Record<string, any>;
    },
  ): void {
    const businessLog = {
      event,
      timestamp: new Date().toISOString(),
      ...context,
    };

    this.logger.info('BUSINESS_EVENT', {
      context: 'Business',
      business: businessLog,
    });
  }

  /**
   * Format context for logging
   */
  private formatContext(context?: string | LogContext): any {
    if (typeof context === 'string') {
      return { context };
    }

    if (context && typeof context === 'object') {
      return {
        context: this.context,
        ...context,
      };
    }

    return { context: this.context };
  }

  /**
   * Get the underlying Winston logger instance
   */
  getWinstonLogger(): winston.Logger {
    return this.logger;
  }

  /**
   * Create a child logger with additional context
   */
  child(defaultContext: LogContext): LoggerService {
    const childLogger = new LoggerService(this.configService);
    
    // Override the logger to include default context
    const originalLogger = childLogger.logger;
    childLogger.logger = originalLogger.child(defaultContext);
    
    return childLogger;
  }
}
