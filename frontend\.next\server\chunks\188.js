exports.id=188,exports.ids=[188],exports.modules={382:(e,t,r)=>{Promise.resolve().then(r.bind(r,71929)),Promise.resolve().then(r.bind(r,30036)),Promise.resolve().then(r.bind(r,3584)),Promise.resolve().then(r.bind(r,89997)),Promise.resolve().then(r.bind(r,82808)),Promise.resolve().then(r.bind(r,5348))},61468:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,33817,23)),Promise.resolve().then(r.t.bind(r,19323,23)),Promise.resolve().then(r.t.bind(r,52204,23)),Promise.resolve().then(r.t.bind(r,60820,23)),Promise.resolve().then(r.t.bind(r,55915,23)),Promise.resolve().then(r.t.bind(r,81611,23))},71929:(e,t,r)=>{"use strict";r.d(t,{A11yProvider:()=>p,Hg:()=>g});var i=r(19899),a=r(5507);let n={announcePageChanges:!0,announceFormErrors:!0,announceStatusUpdates:!0,enableKeyboardNavigation:!0,enableFocusManagement:!0,enableHighContrast:!0,enableReducedMotion:!0,minimumTouchTarget:44,minimumColorContrast:4.5};class o{constructor(){this.liveRegion=null,this.politeRegion=null}createLiveRegions(){this.liveRegion=document.createElement("div"),this.liveRegion.setAttribute("aria-live","assertive"),this.liveRegion.setAttribute("aria-atomic","true"),this.liveRegion.setAttribute("class","sr-only"),this.liveRegion.style.cssText=`
      position: absolute !important;
      width: 1px !important;
      height: 1px !important;
      padding: 0 !important;
      margin: -1px !important;
      overflow: hidden !important;
      clip: rect(0, 0, 0, 0) !important;
      white-space: nowrap !important;
      border: 0 !important;
    `,document.body.appendChild(this.liveRegion),this.politeRegion=document.createElement("div"),this.politeRegion.setAttribute("aria-live","polite"),this.politeRegion.setAttribute("aria-atomic","true"),this.politeRegion.setAttribute("class","sr-only"),this.politeRegion.style.cssText=this.liveRegion.style.cssText,document.body.appendChild(this.politeRegion)}announce(e,t="polite"){if(!e.trim())return;let r="assertive"===t?this.liveRegion:this.politeRegion;r&&(r.textContent="",setTimeout(()=>{r.textContent=e},100),setTimeout(()=>{r.textContent=""},1e3))}announcePageChange(e){this.announce(`Navigated to ${e}`,"polite")}announceFormErrors(e){if(0===e.length)return;let t=1===e.length?`Form error: ${e[0]}`:`Form has ${e.length} errors: ${e.join(", ")}`;this.announce(t,"assertive")}announceStatus(e,t="info"){this.announce(`${{success:"Success:",error:"Error:",warning:"Warning:",info:"Info:"}[t]} ${e}`,"error"===t?"assertive":"polite")}destroy(){this.liveRegion&&(document.body.removeChild(this.liveRegion),this.liveRegion=null),this.politeRegion&&(document.body.removeChild(this.politeRegion),this.politeRegion=null)}}class s{setFocus(e,t){let r="string"==typeof e?document.querySelector(e):e;if(!r)return;let i=document.activeElement;i&&i!==r&&this.focusStack.push(i),r.focus(),t&&m.announce(t)}restoreFocus(){let e=this.focusStack.pop();e&&document.contains(e)&&e.focus()}trapFocus(e){this.trapStack.push(e),this.setupFocusTrap(e)}releaseFocusTrap(){let e=this.trapStack.pop();e&&this.removeFocusTrap(e)}setupFocusTrap(e){let t=this.getFocusableElements(e);if(0===t.length)return;let r=t[0],i=t[t.length-1];e.addEventListener("keydown",e=>{"Tab"===e.key&&(e.shiftKey?document.activeElement===r&&(e.preventDefault(),i.focus()):document.activeElement===i&&(e.preventDefault(),r.focus()))}),e.setAttribute("data-focus-trap","true"),r.focus()}removeFocusTrap(e){let t=e.getAttribute("data-focus-trap-handler");t&&e.removeEventListener("keydown",t),e.removeAttribute("data-focus-trap"),e.removeAttribute("data-focus-trap-handler")}getFocusableElements(e){return Array.from(e.querySelectorAll('a[href], button:not([disabled]), input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"]), [contenteditable="true"]')).filter(e=>e.offsetWidth>0&&e.offsetHeight>0&&!e.hidden)}isFocusable(e){return this.getFocusableElements(document.body).includes(e)}constructor(){this.focusStack=[],this.trapStack=[]}}class l{addShortcut(e,t,r={}){let i=this.createShortcutKey(e,r);this.keyHandlers.set(i,t)}removeShortcut(e,t={}){let r=this.createShortcutKey(e,t);this.keyHandlers.delete(r)}handleKeyDown(e){let t=this.createShortcutKey(e.key,{ctrl:e.ctrlKey,alt:e.altKey,shift:e.shiftKey,meta:e.metaKey}),r=this.keyHandlers.get(t);r&&(e.preventDefault(),r(e))}createShortcutKey(e,t){let r=[];return t.ctrl&&r.push("ctrl"),t.alt&&r.push("alt"),t.shift&&r.push("shift"),t.meta&&r.push("meta"),[...r,e.toLowerCase()].join("+")}setupArrowNavigation(e,t="vertical"){e.addEventListener("keydown",r=>{let i=Array.from(e.querySelectorAll('[tabindex]:not([tabindex="-1"]), button, input, select, textarea, a[href]')),a=i.indexOf(document.activeElement);if(-1===a)return;let n=a;switch(t){case"horizontal":"ArrowLeft"===r.key&&(n=Math.max(0,a-1)),"ArrowRight"===r.key&&(n=Math.min(i.length-1,a+1));break;case"vertical":"ArrowUp"===r.key&&(n=Math.max(0,a-1)),"ArrowDown"===r.key&&(n=Math.min(i.length-1,a+1))}n!==a&&(r.preventDefault(),i[n].focus())})}constructor(){this.keyHandlers=new Map}}class c{getRelativeLuminance(e){let t=this.hexToRgb(e);if(!t)return 0;let[r,i,a]=t.map(e=>(e/=255)<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4));return .2126*r+.7152*i+.0722*a}hexToRgb(e){let t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);return t?[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16)]:null}getContrastRatio(e,t){let r=this.getRelativeLuminance(e),i=this.getRelativeLuminance(t);return(Math.max(r,i)+.05)/(Math.min(r,i)+.05)}meetsWCAG(e,t,r="AA",i="normal"){return this.getContrastRatio(e,t)>=({AA:{normal:4.5,large:3},AAA:{normal:7,large:4.5}})[r][i]}suggestAccessibleColor(e,t,r="AA"){let i="AA"===r?4.5:7,a=this.getContrastRatio(e,t);if(a>=i)return e;let n=this.hexToRgb(e);if(!n)return e;let[o,s,l]=n,c=a<i?-10:10;for(;this.getContrastRatio(this.rgbToHex(o,s,l),t)<i&&(o=Math.max(0,Math.min(255,o+c)),s=Math.max(0,Math.min(255,s+c)),l=Math.max(0,Math.min(255,l+c)),(0!==o||0!==s||0!==l)&&(255!==o||255!==s||255!==l)););return this.rgbToHex(o,s,l)}rgbToHex(e,t,r){return`#${(16777216+(e<<16)+(t<<8)+r).toString(16).slice(1)}`}}class d{prefersReducedMotion(){return!1}applyMotionPreferences(e){this.prefersReducedMotion()&&(e.style.animation="none",e.style.transition="none")}getSafeAnimationDuration(e){return this.prefersReducedMotion()?0:e}}let m=new o,u=new s;new l;let h=new c,f=(new d,(0,a.createContext)(void 0));function g(){let e=(0,a.useContext)(f);if(void 0===e)throw Error("useA11y must be used within an A11yProvider");return e}function p({children:e,config:t}){let[r,o]=(0,a.useState)({...n,...t}),[s,l]=(0,a.useState)(!1),[c,d]=(0,a.useState)(!1);return i.jsx(f.Provider,{value:{config:r,updateConfig:e=>{o(t=>({...t,...e}))},announce:(e,t="polite")=>{r.announceStatusUpdates&&m.announce(e,t)},announcePageChange:e=>{r.announcePageChanges&&m.announcePageChange(e)},announceFormErrors:e=>{r.announceFormErrors&&m.announceFormErrors(e)},announceStatus:(e,t="info")=>{r.announceStatusUpdates&&m.announceStatus(e,t)},setFocus:(e,t)=>{r.enableFocusManagement&&u.setFocus(e,t)},restoreFocus:()=>{r.enableFocusManagement&&u.restoreFocus()},trapFocus:e=>{r.enableFocusManagement&&u.trapFocus(e)},releaseFocusTrap:()=>{r.enableFocusManagement&&u.releaseFocusTrap()},prefersReducedMotion:s,highContrastMode:c,toggleHighContrast:()=>{d(e=>!e)},checkColorContrast:(e,t)=>h.meetsWCAG(e,t,"AA")},children:e})}},30036:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>n});var i=r(19899),a=r(60878);function n({children:e}){return i.jsx(a.SessionProvider,{children:e})}},3584:(e,t,r)=>{"use strict";r.d(t,{OrganizationProvider:()=>c});var i=r(19899),a=r(5507),n=r(60878);let o=(0,a.createContext)(void 0);class s{constructor(e){this.accessToken=null,this.apiUrl=e}setAccessToken(e){this.accessToken=e}async request(e,t={}){let r=`${this.apiUrl}${e}`,i={"Content-Type":"application/json",...this.accessToken&&{Authorization:`Bearer ${this.accessToken}`},...t.headers},a=await fetch(r,{...t,headers:i});if(!a.ok)throw Error((await a.json().catch(()=>({message:"Request failed"}))).message||`HTTP ${a.status}`);return a.json()}async getOrganization(e){return this.request(`/api/v1/organizations/${e}`)}async updateOrganization(e,t){return this.request(`/api/v1/organizations/${e}`,{method:"PATCH",body:JSON.stringify(t)})}async deleteOrganization(e){await this.request(`/api/v1/organizations/${e}`,{method:"DELETE"})}async switchOrganization(e){return this.request("/api/v1/auth/switch-organization",{method:"POST",body:JSON.stringify({organizationId:e})})}async getMembers(e){return this.request(`/api/v1/organizations/${e}/members`)}async addMember(e,t){return this.request(`/api/v1/organizations/${e}/members`,{method:"POST",body:JSON.stringify(t)})}async updateMember(e,t,r){return this.request(`/api/v1/organizations/${e}/members/${t}`,{method:"PATCH",body:JSON.stringify(r)})}async removeMember(e,t){await this.request(`/api/v1/organizations/${e}/members/${t}`,{method:"DELETE"})}async getInvites(e){return this.request(`/api/v1/organizations/${e}/invites`)}async inviteMember(e,t){return this.request(`/api/v1/organizations/${e}/invites`,{method:"POST",body:JSON.stringify(t)})}async revokeInvite(e,t){await this.request(`/api/v1/organizations/${e}/invites/${t}`,{method:"DELETE"})}async resendInvite(e,t){return this.request(`/api/v1/organizations/${e}/invites/${t}/resend`,{method:"POST"})}async getUsage(e){return this.request(`/api/v1/organizations/${e}/usage`)}async getBilling(e){return this.request(`/api/v1/organizations/${e}/billing`)}async updateBilling(e,t){return this.request(`/api/v1/organizations/${e}/billing`,{method:"PATCH",body:JSON.stringify(t)})}async createCheckoutSession(e,t){return this.request(`/api/v1/organizations/${e}/billing/checkout`,{method:"POST",body:JSON.stringify(t)})}async createPortalSession(e,t){return this.request(`/api/v1/organizations/${e}/billing/portal`,{method:"POST",body:JSON.stringify(t)})}}var l=r(71929);function c({children:e}){let{data:t,update:r}=(0,n.useSession)(),{announceStatus:c}=(0,l.Hg)(),[d,m]=(0,a.useState)(null),[u,h]=(0,a.useState)([]),[f,g]=(0,a.useState)([]),[p,y]=(0,a.useState)(null),[b,x]=(0,a.useState)(null),[v,w]=(0,a.useState)(!1),[S,A]=(0,a.useState)(!1),[P,k]=(0,a.useState)(null),[M]=(0,a.useState)(()=>new s("http://localhost:3000")),E=t?.user,T=E?.organizationId,C=async e=>{if(e!==T){A(!0),k(null);try{let{accessToken:t,user:i}=await M.switchOrganization(e);await r({accessToken:t,user:i}),c("Organization switched successfully","success")}catch(t){let e=t instanceof Error?t.message:"Failed to switch organization";throw k(e),c(`Error: ${e}`,"error"),t}finally{A(!1)}}},z=async e=>{if(!T)throw Error("No organization selected");A(!0),k(null);try{let t=await M.updateOrganization(T,e);m(t),c("Organization updated successfully","success")}catch(t){let e=t instanceof Error?t.message:"Failed to update organization";throw k(e),c(`Error: ${e}`,"error"),t}finally{A(!1)}},I=async()=>{if(!T)throw Error("No organization selected");A(!0),k(null);try{await M.deleteOrganization(T),m(null),h([]),g([]),y(null),x(null),c("Organization deleted successfully","success")}catch(t){let e=t instanceof Error?t.message:"Failed to delete organization";throw k(e),c(`Error: ${e}`,"error"),t}finally{A(!1)}},O=async(e,t,r)=>{if(!T)throw Error("No organization selected");A(!0),k(null);try{let i=await M.addMember(T,{email:e,role:t,permissions:r});h(e=>[...e,i]),c("Member added successfully","success")}catch(t){let e=t instanceof Error?t.message:"Failed to add member";throw k(e),c(`Error: ${e}`,"error"),t}finally{A(!1)}},$=async(e,t,r)=>{if(!T)throw Error("No organization selected");A(!0),k(null);try{let i=await M.updateMember(T,e,{role:t,permissions:r});h(t=>t.map(t=>t.id===e?i:t)),c("Member role updated successfully","success")}catch(t){let e=t instanceof Error?t.message:"Failed to update member role";throw k(e),c(`Error: ${e}`,"error"),t}finally{A(!1)}},F=async e=>{if(!T)throw Error("No organization selected");A(!0),k(null);try{await M.removeMember(T,e),h(t=>t.filter(t=>t.id!==e)),c("Member removed successfully","success")}catch(t){let e=t instanceof Error?t.message:"Failed to remove member";throw k(e),c(`Error: ${e}`,"error"),t}finally{A(!1)}},H=async(e,t,r)=>{if(!T)throw Error("No organization selected");A(!0),k(null);try{let i=await M.inviteMember(T,{email:e,role:t,permissions:r});g(e=>[...e,i]),c("Invitation sent successfully","success")}catch(t){let e=t instanceof Error?t.message:"Failed to send invitation";throw k(e),c(`Error: ${e}`,"error"),t}finally{A(!1)}},R=async e=>{if(!T)throw Error("No organization selected");A(!0),k(null);try{await M.revokeInvite(T,e),g(t=>t.filter(t=>t.id!==e)),c("Invitation revoked successfully","success")}catch(t){let e=t instanceof Error?t.message:"Failed to revoke invitation";throw k(e),c(`Error: ${e}`,"error"),t}finally{A(!1)}},j=async e=>{if(!T)throw Error("No organization selected");A(!0),k(null);try{let t=await M.resendInvite(T,e);g(r=>r.map(r=>r.id===e?t:r)),c("Invitation resent successfully","success")}catch(t){let e=t instanceof Error?t.message:"Failed to resend invitation";throw k(e),c(`Error: ${e}`,"error"),t}finally{A(!1)}},N=async e=>{if(!T)throw Error("No organization selected");A(!0),k(null);try{let t=await M.updateBilling(T,{planId:e});x(t),c("Billing updated successfully","success")}catch(t){let e=t instanceof Error?t.message:"Failed to update billing";throw k(e),c(`Error: ${e}`,"error"),t}finally{A(!1)}},L=e=>!!E&&(E.permissions?.includes(e)||!1),q=e=>!!E&&E.organizationRole===e;return i.jsx(o.Provider,{value:{organization:d,switchOrganization:C,updateOrganization:z,deleteOrganization:I,members:u,invites:f,addMember:O,updateMemberRole:$,removeMember:F,inviteMember:H,revokeInvite:R,resendInvite:j,usage:p,billing:b,updateBilling:N,hasPermission:L,hasRole:q,canManageMembers:()=>L("members:invite")||L("members:update")||L("members:remove"),canManageBilling:()=>L("billing:update")||q("owner"),canManageSettings:()=>L("organization:update")||q("owner")||q("admin"),isLoading:v,isUpdating:S,error:P,clearError:()=>{k(null)}},children:e})}},89997:(e,t,r)=>{"use strict";r.d(t,{QueryProvider:()=>s});var i=r(19899),a=r(33069),n=r(1206);!function(){var e=Error("Cannot find module '@tanstack/react-query-devtools'");throw e.code="MODULE_NOT_FOUND",e}();var o=r(5507);function s({children:e}){let[t]=(0,o.useState)(()=>new a.S({defaultOptions:{queries:{staleTime:6e4,gcTime:6e5,retry:(e,t)=>(!(t?.status>=400&&t?.status<500)||!![408,429].includes(t.status))&&e<3,retryDelay:e=>Math.min(1e3*2**e,3e4)},mutations:{retry:(e,t)=>!(t?.status>=400&&t?.status<500)&&e<2}}}));return(0,i.jsxs)(n.aH,{client:t,children:[e,!1]})}},82808:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>c});var i=r(19899),a=r(5507);let n={themes:{light:{name:"SynapseAI Light",mode:"light",colors:{primary:{50:"#eff6ff",100:"#dbeafe",200:"#bfdbfe",300:"#93c5fd",400:"#60a5fa",500:"#3b82f6",600:"#2563eb",700:"#1d4ed8",800:"#1e40af",900:"#1e3a8a",950:"#172554"},success:{50:"#f0fdf4",100:"#dcfce7",200:"#bbf7d0",300:"#86efac",400:"#4ade80",500:"#22c55e",600:"#16a34a",700:"#15803d",800:"#166534",900:"#14532d",950:"#052e16"},warning:{50:"#fffbeb",100:"#fef3c7",200:"#fde68a",300:"#fcd34d",400:"#fbbf24",500:"#f59e0b",600:"#d97706",700:"#b45309",800:"#92400e",900:"#78350f",950:"#451a03"},error:{50:"#fef2f2",100:"#fee2e2",200:"#fecaca",300:"#fca5a5",400:"#f87171",500:"#ef4444",600:"#dc2626",700:"#b91c1c",800:"#991b1b",900:"#7f1d1d",950:"#450a0a"},info:{50:"#f0f9ff",100:"#e0f2fe",200:"#bae6fd",300:"#7dd3fc",400:"#38bdf8",500:"#0ea5e9",600:"#0284c7",700:"#0369a1",800:"#075985",900:"#0c4a6e",950:"#082f49"},gray:{50:"#f9fafb",100:"#f3f4f6",200:"#e5e7eb",300:"#d1d5db",400:"#9ca3af",500:"#6b7280",600:"#4b5563",700:"#374151",800:"#1f2937",900:"#111827",950:"#030712"},background:{primary:"#ffffff",secondary:"#f9fafb",tertiary:"#f3f4f6",inverse:"#111827"},surface:{primary:"#ffffff",secondary:"#f9fafb",tertiary:"#f3f4f6",inverse:"#111827",overlay:"rgba(0, 0, 0, 0.5)"},text:{primary:"#111827",secondary:"#374151",tertiary:"#6b7280",inverse:"#ffffff",disabled:"#9ca3af"},border:{primary:"#e5e7eb",secondary:"#d1d5db",tertiary:"#9ca3af",inverse:"#374151",focus:"#3b82f6"}},spacing:{0:"0px",px:"1px",.5:"0.125rem",1:"0.25rem",1.5:"0.375rem",2:"0.5rem",2.5:"0.625rem",3:"0.75rem",3.5:"0.875rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem",11:"2.75rem",12:"3rem",14:"3.5rem",16:"4rem",20:"5rem",24:"6rem",28:"7rem",32:"8rem",36:"9rem",40:"10rem",44:"11rem",48:"12rem",52:"13rem",56:"14rem",60:"15rem",64:"16rem",72:"18rem",80:"20rem",96:"24rem"},typography:{fontFamily:{sans:["Inter","ui-sans-serif","system-ui","sans-serif"],serif:["ui-serif","Georgia","Cambria","serif"],mono:["ui-monospace","SFMono-Regular","Menlo","Monaco","Consolas","monospace"]},fontSize:{xs:["0.75rem",{lineHeight:"1rem"}],sm:["0.875rem",{lineHeight:"1.25rem"}],base:["1rem",{lineHeight:"1.5rem"}],lg:["1.125rem",{lineHeight:"1.75rem"}],xl:["1.25rem",{lineHeight:"1.75rem"}],"2xl":["1.5rem",{lineHeight:"2rem"}],"3xl":["1.875rem",{lineHeight:"2.25rem"}],"4xl":["2.25rem",{lineHeight:"2.5rem"}],"5xl":["3rem",{lineHeight:"1"}],"6xl":["3.75rem",{lineHeight:"1"}],"7xl":["4.5rem",{lineHeight:"1"}],"8xl":["6rem",{lineHeight:"1"}],"9xl":["8rem",{lineHeight:"1"}]},fontWeight:{thin:"100",extralight:"200",light:"300",normal:"400",medium:"500",semibold:"600",bold:"700",extrabold:"800",black:"900"},lineHeight:{none:"1",tight:"1.25",snug:"1.375",normal:"1.5",relaxed:"1.625",loose:"2"},letterSpacing:{tighter:"-0.05em",tight:"-0.025em",normal:"0em",wide:"0.025em",wider:"0.05em",widest:"0.1em"}},breakpoints:{sm:"640px",md:"768px",lg:"1024px",xl:"1280px","2xl":"1536px"},shadows:{sm:"0 1px 2px 0 rgb(0 0 0 / 0.05)",base:"0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",md:"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",lg:"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",xl:"0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)","2xl":"0 25px 50px -12px rgb(0 0 0 / 0.25)",inner:"inset 0 2px 4px 0 rgb(0 0 0 / 0.05)",none:"0 0 #0000"},radii:{none:"0px",sm:"0.125rem",base:"0.25rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem",full:"9999px"},transitions:{duration:{75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms"},timing:{linear:"linear",in:"cubic-bezier(0.4, 0, 1, 1)",out:"cubic-bezier(0, 0, 0.2, 1)","in-out":"cubic-bezier(0.4, 0, 0.2, 1)"},property:{none:"none",all:"all",default:"color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter",colors:"color, background-color, border-color, text-decoration-color, fill, stroke",opacity:"opacity",shadow:"box-shadow",transform:"transform"}}},dark:{name:"SynapseAI Dark",mode:"dark",colors:{primary:{50:"#eff6ff",100:"#dbeafe",200:"#bfdbfe",300:"#93c5fd",400:"#60a5fa",500:"#3b82f6",600:"#2563eb",700:"#1d4ed8",800:"#1e40af",900:"#1e3a8a",950:"#172554"},success:{50:"#f0fdf4",100:"#dcfce7",200:"#bbf7d0",300:"#86efac",400:"#4ade80",500:"#22c55e",600:"#16a34a",700:"#15803d",800:"#166534",900:"#14532d",950:"#052e16"},warning:{50:"#fffbeb",100:"#fef3c7",200:"#fde68a",300:"#fcd34d",400:"#fbbf24",500:"#f59e0b",600:"#d97706",700:"#b45309",800:"#92400e",900:"#78350f",950:"#451a03"},error:{50:"#fef2f2",100:"#fee2e2",200:"#fecaca",300:"#fca5a5",400:"#f87171",500:"#ef4444",600:"#dc2626",700:"#b91c1c",800:"#991b1b",900:"#7f1d1d",950:"#450a0a"},info:{50:"#f0f9ff",100:"#e0f2fe",200:"#bae6fd",300:"#7dd3fc",400:"#38bdf8",500:"#0ea5e9",600:"#0284c7",700:"#0369a1",800:"#075985",900:"#0c4a6e",950:"#082f49"},gray:{50:"#f9fafb",100:"#f3f4f6",200:"#e5e7eb",300:"#d1d5db",400:"#9ca3af",500:"#6b7280",600:"#4b5563",700:"#374151",800:"#1f2937",900:"#111827",950:"#030712"},background:{primary:"#030712",secondary:"#111827",tertiary:"#1f2937",inverse:"#ffffff"},surface:{primary:"#111827",secondary:"#1f2937",tertiary:"#374151",inverse:"#ffffff",overlay:"rgba(0, 0, 0, 0.8)"},text:{primary:"#f9fafb",secondary:"#e5e7eb",tertiary:"#9ca3af",inverse:"#111827",disabled:"#6b7280"},border:{primary:"#374151",secondary:"#4b5563",tertiary:"#6b7280",inverse:"#e5e7eb",focus:"#3b82f6"}},spacing:{0:"0px",px:"1px",.5:"0.125rem",1:"0.25rem",1.5:"0.375rem",2:"0.5rem",2.5:"0.625rem",3:"0.75rem",3.5:"0.875rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem",11:"2.75rem",12:"3rem",14:"3.5rem",16:"4rem",20:"5rem",24:"6rem",28:"7rem",32:"8rem",36:"9rem",40:"10rem",44:"11rem",48:"12rem",52:"13rem",56:"14rem",60:"15rem",64:"16rem",72:"18rem",80:"20rem",96:"24rem"},typography:{fontFamily:{sans:["Inter","ui-sans-serif","system-ui","sans-serif"],serif:["ui-serif","Georgia","Cambria","serif"],mono:["ui-monospace","SFMono-Regular","Menlo","Monaco","Consolas","monospace"]},fontSize:{xs:["0.75rem",{lineHeight:"1rem"}],sm:["0.875rem",{lineHeight:"1.25rem"}],base:["1rem",{lineHeight:"1.5rem"}],lg:["1.125rem",{lineHeight:"1.75rem"}],xl:["1.25rem",{lineHeight:"1.75rem"}],"2xl":["1.5rem",{lineHeight:"2rem"}],"3xl":["1.875rem",{lineHeight:"2.25rem"}],"4xl":["2.25rem",{lineHeight:"2.5rem"}],"5xl":["3rem",{lineHeight:"1"}],"6xl":["3.75rem",{lineHeight:"1"}],"7xl":["4.5rem",{lineHeight:"1"}],"8xl":["6rem",{lineHeight:"1"}],"9xl":["8rem",{lineHeight:"1"}]},fontWeight:{thin:"100",extralight:"200",light:"300",normal:"400",medium:"500",semibold:"600",bold:"700",extrabold:"800",black:"900"},lineHeight:{none:"1",tight:"1.25",snug:"1.375",normal:"1.5",relaxed:"1.625",loose:"2"},letterSpacing:{tighter:"-0.05em",tight:"-0.025em",normal:"0em",wide:"0.025em",wider:"0.05em",widest:"0.1em"}},breakpoints:{sm:"640px",md:"768px",lg:"1024px",xl:"1280px","2xl":"1536px"},shadows:{sm:"0 1px 2px 0 rgb(0 0 0 / 0.05)",base:"0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",md:"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",lg:"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",xl:"0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)","2xl":"0 25px 50px -12px rgb(0 0 0 / 0.25)",inner:"inset 0 2px 4px 0 rgb(0 0 0 / 0.05)",none:"0 0 #0000"},radii:{none:"0px",sm:"0.125rem",base:"0.25rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem",full:"9999px"},transitions:{duration:{75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms"},timing:{linear:"linear",in:"cubic-bezier(0.4, 0, 1, 1)",out:"cubic-bezier(0, 0, 0.2, 1)","in-out":"cubic-bezier(0.4, 0, 0.2, 1)"},property:{none:"none",all:"all",default:"color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter",colors:"color, background-color, border-color, text-decoration-color, fill, stroke",opacity:"opacity",shadow:"box-shadow",transform:"transform"}}}},defaultTheme:"system",enableSystemTheme:!0,enableTransitions:!0,transitionDuration:200,storageKey:"synapseai-theme"};(0,a.createContext)(void 0);class o{constructor(e=n){this.listeners=new Set,this.isTransitioning=!1,this.mediaQuery=null,this.config=e,this.currentMode=this.getStoredMode()||e.defaultTheme,this.resolvedMode=this.resolveMode(this.currentMode)}getContextValue(){return{theme:this.getCurrentTheme(),mode:this.currentMode,resolvedMode:this.resolvedMode,setMode:this.setMode.bind(this),toggleMode:this.toggleMode.bind(this),config:this.config,isTransitioning:this.isTransitioning}}setMode(e){if(this.currentMode===e)return;this.currentMode=e;let t=this.resolveMode(e);t!==this.resolvedMode&&(this.resolvedMode=t,this.applyTheme()),this.storeMode(e),this.notifyListeners()}toggleMode(){"system"===this.currentMode?this.setMode("light"===this.resolvedMode?"dark":"light"):this.setMode("light"===this.currentMode?"dark":"light")}subscribe(e){return this.listeners.add(e),()=>{this.listeners.delete(e)}}updateConfig(e){this.config={...this.config,...e},this.applyTheme(),this.notifyListeners()}getCurrentTheme(){return this.config.themes[this.resolvedMode]}applyTheme(){}applyCSSVariables(e){let t=document.documentElement,{colors:r,spacing:i,typography:a,shadows:n,radii:o,transitions:s}=e;Object.entries(r).forEach(([e,r])=>{"object"==typeof r&&null!==r&&Object.entries(r).forEach(([r,i])=>{t.style.setProperty(`--color-${e}-${r}`,i)})}),Object.entries(i).forEach(([e,r])=>{t.style.setProperty(`--spacing-${e}`,r)}),Object.entries(a.fontSize).forEach(([e,[r,{lineHeight:i}]])=>{t.style.setProperty(`--font-size-${e}`,r),t.style.setProperty(`--line-height-${e}`,i)}),Object.entries(a.fontWeight).forEach(([e,r])=>{t.style.setProperty(`--font-weight-${e}`,r)}),Object.entries(a.letterSpacing).forEach(([e,r])=>{t.style.setProperty(`--letter-spacing-${e}`,r)}),Object.entries(n).forEach(([e,r])=>{t.style.setProperty(`--shadow-${e}`,r)}),Object.entries(o).forEach(([e,r])=>{t.style.setProperty(`--radius-${e}`,r)}),Object.entries(s.duration).forEach(([e,r])=>{t.style.setProperty(`--duration-${e}`,r)}),Object.entries(s.timing).forEach(([e,r])=>{t.style.setProperty(`--timing-${e}`,r)})}applyMetaThemeColor(e){let t=document.querySelector('meta[name="theme-color"]');t||((t=document.createElement("meta")).setAttribute("name","theme-color"),document.head.appendChild(t)),t.setAttribute("content",e.colors.background.primary)}setupMediaQuery(){this.config.enableSystemTheme&&(this.mediaQuery=window.matchMedia("(prefers-color-scheme: dark)"),this.mediaQuery.addEventListener("change",this.handleMediaQueryChange.bind(this)))}handleMediaQueryChange(){if("system"===this.currentMode){let e=this.resolveMode("system");e!==this.resolvedMode&&(this.resolvedMode=e,this.applyTheme(),this.notifyListeners())}}resolveMode(e){return"system"===e?"light":e}getStoredMode(){return null}storeMode(e){}startTransition(){this.isTransitioning=!0,document.documentElement.classList.add("theme-transitioning"),this.notifyListeners()}endTransition(){this.isTransitioning=!1,document.documentElement.classList.remove("theme-transitioning"),this.notifyListeners()}notifyListeners(){let e=this.getContextValue();this.listeners.forEach(t=>t(e))}destroy(){this.mediaQuery&&this.mediaQuery.removeEventListener("change",this.handleMediaQueryChange.bind(this)),this.listeners.clear()}}let s=null,l=(0,a.createContext)(void 0);function c({children:e,config:t,enableTransitions:r=!0,transitionDuration:c=200}){let[d]=(0,a.useState)(()=>{var e;return e={...n,...t,enableTransitions:r,transitionDuration:c},s||(s=new o(e)),s}),[m,u]=(0,a.useState)(d.getContextValue());return i.jsx(l.Provider,{value:m,children:e})}},5348:(e,t,r)=>{"use strict";r.d(t,{ToastProvider:()=>n});var i=r(19899),a=r(90832);function n({children:e}){return(0,i.jsxs)(i.Fragment,{children:[e,i.jsx(a.x7,{position:"top-right",gutter:8,containerClassName:"",containerStyle:{},toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff",fontSize:"14px",fontWeight:"500",padding:"12px 16px",borderRadius:"8px",boxShadow:"0 4px 12px rgba(0, 0, 0, 0.15)"},success:{duration:3e3,style:{background:"#10b981",color:"#fff"},iconTheme:{primary:"#fff",secondary:"#10b981"}},error:{duration:5e3,style:{background:"#ef4444",color:"#fff"},iconTheme:{primary:"#fff",secondary:"#ef4444"}},loading:{style:{background:"#3b82f6",color:"#fff"},iconTheme:{primary:"#fff",secondary:"#3b82f6"}}}})]})}},77406:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f,metadata:()=>h});var i=r(35023),a=r(86232),n=r.n(a);r(40642);var o=r(2772);let s=(0,o.createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\components\auth\AuthProvider.tsx#AuthProvider`),l=(0,o.createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\components\providers\QueryProvider.tsx#QueryProvider`),c=(0,o.createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\components\providers\ToastProvider.tsx#ToastProvider`);(0,o.createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\components\providers\ThemeProvider.tsx#useTheme`);let d=(0,o.createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\components\providers\ThemeProvider.tsx#ThemeProvider`);(0,o.createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\components\accessibility\A11yProvider.tsx#useA11y`);let m=(0,o.createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\components\accessibility\A11yProvider.tsx#A11yProvider`);(0,o.createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\components\accessibility\A11yProvider.tsx#withA11y`),(0,o.createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\components\accessibility\A11yProvider.tsx#useKeyboardShortcuts`),(0,o.createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\components\accessibility\A11yProvider.tsx#useFocusManagement`),(0,o.createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\components\accessibility\A11yProvider.tsx#useAnnouncements`),(0,o.createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\components\accessibility\A11yProvider.tsx#SkipLinks`);let u=(0,o.createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\components\organization\OrganizationProvider.tsx#OrganizationProvider`);(0,o.createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\components\organization\OrganizationProvider.tsx#useOrganizationSwitcher`),(0,o.createProxy)(String.raw`C:\laragon\www\max\trae\kilo-teset\augsyanapseAI\frontend\components\organization\OrganizationProvider.tsx#useOrganizationPermissions`);let h={title:{default:"SynapseAI - Universal AI Orchestration Platform",template:"%s | SynapseAI"},description:"Build, deploy, and manage AI agents, tools, and workflows with SynapseAI's no-code platform.",keywords:["AI","artificial intelligence","automation","no-code","agents","workflows","orchestration","SynapseAI"],authors:[{name:"SynapseAI Team"}],creator:"SynapseAI",publisher:"SynapseAI",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("http://localhost:3001"),alternates:{canonical:"/"},openGraph:{type:"website",locale:"en_US",url:"/",title:"SynapseAI - Universal AI Orchestration Platform",description:"Build, deploy, and manage AI agents, tools, and workflows with SynapseAI's no-code platform.",siteName:"SynapseAI",images:[{url:"/og-image.png",width:1200,height:630,alt:"SynapseAI Platform"}]},twitter:{card:"summary_large_image",title:"SynapseAI - Universal AI Orchestration Platform",description:"Build, deploy, and manage AI agents, tools, and workflows with SynapseAI's no-code platform.",images:["/og-image.png"],creator:"@synapseai"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:process.env.GOOGLE_SITE_VERIFICATION}};function f({children:e}){return(0,i.jsxs)("html",{lang:"en",className:n().variable,suppressHydrationWarning:!0,children:[(0,i.jsxs)("head",{children:[i.jsx("link",{rel:"icon",href:"/favicon.ico",sizes:"any"}),i.jsx("link",{rel:"icon",href:"/favicon.svg",type:"image/svg+xml"}),i.jsx("link",{rel:"apple-touch-icon",href:"/apple-touch-icon.png"}),i.jsx("link",{rel:"manifest",href:"/manifest.json"}),i.jsx("meta",{name:"theme-color",content:"#0ea5e9"}),i.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1, maximum-scale=5"})]}),i.jsx("body",{className:"min-h-screen bg-background font-sans antialiased",children:i.jsx(d,{enableTransitions:!0,transitionDuration:200,children:i.jsx(m,{children:i.jsx(s,{children:i.jsx(u,{children:i.jsx(l,{children:i.jsx(c,{children:i.jsx("div",{className:"relative flex min-h-screen flex-col",children:i.jsx("div",{className:"flex-1",children:e})})})})})})})})})]})}},40642:()=>{}};