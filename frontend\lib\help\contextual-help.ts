import { apiClient } from '@/lib/api';

export interface HelpContext {
  page: string;
  section?: string;
  element?: string;
  userAction?: string;
  userLevel: 'beginner' | 'intermediate' | 'advanced';
  currentData?: Record<string, any>;
  errorState?: {
    type: string;
    message: string;
    field?: string;
  };
  sessionHistory?: Array<{
    action: string;
    timestamp: number;
    success: boolean;
  }>;
}

export interface HelpContent {
  id: string;
  title: string;
  content: string;
  type: 'explanation' | 'tutorial' | 'troubleshooting' | 'reference' | 'tip';
  priority: 'low' | 'medium' | 'high' | 'critical';
  format: 'text' | 'markdown' | 'video' | 'interactive';
  media?: {
    type: 'image' | 'video' | 'gif' | 'diagram';
    url: string;
    alt?: string;
    caption?: string;
  };
  actions?: Array<{
    type: 'link' | 'button' | 'tutorial' | 'demo';
    label: string;
    action: string;
    data?: any;
  }>;
  relatedTopics?: string[];
  tags: string[];
  confidence: number;
  source: 'ai' | 'documentation' | 'community' | 'support';
}

export interface HelpSuggestion {
  content: HelpContent;
  relevance: number;
  reasoning: string;
  trigger: 'proactive' | 'reactive' | 'requested';
  timing: 'immediate' | 'delayed' | 'contextual';
}

export interface SmartHelpOptions {
  enableProactive: boolean;
  enableAI: boolean;
  showTips: boolean;
  autoShow: boolean;
  preferredFormat: 'text' | 'video' | 'interactive';
  maxSuggestions: number;
}

class ContextualHelp {
  private context: HelpContext | null = null;
  private options: SmartHelpOptions;
  private helpCache: Map<string, HelpContent[]> = new Map();
  private eventListeners: Map<string, Function[]> = new Map();
  private activeHelp: HelpSuggestion[] = [];
  private helpHistory: Array<{ contentId: string; timestamp: number; helpful: boolean }> = [];

  constructor(options: Partial<SmartHelpOptions> = {}) {
    this.options = {
      enableProactive: true,
      enableAI: true,
      showTips: true,
      autoShow: false,
      preferredFormat: 'text',
      maxSuggestions: 5,
      ...options,
    };

    this.setupContextDetection();
  }

  /**
   * Update current context
   */
  updateContext(context: Partial<HelpContext>): void {
    this.context = { ...this.context, ...context } as HelpContext;
    
    if (this.options.enableProactive) {
      this.triggerProactiveHelp();
    }
    
    this.emit('context_updated', { context: this.context });
  }

  /**
   * Get contextual help suggestions
   */
  async getHelp(query?: string): Promise<HelpSuggestion[]> {
    if (!this.context) {
      throw new Error('No context available for help');
    }

    try {
      // Try AI-powered help first
      if (this.options.enableAI) {
        const aiHelp = await this.getAIHelp(query);
        if (aiHelp.length > 0) {
          return this.filterAndRankSuggestions(aiHelp);
        }
      }
    } catch (error) {
      console.warn('AI help failed, using static fallback:', error);
    }

    // Fallback to static help
    const staticHelp = await this.getStaticHelp(query);
    return this.filterAndRankSuggestions(staticHelp);
  }

  /**
   * Get help for specific error
   */
  async getErrorHelp(error: HelpContext['errorState']): Promise<HelpSuggestion[]> {
    if (!error) return [];

    const errorContext = {
      ...this.context,
      errorState: error,
    } as HelpContext;

    try {
      const response = await apiClient.post('/api/v1/help/error-assistance', {
        context: errorContext,
        options: this.options,
      });

      return response.data.suggestions.map((suggestion: any) => ({
        content: suggestion.content,
        relevance: suggestion.relevance,
        reasoning: suggestion.reasoning,
        trigger: 'reactive' as const,
        timing: 'immediate' as const,
      }));
    } catch (error) {
      return this.getStaticErrorHelp(errorContext.errorState!);
    }
  }

  /**
   * Get step-by-step guidance
   */
  async getStepByStepGuidance(goal: string): Promise<HelpContent[]> {
    try {
      const response = await apiClient.post('/api/v1/help/step-by-step', {
        goal,
        context: this.context,
        userLevel: this.context?.userLevel || 'beginner',
      });

      return response.data.steps;
    } catch (error) {
      return this.getStaticStepByStep(goal);
    }
  }

  /**
   * Search help content
   */
  async searchHelp(query: string): Promise<HelpContent[]> {
    const cacheKey = `search:${query}`;
    
    if (this.helpCache.has(cacheKey)) {
      return this.helpCache.get(cacheKey)!;
    }

    try {
      const response = await apiClient.post('/api/v1/help/search', {
        query,
        context: this.context,
        options: this.options,
      });

      const results = response.data.results as HelpContent[];
      this.helpCache.set(cacheKey, results);
      
      return results;
    } catch (error) {
      return this.searchStaticHelp(query);
    }
  }

  /**
   * Mark help as helpful or not
   */
  markHelpful(contentId: string, helpful: boolean): void {
    this.helpHistory.push({
      contentId,
      timestamp: Date.now(),
      helpful,
    });

    // Send feedback to improve AI suggestions
    if (this.options.enableAI) {
      apiClient.post('/api/v1/help/feedback', {
        contentId,
        helpful,
        context: this.context,
      }).catch(console.error);
    }

    this.emit('help_feedback', { contentId, helpful });
  }

  /**
   * Get personalized tips
   */
  async getPersonalizedTips(): Promise<HelpContent[]> {
    if (!this.options.showTips) return [];

    try {
      const response = await apiClient.post('/api/v1/help/personalized-tips', {
        context: this.context,
        history: this.helpHistory,
        userLevel: this.context?.userLevel || 'beginner',
      });

      return response.data.tips;
    } catch (error) {
      return this.getStaticTips();
    }
  }

  /**
   * Update help options
   */
  updateOptions(options: Partial<SmartHelpOptions>): void {
    this.options = { ...this.options, ...options };
    this.emit('options_updated', { options: this.options });
  }

  /**
   * Get help statistics
   */
  getStats(): {
    totalHelpViewed: number;
    helpfulCount: number;
    unhelpfulCount: number;
    topTopics: string[];
    averageRelevance: number;
  } {
    const totalHelpViewed = this.helpHistory.length;
    const helpfulCount = this.helpHistory.filter(h => h.helpful).length;
    const unhelpfulCount = this.helpHistory.filter(h => !h.helpful).length;
    
    return {
      totalHelpViewed,
      helpfulCount,
      unhelpfulCount,
      topTopics: [], // Would be calculated from actual data
      averageRelevance: 0.8, // Would be calculated from actual data
    };
  }

  /**
   * Add event listener
   */
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  /**
   * Remove event listener
   */
  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * Private methods
   */
  private async getAIHelp(query?: string): Promise<HelpSuggestion[]> {
    const response = await apiClient.post('/api/v1/help/ai-assistance', {
      query,
      context: this.context,
      options: this.options,
      history: this.helpHistory.slice(-10), // Last 10 interactions
    });

    return response.data.suggestions;
  }

  private async getStaticHelp(query?: string): Promise<HelpSuggestion[]> {
    // Static help based on context and query
    const suggestions: HelpSuggestion[] = [];

    if (this.context?.page === 'agent-builder') {
      suggestions.push({
        content: {
          id: 'agent-builder-basics',
          title: 'Agent Builder Basics',
          content: 'Learn how to create and configure AI agents using our visual builder.',
          type: 'tutorial',
          priority: 'medium',
          format: 'text',
          tags: ['agent', 'builder', 'basics'],
          confidence: 0.8,
          source: 'documentation',
        },
        relevance: 0.9,
        reasoning: 'User is on agent builder page',
        trigger: 'proactive',
        timing: 'contextual',
      });
    }

    if (this.context?.errorState) {
      suggestions.push({
        content: {
          id: 'error-troubleshooting',
          title: 'Troubleshooting Common Errors',
          content: 'Common solutions for the most frequent issues users encounter.',
          type: 'troubleshooting',
          priority: 'high',
          format: 'text',
          tags: ['error', 'troubleshooting'],
          confidence: 0.7,
          source: 'documentation',
        },
        relevance: 0.95,
        reasoning: 'User encountered an error',
        trigger: 'reactive',
        timing: 'immediate',
      });
    }

    return suggestions;
  }

  private getStaticErrorHelp(error: NonNullable<HelpContext['errorState']>): HelpSuggestion[] {
    const errorHelp: Record<string, HelpContent> = {
      validation_error: {
        id: 'validation-error-help',
        title: 'Fixing Validation Errors',
        content: 'Validation errors occur when required fields are missing or contain invalid data. Check that all required fields are filled correctly.',
        type: 'troubleshooting',
        priority: 'high',
        format: 'text',
        actions: [
          {
            type: 'button',
            label: 'Check Required Fields',
            action: 'highlight_required_fields',
          },
        ],
        tags: ['validation', 'error', 'form'],
        confidence: 0.9,
        source: 'documentation',
      },
      connection_error: {
        id: 'connection-error-help',
        title: 'Connection Issues',
        content: 'Connection errors can be caused by network issues or server problems. Try refreshing the page or checking your internet connection.',
        type: 'troubleshooting',
        priority: 'high',
        format: 'text',
        actions: [
          {
            type: 'button',
            label: 'Retry Connection',
            action: 'retry_connection',
          },
        ],
        tags: ['connection', 'network', 'error'],
        confidence: 0.8,
        source: 'documentation',
      },
    };

    const content = errorHelp[error.type];
    if (!content) return [];

    return [{
      content,
      relevance: 0.95,
      reasoning: `Specific help for ${error.type}`,
      trigger: 'reactive',
      timing: 'immediate',
    }];
  }

  private getStaticStepByStep(goal: string): HelpContent[] {
    const guides: Record<string, HelpContent[]> = {
      'create_agent': [
        {
          id: 'step-1-name-agent',
          title: 'Step 1: Name Your Agent',
          content: 'Choose a descriptive name for your AI agent that reflects its purpose.',
          type: 'tutorial',
          priority: 'medium',
          format: 'text',
          tags: ['agent', 'creation', 'step1'],
          confidence: 0.9,
          source: 'documentation',
        },
        {
          id: 'step-2-configure-agent',
          title: 'Step 2: Configure Settings',
          content: 'Set up your agent\'s behavior, model, and parameters.',
          type: 'tutorial',
          priority: 'medium',
          format: 'text',
          tags: ['agent', 'configuration', 'step2'],
          confidence: 0.9,
          source: 'documentation',
        },
      ],
    };

    return guides[goal] || [];
  }

  private searchStaticHelp(query: string): HelpContent[] {
    // Simple static search implementation
    const allContent: HelpContent[] = [
      {
        id: 'agent-basics',
        title: 'AI Agent Basics',
        content: 'Learn the fundamentals of creating and managing AI agents.',
        type: 'explanation',
        priority: 'medium',
        format: 'text',
        tags: ['agent', 'basics', 'fundamentals'],
        confidence: 0.8,
        source: 'documentation',
      },
      {
        id: 'workflow-builder',
        title: 'Visual Workflow Builder',
        content: 'How to use the drag-and-drop interface to build AI workflows.',
        type: 'tutorial',
        priority: 'medium',
        format: 'text',
        tags: ['workflow', 'builder', 'visual'],
        confidence: 0.8,
        source: 'documentation',
      },
    ];

    const normalizedQuery = query.toLowerCase();
    return allContent.filter(content =>
      content.title.toLowerCase().includes(normalizedQuery) ||
      content.content.toLowerCase().includes(normalizedQuery) ||
      content.tags.some(tag => tag.includes(normalizedQuery))
    );
  }

  private getStaticTips(): HelpContent[] {
    return [
      {
        id: 'tip-keyboard-shortcuts',
        title: 'Keyboard Shortcuts',
        content: 'Use Ctrl+S to save, Ctrl+Z to undo, and Ctrl+Y to redo.',
        type: 'tip',
        priority: 'low',
        format: 'text',
        tags: ['shortcuts', 'productivity'],
        confidence: 0.7,
        source: 'documentation',
      },
    ];
  }

  private filterAndRankSuggestions(suggestions: HelpSuggestion[]): HelpSuggestion[] {
    // Filter by user level and preferences
    let filtered = suggestions.filter(suggestion => {
      // Filter by format preference
      if (this.options.preferredFormat !== 'text' && 
          suggestion.content.format !== this.options.preferredFormat) {
        return false;
      }
      
      return true;
    });

    // Sort by relevance and priority
    filtered.sort((a, b) => {
      const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
      const aPriority = priorityOrder[a.content.priority];
      const bPriority = priorityOrder[b.content.priority];
      
      if (aPriority !== bPriority) {
        return bPriority - aPriority;
      }
      
      return b.relevance - a.relevance;
    });

    // Limit results
    return filtered.slice(0, this.options.maxSuggestions);
  }

  private setupContextDetection(): void {
    // Detect context changes automatically
    if (typeof window !== 'undefined') {
      // URL changes
      let currentUrl = window.location.pathname;
      const checkUrlChange = () => {
        if (window.location.pathname !== currentUrl) {
          currentUrl = window.location.pathname;
          this.updateContext({ page: currentUrl });
        }
      };
      
      setInterval(checkUrlChange, 1000);

      // Error detection
      window.addEventListener('error', (event) => {
        this.updateContext({
          errorState: {
            type: 'javascript_error',
            message: event.message,
          },
        });
      });
    }
  }

  private async triggerProactiveHelp(): Promise<void> {
    if (!this.options.enableProactive || !this.context) return;

    try {
      const suggestions = await this.getHelp();
      const proactiveSuggestions = suggestions.filter(s => s.trigger === 'proactive');
      
      if (proactiveSuggestions.length > 0) {
        this.activeHelp = proactiveSuggestions;
        this.emit('proactive_help_available', { suggestions: proactiveSuggestions });
      }
    } catch (error) {
      console.error('Failed to get proactive help:', error);
    }
  }

  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => callback(data));
    }
  }
}

export { ContextualHelp };
