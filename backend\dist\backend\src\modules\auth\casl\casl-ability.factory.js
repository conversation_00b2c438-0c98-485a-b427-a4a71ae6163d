"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CaslAbilityFactory = void 0;
const common_1 = require("@nestjs/common");
const ability_1 = require("@casl/ability");
const client_1 = require("@prisma/client");
let CaslAbilityFactory = class CaslAbilityFactory {
    createForUser(user) {
        const { can, cannot, build } = new ability_1.AbilityBuilder(ability_1.Ability);
        switch (user.role) {
            case client_1.UserRole.SUPER_ADMIN:
                can('manage', 'all');
                break;
            case client_1.UserRole.ORG_ADMIN:
                can('manage', 'User');
                can('manage', 'Agent');
                can('manage', 'Tool');
                can('manage', 'Workflow');
                can('manage', 'Template');
                can('manage', 'Widget');
                can('manage', 'Session');
                can('manage', 'Notification');
                can('manage', 'Document');
                can('manage', 'Sandbox');
                can('read', 'Provider');
                can('read', 'Analytics');
                can('read', 'Billing');
                cannot('update', 'Organization');
                cannot('delete', 'Organization');
                cannot('manage', 'Billing');
                break;
            case client_1.UserRole.DEVELOPER:
                can('read', 'User');
                can('manage', 'Agent');
                can('manage', 'Tool');
                can('manage', 'Workflow');
                can('manage', 'Template');
                can('manage', 'Widget');
                can('manage', 'Session');
                can('manage', 'Document');
                can('manage', 'Sandbox');
                can('read', 'Provider');
                can('read', 'Analytics');
                can('read', 'Notification');
                can('execute', 'Agent');
                can('execute', 'Tool');
                can('execute', 'Workflow');
                break;
            case client_1.UserRole.VIEWER:
                can('read', 'Agent');
                can('read', 'Tool');
                can('read', 'Workflow');
                can('read', 'Template');
                can('read', 'Widget');
                can('read', 'Provider');
                can('read', 'Analytics');
                can('read', 'Document');
                can('read', 'Notification');
                can('read', 'Session');
                can('read', 'Sandbox');
                can('execute', 'Agent');
                can('execute', 'Tool');
                can('execute', 'Workflow');
                break;
            default:
                break;
        }
        cannot('delete', 'Organization');
        cannot('manage', 'User', { role: client_1.UserRole.SUPER_ADMIN });
        return build({
            detectSubjectType: (item) => item.constructor?.name,
        });
    }
    createForApiKey(organizationId, permissions) {
        const { can, build } = new ability_1.AbilityBuilder(ability_1.Ability);
        permissions.forEach(permission => {
            const [action, subject] = permission.split(':');
            if (action && subject) {
                can(action, subject);
            }
        });
        return build({
            detectSubjectType: (item) => item.constructor?.name,
        });
    }
};
exports.CaslAbilityFactory = CaslAbilityFactory;
exports.CaslAbilityFactory = CaslAbilityFactory = __decorate([
    (0, common_1.Injectable)()
], CaslAbilityFactory);
//# sourceMappingURL=casl-ability.factory.js.map