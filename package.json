{"name": "synapseai", "version": "1.0.0", "description": "SynapseAI - Universal AI Orchestration Platform", "private": true, "workspaces": ["backend", "frontend", "shared"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:shared && npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "build:shared": "cd shared && npm run build", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm run test", "test:frontend": "cd frontend && npm run test", "test:e2e": "cd backend && npm run test:e2e", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "type-check": "npm run type-check:backend && npm run type-check:frontend", "type-check:backend": "cd backend && npm run type-check", "type-check:frontend": "cd frontend && npm run type-check", "db:migrate": "cd backend && npm run db:migrate", "db:seed": "cd backend && npm run db:seed", "db:studio": "cd backend && npm run db:studio", "docker:dev": "docker-compose -f docker-compose.dev.yml up", "docker:prod": "docker-compose -f docker-compose.prod.yml up", "docker:build": "docker-compose build", "install:all": "npm install && npm run install:backend && npm run install:frontend && npm run install:shared", "install:backend": "cd backend && npm install", "install:frontend": "cd frontend && npm install", "install:shared": "cd shared && npm install"}, "devDependencies": {"concurrently": "^8.2.2", "cross-env": "^7.0.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}