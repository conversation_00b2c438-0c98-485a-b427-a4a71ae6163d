import { Module } from '@nestjs/common';
import { TerminusModule } from '@nestjs/terminus';
import { HealthController } from './health.controller';
import { HealthService } from './health.service';
import { PrismaHealthIndicator } from '@modules/prisma/prisma.health';
import { RedisHealthIndicator } from '@modules/redis/redis.health';

@Module({
  imports: [TerminusModule],
  controllers: [HealthController],
  providers: [HealthService, PrismaHealthIndicator, RedisHealthIndicator],
  exports: [HealthService],
})
export class HealthModule {}
