{"version": 3, "file": "api-key.service.js", "sourceRoot": "", "sources": ["../../../../../src/modules/auth/api-key.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAAwF;AACxF,6DAA+D;AAC/D,yEAAgE;AAChE,iCAAiC;AAyB1B,IAAM,aAAa,GAAnB,MAAM,aAAa;IACxB,YACU,aAA4B,EAC5B,MAAqB;QADrB,kBAAa,GAAb,aAAa,CAAe;QAC5B,WAAM,GAAN,MAAM,CAAe;IAC5B,CAAC;IAKJ,KAAK,CAAC,YAAY,CAAC,SAA0B;QAE3C,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACpD,MAAM,SAAS,GAAG,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACzD,MAAM,MAAM,GAAG,MAAM,KAAK,IAAI,SAAS,EAAE,CAAC;QAC1C,MAAM,SAAS,GAAG,MAAM,KAAK,EAAE,CAAC;QAGhC,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAEzE,IAAI,CAAC;YAEH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC;gBAC1D,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS,CAAC,IAAI;oBACpB,WAAW,EAAE,SAAS,CAAC,WAAW;oBAClC,OAAO;oBACP,SAAS;oBACT,WAAW,EAAE,SAAS,CAAC,WAAW;oBAClC,SAAS,EAAE,SAAS,CAAC,SAAS;oBAC9B,cAAc,EAAE,SAAS,CAAC,cAAc;oBACxC,SAAS,EAAE,SAAS,CAAC,SAAS;oBAC9B,QAAQ,EAAE,IAAI;iBACf;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,SAAS,EAAE;gBAC9C,MAAM,EAAE,SAAS,CAAC,SAAS;gBAC3B,cAAc,EAAE,SAAS,CAAC,cAAc;gBACxC,QAAQ,EAAE,SAAS;gBACnB,OAAO,EAAE;oBACP,QAAQ,EAAE,YAAY,CAAC,EAAE;oBACzB,WAAW,EAAE,SAAS,CAAC,WAAW;iBACnC;aACF,CAAC,CAAC;YAEH,OAAO;gBACL,EAAE,EAAE,YAAY,CAAC,EAAE;gBACnB,IAAI,EAAE,YAAY,CAAC,IAAI;gBACvB,WAAW,EAAE,YAAY,CAAC,WAAW;gBACrC,WAAW,EAAE,YAAY,CAAC,WAAuB;gBACjD,GAAG,EAAE,MAAM;gBACX,SAAS,EAAE,YAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,YAAY,CAAC,SAAS;gBACjC,QAAQ,EAAE,YAAY,CAAC,QAAQ;gBAC/B,UAAU,EAAE,YAAY,CAAC,UAAU;gBACnC,SAAS,EAAE,YAAY,CAAC,SAAS;aAClC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,IAAI,4BAAmB,CAAC,0BAA0B,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,cAAc,CAAC,MAAc;QACjC,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;YACzC,MAAM,IAAI,8BAAqB,CAAC,wBAAwB,CAAC,CAAC;QAC5D,CAAC;QAGD,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAG1C,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAEzE,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC;gBAC7D,KAAK,EAAE;oBACL,OAAO;oBACP,SAAS;oBACT,QAAQ,EAAE,IAAI;iBACf;gBACD,OAAO,EAAE;oBACP,YAAY,EAAE;wBACZ,MAAM,EAAE;4BACN,EAAE,EAAE,IAAI;4BACR,IAAI,EAAE,IAAI;4BACV,IAAI,EAAE,IAAI;4BACV,QAAQ,EAAE,IAAI;yBACf;qBACF;oBACD,OAAO,EAAE;wBACP,MAAM,EAAE;4BACN,EAAE,EAAE,IAAI;4BACR,KAAK,EAAE,IAAI;4BACX,SAAS,EAAE,IAAI;4BACf,QAAQ,EAAE,IAAI;yBACf;qBACF;iBACF;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,YAAY,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;gBACzD,MAAM,IAAI,8BAAqB,CAAC,6BAA6B,CAAC,CAAC;YACjE,CAAC;YAGD,IAAI,YAAY,CAAC,SAAS,IAAI,YAAY,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,EAAE,CAAC;gBAClE,MAAM,IAAI,8BAAqB,CAAC,qBAAqB,CAAC,CAAC;YACzD,CAAC;YAGD,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC;gBACrC,KAAK,EAAE,EAAE,EAAE,EAAE,YAAY,CAAC,EAAE,EAAE;gBAC9B,IAAI,EAAE,EAAE,UAAU,EAAE,IAAI,IAAI,EAAE,EAAE;aACjC,CAAC,CAAC;YAEH,OAAO;gBACL,QAAQ,EAAE,YAAY,CAAC,EAAE;gBACzB,cAAc,EAAE,YAAY,CAAC,cAAc;gBAC3C,WAAW,EAAE,YAAY,CAAC,WAAuB;gBACjD,YAAY,EAAE,YAAY,CAAC,YAAY;gBACvC,OAAO,EAAE,YAAY,CAAC,OAAO;aAC9B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,8BAAqB,EAAE,CAAC;gBAC3C,MAAM,KAAK,CAAC;YACd,CAAC;YACD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,IAAI,8BAAqB,CAAC,iBAAiB,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,WAAW,CAAC,cAAsB,EAAE,MAAc;QACtD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC;YACvD,KAAK,EAAE;gBACL,cAAc;aAGf;YACD,MAAM,EAAE;gBACN,EAAE,EAAE,IAAI;gBACR,IAAI,EAAE,IAAI;gBACV,WAAW,EAAE,IAAI;gBACjB,WAAW,EAAE,IAAI;gBACjB,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,IAAI;gBACd,UAAU,EAAE,IAAI;gBAChB,SAAS,EAAE,IAAI;aAChB;YACD,OAAO,EAAE;gBACP,SAAS,EAAE,MAAM;aAClB;SACF,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACzB,GAAG,GAAG;YACN,WAAW,EAAE,GAAG,CAAC,WAAuB;SACzC,CAAC,CAAC,CAAC;IACN,CAAC;IAKD,KAAK,CAAC,YAAY,CAAC,QAAgB,EAAE,cAAsB,EAAE,MAAc;QACzE,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC;gBACvD,KAAK,EAAE;oBACL,EAAE,EAAE,QAAQ;oBACZ,cAAc;iBAEf;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,4BAAmB,CAAC,mBAAmB,CAAC,CAAC;YACrD,CAAC;YAED,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC;gBACrC,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE;gBACvB,IAAI,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE;aAC1B,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,SAAS,EAAE;gBAC9C,MAAM;gBACN,cAAc;gBACd,QAAQ,EAAE,SAAS;gBACnB,OAAO,EAAE;oBACP,QAAQ;iBACT;aACF,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,IAAI,4BAAmB,CAAC,0BAA0B,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,cAAc,CAAC,cAAsB;QACzC,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC;YACpD,EAAE,EAAE,CAAC,UAAU,CAAC;YAChB,KAAK,EAAE,EAAE,cAAc,EAAE;YACzB,MAAM,EAAE,IAAI;SACb,CAAC,CAAC;QAEH,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACpE,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC;QAClE,MAAM,YAAY,GAAG,SAAS,GAAG,UAAU,CAAC;QAE5C,OAAO;YACL,KAAK,EAAE,SAAS;YAChB,MAAM,EAAE,UAAU;YAClB,QAAQ,EAAE,YAAY;SACvB,CAAC;IACJ,CAAC;CACF,CAAA;AA/NY,sCAAa;wBAAb,aAAa;IADzB,IAAA,mBAAU,GAAE;qCAGc,8BAAa;QACpB,8BAAa;GAHpB,aAAa,CA+NzB"}