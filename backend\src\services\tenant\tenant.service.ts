import { Injectable, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '@modules/prisma/prisma.service';
import { LoggerService } from '@services/logger/logger.service';

export interface TenantContext {
  organizationId: string;
  userId?: string;
  userRole?: string;
  permissions?: string[];
}

@Injectable()
export class TenantService {
  constructor(
    private prismaService: PrismaService,
    private logger: LoggerService,
  ) {}

  /**
   * Create a tenant-aware Prisma client that automatically filters by organization
   */
  createTenantClient(context: TenantContext) {
    const { organizationId, userId, userRole } = context;

    return {
      // Core entities with automatic organization filtering
      organization: {
        findUnique: (args: any) => 
          this.prismaService.organization.findUnique({
            ...args,
            where: { ...args.where, id: organizationId },
          }),
        
        findFirst: (args: any) => 
          this.prismaService.organization.findFirst({
            ...args,
            where: { ...args.where, id: organizationId },
          }),
        
        update: (args: any) => 
          this.prismaService.organization.update({
            ...args,
            where: { ...args.where, id: organizationId },
          }),
      },

      user: {
        findMany: (args: any = {}) => 
          this.prismaService.user.findMany({
            ...args,
            where: { ...args.where, organizationId },
          }),
        
        findUnique: (args: any) => 
          this.prismaService.user.findUnique({
            ...args,
            where: { ...args.where, organizationId },
          }),
        
        findFirst: (args: any) => 
          this.prismaService.user.findFirst({
            ...args,
            where: { ...args.where, organizationId },
          }),
        
        create: (args: any) => 
          this.prismaService.user.create({
            ...args,
            data: { ...args.data, organizationId },
          }),
        
        update: (args: any) => 
          this.prismaService.user.update({
            ...args,
            where: { ...args.where, organizationId },
          }),
        
        delete: (args: any) => 
          this.prismaService.user.delete({
            ...args,
            where: { ...args.where, organizationId },
          }),
      },

      // Agent system with tenant isolation
      agent: {
        findMany: (args: any = {}) => 
          this.prismaService.agent.findMany({
            ...args,
            where: { ...args.where, organizationId },
          }),
        
        findUnique: (args: any) => 
          this.prismaService.agent.findUnique({
            ...args,
            where: { ...args.where, organizationId },
          }),
        
        create: (args: any) => 
          this.prismaService.agent.create({
            ...args,
            data: { 
              ...args.data, 
              organizationId,
              createdBy: userId || args.data.createdBy,
            },
          }),
        
        update: (args: any) => 
          this.prismaService.agent.update({
            ...args,
            where: { ...args.where, organizationId },
          }),
        
        delete: (args: any) => 
          this.prismaService.agent.delete({
            ...args,
            where: { ...args.where, organizationId },
          }),
      },

      agentExecution: {
        findMany: (args: any = {}) => 
          this.prismaService.agentExecution.findMany({
            ...args,
            where: { ...args.where, organizationId },
          }),
        
        create: (args: any) => 
          this.prismaService.agentExecution.create({
            ...args,
            data: { ...args.data, organizationId },
          }),
      },

      // Tool system with tenant isolation
      tool: {
        findMany: (args: any = {}) => 
          this.prismaService.tool.findMany({
            ...args,
            where: { ...args.where, organizationId },
          }),
        
        findUnique: (args: any) => 
          this.prismaService.tool.findUnique({
            ...args,
            where: { ...args.where, organizationId },
          }),
        
        create: (args: any) => 
          this.prismaService.tool.create({
            ...args,
            data: { 
              ...args.data, 
              organizationId,
              createdBy: userId || args.data.createdBy,
            },
          }),
        
        update: (args: any) => 
          this.prismaService.tool.update({
            ...args,
            where: { ...args.where, organizationId },
          }),
        
        delete: (args: any) => 
          this.prismaService.tool.delete({
            ...args,
            where: { ...args.where, organizationId },
          }),
      },

      toolExecution: {
        findMany: (args: any = {}) => 
          this.prismaService.toolExecution.findMany({
            ...args,
            where: { ...args.where, organizationId },
          }),
        
        create: (args: any) => 
          this.prismaService.toolExecution.create({
            ...args,
            data: { ...args.data, organizationId },
          }),
      },

      // Session management with tenant isolation
      session: {
        findMany: (args: any = {}) => 
          this.prismaService.session.findMany({
            ...args,
            where: { ...args.where, organizationId },
          }),
        
        findUnique: (args: any) => 
          this.prismaService.session.findUnique({
            ...args,
            where: { ...args.where, organizationId },
          }),
        
        create: (args: any) => 
          this.prismaService.session.create({
            ...args,
            data: { ...args.data, organizationId },
          }),
        
        update: (args: any) => 
          this.prismaService.session.update({
            ...args,
            where: { ...args.where, organizationId },
          }),
        
        delete: (args: any) => 
          this.prismaService.session.delete({
            ...args,
            where: { ...args.where, organizationId },
          }),
      },

      // Analytics with tenant isolation
      analytics: {
        findMany: (args: any = {}) => 
          this.prismaService.analytics.findMany({
            ...args,
            where: { ...args.where, organizationId },
          }),
        
        create: (args: any) => 
          this.prismaService.analytics.create({
            ...args,
            data: { ...args.data, organizationId },
          }),
      },

      // Billing with tenant isolation
      billing: {
        findMany: (args: any = {}) => 
          this.prismaService.billing.findMany({
            ...args,
            where: { ...args.where, organizationId },
          }),
        
        findFirst: (args: any) => 
          this.prismaService.billing.findFirst({
            ...args,
            where: { ...args.where, organizationId },
          }),
        
        create: (args: any) => 
          this.prismaService.billing.create({
            ...args,
            data: { ...args.data, organizationId },
          }),
        
        update: (args: any) => 
          this.prismaService.billing.update({
            ...args,
            where: { ...args.where, organizationId },
          }),
      },
    };
  }

  /**
   * Validate that a user has permission to access a resource
   */
  async validateResourceAccess(
    context: TenantContext,
    resource: string,
    action: string,
    resourceId?: string,
  ): Promise<boolean> {
    const { organizationId, userId, userRole, permissions = [] } = context;

    // Super admin has access to everything
    if (userRole === 'SUPER_ADMIN' || permissions.includes('*')) {
      return true;
    }

    // Check specific permission
    const requiredPermission = `${resource}:${action}`;
    if (permissions.includes(requiredPermission)) {
      return true;
    }

    // Additional resource-specific validation
    if (resourceId) {
      const hasAccess = await this.validateSpecificResourceAccess(
        organizationId,
        userId,
        resource,
        resourceId,
      );
      
      if (!hasAccess) {
        this.logger.security('RESOURCE_ACCESS_DENIED', 'medium', {
          organizationId,
          userId,
          details: {
            resource: 'tenant_boundary',
            userRole,
            action,
            resourceId,
          },
        });
        return false;
      }
    }

    return true;
  }

  /**
   * Validate access to specific resource instances
   */
  private async validateSpecificResourceAccess(
    organizationId: string,
    userId: string | undefined,
    resource: string,
    resourceId: string,
  ): Promise<boolean> {
    try {
      switch (resource) {
        case 'agents':
          const agent = await this.prismaService.agent.findFirst({
            where: { id: resourceId, organizationId },
          });
          return !!agent;

        case 'tools':
          const tool = await this.prismaService.tool.findFirst({
            where: { id: resourceId, organizationId },
          });
          return !!tool;

        case 'workflows':
          const workflow = await this.prismaService.workflow.findFirst({
            where: { id: resourceId, organizationId },
          });
          return !!workflow;

        case 'users':
          const user = await this.prismaService.user.findFirst({
            where: { id: resourceId, organizationId },
          });
          return !!user;

        default:
          return true; // Allow access for unknown resources
      }
    } catch (error) {
      this.logger.error('Resource access validation failed:', error);
      return false;
    }
  }

  /**
   * Ensure user can only access their own organization's data
   */
  async enforceOrganizationBoundary(
    organizationId: string,
    userId?: string,
  ): Promise<void> {
    if (!userId) {
      return; // Skip validation for anonymous access
    }

    const user = await this.prismaService.user.findFirst({
      where: { id: userId, organizationId, isActive: true },
    });

    if (!user) {
      throw new ForbiddenException('Access denied: User does not belong to this organization');
    }
  }
}
