'use client';

import { useState, useCallback, useEffect } from 'react';
import { configAssistant, ConfigSuggestion, ConfigContext } from '@/lib/ai/config-assistant';
import { useAppStore } from '@/lib/store';

interface UseConfigAssistantOptions {
  autoLoad?: boolean;
  debounceMs?: number;
}

export function useConfigAssistant(
  context: ConfigContext,
  options: UseConfigAssistantOptions = {}
) {
  const { autoLoad = true, debounceMs = 500 } = options;
  
  const [suggestions, setSuggestions] = useState<ConfigSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [appliedSuggestions, setAppliedSuggestions] = useState<Set<string>>(new Set());
  
  const { addNotification } = useAppStore();

  // Debounced suggestion loading
  const loadSuggestions = useCallback(
    debounce(async (currentContext: ConfigContext) => {
      setIsLoading(true);
      setError(null);
      
      try {
        const response = await configAssistant.getSuggestions(currentContext);
        setSuggestions(response.suggestions);
        
        // Show notification for high-impact suggestions
        const highImpactSuggestions = response.suggestions.filter(s => s.impact === 'high');
        if (highImpactSuggestions.length > 0) {
          addNotification({
            type: 'info',
            title: 'High-Impact Suggestions Available',
            message: `${highImpactSuggestions.length} suggestions that could significantly improve your configuration`,
          });
        }
      } catch (err: any) {
        setError(err.message || 'Failed to load suggestions');
        setSuggestions([]);
      } finally {
        setIsLoading(false);
      }
    }, debounceMs),
    [debounceMs, addNotification]
  );

  // Auto-load suggestions when context changes
  useEffect(() => {
    if (autoLoad) {
      loadSuggestions(context);
    }
  }, [context.currentConfig, context.domain, context.userIntent, autoLoad, loadSuggestions]);

  const applySuggestion = useCallback(async (suggestion: ConfigSuggestion) => {
    try {
      setAppliedSuggestions(prev => new Set([...prev, suggestion.id]));
      
      addNotification({
        type: 'success',
        title: 'Suggestion Applied',
        message: suggestion.title,
      });

      // Reload suggestions after applying one
      setTimeout(() => {
        loadSuggestions(context);
      }, 1000);
      
      return true;
    } catch (err: any) {
      setError(err.message || 'Failed to apply suggestion');
      setAppliedSuggestions(prev => {
        const newSet = new Set(prev);
        newSet.delete(suggestion.id);
        return newSet;
      });
      return false;
    }
  }, [context, loadSuggestions, addNotification]);

  const dismissSuggestion = useCallback((suggestionId: string) => {
    setSuggestions(prev => prev.filter(s => s.id !== suggestionId));
  }, []);

  const analyzeIntent = useCallback(async (input: string) => {
    setIsLoading(true);
    try {
      const result = await configAssistant.analyzeIntent(input, context);
      return result;
    } catch (err: any) {
      setError(err.message || 'Failed to analyze intent');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [context]);

  const generateFromDescription = useCallback(async (
    description: string,
    constraints?: Record<string, any>
  ) => {
    setIsLoading(true);
    try {
      const result = await configAssistant.generateFromDescription(
        description,
        context.domain,
        constraints
      );
      
      addNotification({
        type: 'success',
        title: 'Configuration Generated',
        message: `Generated ${context.domain} configuration from your description`,
      });
      
      return result;
    } catch (err: any) {
      setError(err.message || 'Failed to generate configuration');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [context.domain, addNotification]);

  const validateConfig = useCallback(async (config: Record<string, any>) => {
    setIsLoading(true);
    try {
      const result = await configAssistant.validateConfig(config, context.domain);
      
      if (result.errors.length > 0) {
        const errorCount = result.errors.filter(e => e.severity === 'error').length;
        const warningCount = result.errors.filter(e => e.severity === 'warning').length;
        
        if (errorCount > 0) {
          addNotification({
            type: 'error',
            title: 'Configuration Errors',
            message: `Found ${errorCount} error${errorCount !== 1 ? 's' : ''} that need to be fixed`,
          });
        } else if (warningCount > 0) {
          addNotification({
            type: 'warning',
            title: 'Configuration Warnings',
            message: `Found ${warningCount} warning${warningCount !== 1 ? 's' : ''} to review`,
          });
        }
      }
      
      return result;
    } catch (err: any) {
      setError(err.message || 'Failed to validate configuration');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [context.domain, addNotification]);

  const getContextualHelp = useCallback(async (field: string) => {
    try {
      const help = await configAssistant.getContextualHelp(field, context);
      return help;
    } catch (err: any) {
      setError(err.message || 'Failed to get contextual help');
      throw err;
    }
  }, [context]);

  const getSmartDefaults = useCallback(async (userPreferences?: Record<string, any>) => {
    try {
      const defaults = await configAssistant.getSmartDefaults(context.domain, userPreferences);
      return defaults;
    } catch (err: any) {
      setError(err.message || 'Failed to get smart defaults');
      throw err;
    }
  }, [context.domain]);

  const refreshSuggestions = useCallback(() => {
    loadSuggestions(context);
  }, [context, loadSuggestions]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Filter out applied suggestions
  const activeSuggestions = suggestions.filter(s => !appliedSuggestions.has(s.id));

  return {
    // State
    suggestions: activeSuggestions,
    allSuggestions: suggestions,
    isLoading,
    error,
    appliedSuggestions,
    
    // Actions
    applySuggestion,
    dismissSuggestion,
    analyzeIntent,
    generateFromDescription,
    validateConfig,
    getContextualHelp,
    getSmartDefaults,
    refreshSuggestions,
    clearError,
    
    // Computed
    hasHighImpactSuggestions: activeSuggestions.some(s => s.impact === 'high'),
    suggestionCount: activeSuggestions.length,
    averageConfidence: activeSuggestions.length > 0 
      ? activeSuggestions.reduce((sum, s) => sum + s.confidence, 0) / activeSuggestions.length 
      : 0,
  };
}

// Utility function for debouncing
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}
