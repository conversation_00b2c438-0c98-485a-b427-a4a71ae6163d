"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserRole = exports.UserId = exports.OrganizationId = exports.Tenant = void 0;
const common_1 = require("@nestjs/common");
exports.Tenant = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    if (data) {
        return request.tenant?.[data];
    }
    return request.tenant;
});
exports.OrganizationId = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    return request.organizationId;
});
exports.UserId = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    return request.userId;
});
exports.UserRole = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    return request.userRole;
});
//# sourceMappingURL=tenant-aware.decorator.js.map