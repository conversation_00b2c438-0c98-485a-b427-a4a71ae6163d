{"version": 3, "file": "tenant-aware.decorator.js", "sourceRoot": "", "sources": ["../../../../src/decorators/tenant-aware.decorator.ts"], "names": [], "mappings": ";;;AAAA,2CAAwE;AAM3D,QAAA,MAAM,GAAG,IAAA,6BAAoB,EACxC,CAAC,IAAwB,EAAE,GAAqB,EAAE,EAAE;IAClD,MAAM,OAAO,GAAG,GAAG,CAAC,YAAY,EAAE,CAAC,UAAU,EAAiB,CAAC;IAE/D,IAAI,IAAI,EAAE,CAAC;QACT,OAAO,OAAO,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAED,OAAO,OAAO,CAAC,MAAM,CAAC;AACxB,CAAC,CACF,CAAC;AAKW,QAAA,cAAc,GAAG,IAAA,6BAAoB,EAChD,CAAC,IAAa,EAAE,GAAqB,EAAE,EAAE;IACvC,MAAM,OAAO,GAAG,GAAG,CAAC,YAAY,EAAE,CAAC,UAAU,EAAiB,CAAC;IAC/D,OAAO,OAAO,CAAC,cAAc,CAAC;AAChC,CAAC,CACF,CAAC;AAKW,QAAA,MAAM,GAAG,IAAA,6BAAoB,EACxC,CAAC,IAAa,EAAE,GAAqB,EAAE,EAAE;IACvC,MAAM,OAAO,GAAG,GAAG,CAAC,YAAY,EAAE,CAAC,UAAU,EAAiB,CAAC;IAC/D,OAAO,OAAO,CAAC,MAAM,CAAC;AACxB,CAAC,CACF,CAAC;AAKW,QAAA,QAAQ,GAAG,IAAA,6BAAoB,EAC1C,CAAC,IAAa,EAAE,GAAqB,EAAE,EAAE;IACvC,MAAM,OAAO,GAAG,GAAG,CAAC,YAAY,EAAE,CAAC,UAAU,EAAiB,CAAC;IAC/D,OAAO,OAAO,CAAC,QAAQ,CAAC;AAC1B,CAAC,CACF,CAAC"}