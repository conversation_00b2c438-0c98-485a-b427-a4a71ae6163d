import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy as LdapStrategy } from 'passport-ldapauth';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class ActiveDirectoryStrategy extends PassportStrategy(LdapStrategy, 'ldap') {
  constructor(private configService: ConfigService) {
    super({
      server: {
        url: configService.get('LDAP_URL'),
        bindDN: configService.get('LDAP_BIND_DN'),
        bindCredentials: configService.get('LDAP_BIND_PASSWORD'),
        searchBase: configService.get('LDAP_SEARCH_BASE'),
        searchFilter: '(sAMAccountName={{username}})',
        searchAttributes: [
          'displayName',
          'mail',
          'givenName',
          'sn',
          'memberOf',
          'department',
          'title',
        ],
      },
      usernameField: 'username',
      passwordField: 'password',
    });
  }

  async validate(user: any): Promise<any> {
    // Extract user information from Active Directory
    const groups = user.memberOf || [];
    
    return {
      email: user.mail,
      firstName: user.givenName,
      lastName: user.sn,
      displayName: user.displayName,
      groups: Array.isArray(groups) ? groups : [groups],
      attributes: {
        department: user.department,
        title: user.title,
        ...user,
      },
    };
  }
}
