{"name": "synapseai-frontend", "version": "1.0.0", "description": "SynapseAI Universal AI Orchestration Platform - Frontend", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "next-auth": "^4.24.5", "typescript": "^5.3.3", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "clsx": "^2.0.0", "class-variance-authority": "^0.7.0", "lucide-react": "^0.294.0", "zustand": "^4.4.7", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "axios": "^1.6.2", "react-query": "^3.39.3", "@tanstack/react-query": "^5.14.2", "framer-motion": "^10.16.16", "react-hot-toast": "^2.4.1", "date-fns": "^2.30.0", "recharts": "^2.8.0", "react-markdown": "^9.0.1", "prismjs": "^1.29.0", "react-syntax-highlighter": "^15.5.0"}, "devDependencies": {"@types/jest": "^29.5.8", "@types/prismjs": "^1.26.3", "@types/react-syntax-highlighter": "^15.5.11", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}