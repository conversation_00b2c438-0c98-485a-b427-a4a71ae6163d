import { LoggerService as NestLoggerService } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as winston from 'winston';
export interface LogContext {
    userId?: string;
    organizationId?: string;
    sessionId?: string;
    requestId?: string;
    module?: string;
    action?: string;
    metadata?: Record<string, any>;
}
export declare class LoggerService implements NestLoggerService {
    private configService;
    private logger;
    private context;
    constructor(configService: ConfigService);
    private createLogger;
    setContext(context: string): void;
    log(message: string, context?: string | LogContext): void;
    error(message: string, trace?: string | Error, context?: string | LogContext): void;
    warn(message: string, context?: string | LogContext): void;
    debug(message: string, context?: string | LogContext): void;
    verbose(message: string, context?: string | LogContext): void;
    audit(action: string, result: 'success' | 'failure', context: LogContext & {
        resource?: string;
        details?: Record<string, any>;
    }): void;
    performance(operation: string, duration: number, context: LogContext & {
        success?: boolean;
        metadata?: Record<string, any>;
    }): void;
    security(event: string, severity: 'low' | 'medium' | 'high' | 'critical', context: LogContext & {
        threat?: string;
        action?: string;
        details?: Record<string, any>;
    }): void;
    business(event: string, context: LogContext & {
        value?: number;
        currency?: string;
        properties?: Record<string, any>;
    }): void;
    private formatContext;
    getWinstonLogger(): winston.Logger;
    child(defaultContext: LogContext): LoggerService;
}
