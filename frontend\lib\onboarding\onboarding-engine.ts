export interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  type: 'intro' | 'action' | 'explanation' | 'practice' | 'completion';
  target?: string | undefined; // CSS selector for highlighting
  position?: 'top' | 'bottom' | 'left' | 'right' | 'center' | undefined;
  content: {
    text: string;
    media?: {
      type: 'image' | 'video' | 'gif';
      url: string;
      alt?: string | undefined;
    } | undefined;
    actions?: Array<{
      type: 'click' | 'input' | 'drag' | 'wait';
      target?: string | undefined;
      value?: any;
      description: string;
    }> | undefined;
    tips?: string[] | undefined;
    nextButton?: {
      text: string;
      disabled?: boolean | undefined;
    } | undefined;
  };
  validation?: {
    type: 'element_exists' | 'value_equals' | 'custom';
    target?: string | undefined;
    value?: any;
    validator?: (() => boolean) | undefined;
  } | undefined;
  autoAdvance?: {
    trigger: 'timeout' | 'event' | 'condition';
    delay?: number | undefined;
    event?: string | undefined;
    condition?: (() => boolean) | undefined;
  } | undefined;
  skippable: boolean;
  dependencies?: string[] | undefined;
}

export interface OnboardingFlow {
  id: string;
  name: string;
  description: string;
  category: 'getting_started' | 'feature_intro' | 'advanced_tutorial';
  estimatedTime: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  prerequisites?: string[] | undefined;
  steps: OnboardingStep[];
  completion: {
    badge?: string | undefined;
    certificate?: string | undefined;
    unlocks?: string[] | undefined;
    nextFlows?: string[] | undefined;
  };
}

export interface OnboardingState {
  currentFlow?: string | undefined;
  currentStep: number;
  completedFlows: string[];
  completedSteps: string[];
  skippedSteps: string[];
  userProgress: {
    totalSteps: number;
    completedSteps: number;
    timeSpent: number;
    startedAt?: number | undefined;
  };
  preferences: {
    showHints: boolean;
    autoAdvance: boolean;
    skipAnimations: boolean;
    pauseOnError: boolean;
  };
}

export interface OnboardingContext {
  userLevel: 'beginner' | 'intermediate' | 'advanced';
  userRole: string;
  hasCompletedBasics: boolean;
  availableFeatures: string[];
  currentPage: string;
  sessionData?: Record<string, any>;
}

class OnboardingEngine {
  private flows: Map<string, OnboardingFlow> = new Map();
  private state: OnboardingState;
  private context: OnboardingContext;
  private eventListeners: Map<string, Function[]> = new Map();
  private highlightElement: HTMLElement | null = null;
  private overlayElement: HTMLElement | null = null;

  constructor(initialState?: Partial<OnboardingState>, context?: OnboardingContext) {
    this.state = {
      currentStep: 0,
      completedFlows: [],
      completedSteps: [],
      skippedSteps: [],
      userProgress: {
        totalSteps: 0,
        completedSteps: 0,
        timeSpent: 0,
      },
      preferences: {
        showHints: true,
        autoAdvance: false,
        skipAnimations: false,
        pauseOnError: true,
      },
      ...initialState,
    };

    this.context = context || {
      userLevel: 'beginner',
      userRole: 'user',
      hasCompletedBasics: false,
      availableFeatures: [],
      currentPage: '/',
    };

    this.initializeFlows();
    this.setupEventListeners();
  }

  /**
   * Start an onboarding flow
   */
  startFlow(flowId: string): boolean {
    const flow = this.flows.get(flowId);
    if (!flow) {
      console.error(`Onboarding flow ${flowId} not found`);
      return false;
    }

    // Check prerequisites
    if (flow.prerequisites) {
      const missingPrereqs = flow.prerequisites.filter(
        prereq => !this.state.completedFlows.includes(prereq)
      );
      if (missingPrereqs.length > 0) {
        console.warn(`Missing prerequisites for flow ${flowId}:`, missingPrereqs);
        return false;
      }
    }

    this.state.currentFlow = flowId;
    this.state.currentStep = 0;
    this.state.userProgress.startedAt = Date.now();
    this.state.userProgress.totalSteps = flow.steps.length;

    this.emit('flow_started', { flowId, flow });
    this.showCurrentStep();
    
    return true;
  }

  /**
   * Go to next step
   */
  nextStep(): boolean {
    if (!this.state.currentFlow) return false;

    const flow = this.flows.get(this.state.currentFlow);
    if (!flow) return false;

    const currentStep = flow.steps[this.state.currentStep];
    if (currentStep) {
      // Validate current step
      if (currentStep.validation && !this.validateStep(currentStep)) {
        this.emit('step_validation_failed', { step: currentStep });
        return false;
      }

      // Mark step as completed
      this.state.completedSteps.push(currentStep.id);
      this.state.userProgress.completedSteps++;
      this.emit('step_completed', { step: currentStep });
    }

    // Move to next step
    this.state.currentStep++;

    if (this.state.currentStep >= flow.steps.length) {
      // Flow completed
      this.completeFlow();
      return true;
    }

    this.showCurrentStep();
    return true;
  }

  /**
   * Go to previous step
   */
  previousStep(): boolean {
    if (!this.state.currentFlow || this.state.currentStep <= 0) return false;

    this.state.currentStep--;
    this.showCurrentStep();
    this.emit('step_changed', { stepIndex: this.state.currentStep });
    
    return true;
  }

  /**
   * Skip current step
   */
  skipStep(): boolean {
    if (!this.state.currentFlow) return false;

    const flow = this.flows.get(this.state.currentFlow);
    if (!flow) return false;

    const currentStep = flow.steps[this.state.currentStep];
    if (!currentStep || !currentStep.skippable) return false;

    this.state.skippedSteps.push(currentStep.id);
    this.emit('step_skipped', { step: currentStep });

    return this.nextStep();
  }

  /**
   * Complete current flow
   */
  completeFlow(): void {
    if (!this.state.currentFlow) return;

    const flow = this.flows.get(this.state.currentFlow);
    if (!flow) return;

    this.state.completedFlows.push(this.state.currentFlow);
    this.state.userProgress.timeSpent += Date.now() - (this.state.userProgress.startedAt || 0);

    this.hideOverlay();
    this.emit('flow_completed', { 
      flowId: this.state.currentFlow, 
      flow,
      stats: this.getFlowStats(),
    });

    // Check for unlocked flows
    if (flow.completion.unlocks) {
      this.emit('flows_unlocked', { flows: flow.completion.unlocks });
    }

    this.state.currentFlow = undefined;
    this.state.currentStep = 0;
  }

  /**
   * Pause current flow
   */
  pauseFlow(): void {
    if (!this.state.currentFlow) return;

    this.hideOverlay();
    this.emit('flow_paused', { flowId: this.state.currentFlow });
  }

  /**
   * Resume paused flow
   */
  resumeFlow(): void {
    if (!this.state.currentFlow) return;

    this.showCurrentStep();
    this.emit('flow_resumed', { flowId: this.state.currentFlow });
  }

  /**
   * Stop current flow
   */
  stopFlow(): void {
    if (!this.state.currentFlow) return;

    const flowId = this.state.currentFlow;
    this.hideOverlay();
    
    this.state.currentFlow = undefined;
    this.state.currentStep = 0;
    
    this.emit('flow_stopped', { flowId });
  }

  /**
   * Get available flows for current context
   */
  getAvailableFlows(): OnboardingFlow[] {
    return Array.from(this.flows.values()).filter(flow => {
      // Check prerequisites
      if (flow.prerequisites) {
        const hasPrereqs = flow.prerequisites.every(prereq => 
          this.state.completedFlows.includes(prereq)
        );
        if (!hasPrereqs) return false;
      }

      // Check if already completed
      if (this.state.completedFlows.includes(flow.id)) return false;

      // Check difficulty level
      const difficultyOrder = { beginner: 1, intermediate: 2, advanced: 3 };
      const userLevel = difficultyOrder[this.context.userLevel];
      const flowLevel = difficultyOrder[flow.difficulty];
      
      return flowLevel <= userLevel;
    });
  }

  /**
   * Get current step
   */
  getCurrentStep(): OnboardingStep | null {
    if (!this.state.currentFlow) return null;

    const flow = this.flows.get(this.state.currentFlow);
    if (!flow) return null;

    return flow.steps[this.state.currentStep] || null;
  }

  /**
   * Get flow progress
   */
  getProgress(): { current: number; total: number; percentage: number } {
    if (!this.state.currentFlow) {
      return { current: 0, total: 0, percentage: 0 };
    }

    const flow = this.flows.get(this.state.currentFlow);
    if (!flow) {
      return { current: 0, total: 0, percentage: 0 };
    }

    const current = this.state.currentStep;
    const total = flow.steps.length;
    const percentage = total > 0 ? Math.round((current / total) * 100) : 0;

    return { current, total, percentage };
  }

  /**
   * Update user preferences
   */
  updatePreferences(preferences: Partial<OnboardingState['preferences']>): void {
    this.state.preferences = { ...this.state.preferences, ...preferences };
    this.emit('preferences_updated', { preferences: this.state.preferences });
  }

  /**
   * Get onboarding state
   */
  getState(): OnboardingState {
    return { ...this.state };
  }

  /**
   * Update context
   */
  updateContext(context: Partial<OnboardingContext>): void {
    this.context = { ...this.context, ...context };
    this.emit('context_updated', { context: this.context });
  }

  /**
   * Add event listener
   */
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  /**
   * Remove event listener
   */
  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * Private methods
   */
  private initializeFlows(): void {
    // Initialize default onboarding flows
    this.flows.set('getting_started', {
      id: 'getting_started',
      name: 'Getting Started with SynapseAI',
      description: 'Learn the basics of building AI workflows',
      category: 'getting_started',
      estimatedTime: '10 minutes',
      difficulty: 'beginner',
      steps: [
        {
          id: 'welcome',
          title: 'Welcome to SynapseAI',
          description: 'Your journey to AI mastery begins here',
          type: 'intro',
          content: {
            text: 'Welcome to SynapseAI! We\'ll guide you through creating your first AI agent in just a few minutes.',
            media: {
              type: 'gif',
              url: '/onboarding/welcome.gif',
              alt: 'SynapseAI welcome animation',
            },
            nextButton: { text: 'Let\'s Start!' },
          },
          skippable: false,
        },
        {
          id: 'canvas_intro',
          title: 'Meet the Canvas',
          description: 'This is where you build your AI workflows',
          type: 'explanation',
          target: '.visual-canvas',
          position: 'center',
          content: {
            text: 'This is your canvas - a visual workspace where you can drag and drop components to build powerful AI workflows.',
            tips: [
              'Use mouse wheel to zoom in and out',
              'Click and drag to pan around',
              'Right-click for context menu',
            ],
            nextButton: { text: 'Got it!' },
          },
          skippable: true,
        },
      ],
      completion: {
        badge: 'First Steps',
        unlocks: ['agent_builder'],
      },
    });
  }

  private showCurrentStep(): void {
    const step = this.getCurrentStep();
    if (!step) return;

    this.createOverlay();
    this.highlightTarget(step.target);
    this.emit('step_shown', { step });

    // Handle auto-advance
    if (step.autoAdvance) {
      this.setupAutoAdvance(step);
    }
  }

  private createOverlay(): void {
    // Remove existing overlay
    this.hideOverlay();

    // Create new overlay
    this.overlayElement = document.createElement('div');
    this.overlayElement.className = 'onboarding-overlay';
    this.overlayElement.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      z-index: 9999;
      pointer-events: none;
    `;

    document.body.appendChild(this.overlayElement);
  }

  private highlightTarget(selector?: string): void {
    if (!selector) return;

    const element = document.querySelector(selector) as HTMLElement;
    if (!element) return;

    const rect = element.getBoundingClientRect();
    
    // Create highlight element
    this.highlightElement = document.createElement('div');
    this.highlightElement.className = 'onboarding-highlight';
    this.highlightElement.style.cssText = `
      position: fixed;
      top: ${rect.top - 4}px;
      left: ${rect.left - 4}px;
      width: ${rect.width + 8}px;
      height: ${rect.height + 8}px;
      border: 2px solid #3b82f6;
      border-radius: 8px;
      background: rgba(59, 130, 246, 0.1);
      z-index: 10000;
      pointer-events: none;
      animation: pulse 2s infinite;
    `;

    document.body.appendChild(this.highlightElement);
  }

  private hideOverlay(): void {
    if (this.overlayElement) {
      this.overlayElement.remove();
      this.overlayElement = null;
    }

    if (this.highlightElement) {
      this.highlightElement.remove();
      this.highlightElement = null;
    }
  }

  private validateStep(step: OnboardingStep): boolean {
    if (!step.validation) return true;

    switch (step.validation.type) {
      case 'element_exists':
        return !!document.querySelector(step.validation.target!);
      
      case 'value_equals':
        const element = document.querySelector(step.validation.target!) as HTMLInputElement;
        return element?.value === step.validation.value;
      
      case 'custom':
        return step.validation.validator ? step.validation.validator() : true;
      
      default:
        return true;
    }
  }

  private setupAutoAdvance(step: OnboardingStep): void {
    if (!step.autoAdvance) return;

    switch (step.autoAdvance.trigger) {
      case 'timeout':
        setTimeout(() => {
          this.nextStep();
        }, step.autoAdvance.delay || 3000);
        break;
      
      case 'event':
        const handler = () => {
          this.nextStep();
          document.removeEventListener(step.autoAdvance!.event!, handler);
        };
        document.addEventListener(step.autoAdvance.event!, handler);
        break;
      
      case 'condition':
        const checkCondition = () => {
          if (step.autoAdvance!.condition!()) {
            this.nextStep();
          } else {
            setTimeout(checkCondition, 500);
          }
        };
        checkCondition();
        break;
    }
  }

  private setupEventListeners(): void {
    // Add CSS for animations
    if (!document.getElementById('onboarding-styles')) {
      const style = document.createElement('style');
      style.id = 'onboarding-styles';
      style.textContent = `
        @keyframes pulse {
          0% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7); }
          70% { box-shadow: 0 0 0 10px rgba(59, 130, 246, 0); }
          100% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0); }
        }
      `;
      document.head.appendChild(style);
    }
  }

  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => callback(data));
    }
  }

  private getFlowStats(): any {
    return {
      completedSteps: this.state.userProgress.completedSteps,
      totalSteps: this.state.userProgress.totalSteps,
      timeSpent: this.state.userProgress.timeSpent,
      skippedSteps: this.state.skippedSteps.length,
    };
  }
}

export { OnboardingEngine };
