import { NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '@modules/prisma/prisma.service';
import { LoggerService } from '@services/logger/logger.service';
export interface TenantRequest extends Request {
    organizationId?: string;
    userId?: string;
    userRole?: string;
    tenant?: {
        organizationId: string;
        userId?: string;
        userRole?: string;
        permissions?: string[];
    };
}
export declare class TenantMiddleware implements NestMiddleware {
    private jwtService;
    private prismaService;
    private logger;
    constructor(jwtService: JwtService, prismaService: PrismaService, logger: LoggerService);
    use(req: TenantRequest, res: Response, next: NextFunction): Promise<void>;
    private extractOrganizationId;
    private extractToken;
    private getUserPermissions;
    private getDefaultPermissions;
    private isPublicEndpoint;
}
