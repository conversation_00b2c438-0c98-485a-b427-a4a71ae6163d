import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '@modules/prisma/prisma.service';
import { RedisService } from '@modules/redis/redis.service';
import { LoggerService } from '@services/logger/logger.service';
declare enum UserRole {
    SUPER_ADMIN = "SUPER_ADMIN",
    ORG_ADMIN = "ORG_ADMIN",
    DEVELOPER = "DEVELOPER",
    VIEWER = "VIEWER"
}
export interface JwtPayload {
    userId: string;
    organizationId: string;
    role: UserRole;
    email: string;
    iat?: number;
    exp?: number;
}
export interface AuthTokens {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
}
export interface LoginDto {
    email: string;
    password: string;
    organizationId: string;
}
export interface RegisterDto {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    organizationId: string;
    role?: UserRole;
}
export declare class AuthService {
    private jwtService;
    private prismaService;
    private redisService;
    private configService;
    private logger;
    private readonly saltRounds;
    private readonly refreshTokenExpiry;
    constructor(jwtService: JwtService, prismaService: PrismaService, redisService: RedisService, configService: ConfigService, logger: LoggerService);
    validateUser(email: string, password: string, organizationId: string): Promise<any>;
    login(loginDto: LoginDto): Promise<AuthTokens>;
    register(registerDto: RegisterDto): Promise<AuthTokens>;
    refreshToken(refreshToken: string): Promise<AuthTokens>;
    logout(userId: string, refreshToken?: string): Promise<void>;
    private generateRefreshToken;
    private validatePasswordStrength;
    verifyToken(token: string): Promise<JwtPayload>;
}
export {};
