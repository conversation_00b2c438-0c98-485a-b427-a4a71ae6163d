import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { Sso<PERSON>ontroller } from './sso.controller';
import { SsoService } from './sso.service';
import { SamlStrategy } from './strategies/saml.strategy';
import { OidcStrategy } from './strategies/oidc.strategy';
import { ActiveDirectoryStrategy } from './strategies/active-directory.strategy';
import { PrismaModule } from '@modules/prisma/prisma.module';
import { LoggerModule } from '@services/logger/logger.module';

@Module({
  imports: [
    ConfigModule,
    PrismaModule,
    LoggerModule,
  ],
  controllers: [SsoController],
  providers: [
    SsoService,
    SamlStrategy,
    OidcStrategy,
    ActiveDirectoryStrategy,
  ],
  exports: [SsoService],
})
export class SsoModule {}
