import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as Sentry from '@sentry/node';
import { ProfilingIntegration } from '@sentry/profiling-node';

@Injectable()
export class SentryService {
  private readonly logger = new Logger(SentryService.name);
  private initialized = false;

  constructor(private configService: ConfigService) {}

  /**
   * Initialize Sentry with production-ready configuration
   */
  init(): void {
    const dsn = this.configService.get('SENTRY_DSN');
    const environment = this.configService.get('NODE_ENV', 'development');
    const release = this.configService.get('APP_VERSION', '1.0.0');

    if (!dsn) {
      this.logger.warn('Sentry DSN not configured, skipping initialization');
      return;
    }

    try {
      Sentry.init({
        dsn,
        environment,
        release: `synapseai@${release}`,
        
        // Performance Monitoring
        tracesSampleRate: environment === 'production' ? 0.1 : 1.0,
        profilesSampleRate: environment === 'production' ? 0.1 : 1.0,
        
        // Integrations
        integrations: [
          // Profiling integration for performance insights
          new ProfilingIntegration(),
          
          // HTTP integration for API monitoring
          new Sentry.Integrations.Http({ tracing: true }),
          
          // Express integration (if using Express)
          new Sentry.Integrations.Express({ app: undefined }),
          
          // Console integration for logging
          new Sentry.Integrations.Console(),
          
          // OnUncaughtException integration
          new Sentry.Integrations.OnUncaughtException({
            exitEvenIfOtherHandlersAreRegistered: false,
          }),
          
          // OnUnhandledRejection integration
          new Sentry.Integrations.OnUnhandledRejection({
            mode: 'warn',
          }),
        ],

        // Error Filtering
        beforeSend(event, hint) {
          // Filter out known non-critical errors
          const error = hint.originalException;
          
          if (error instanceof Error) {
            // Skip database connection timeouts in development
            if (environment === 'development' && error.message.includes('connect ECONNREFUSED')) {
              return null;
            }
            
            // Skip Redis connection errors in development
            if (environment === 'development' && error.message.includes('Redis connection')) {
              return null;
            }
            
            // Skip validation errors (they're handled by the application)
            if (error.message.includes('ValidationError')) {
              return null;
            }
          }
          
          return event;
        },

        // Performance Filtering
        beforeSendTransaction(event) {
          // Filter out health check transactions
          if (event.transaction === 'GET /health') {
            return null;
          }
          
          // Filter out very fast transactions (< 100ms) in production
          if (environment === 'production' && event.start_timestamp && event.timestamp) {
            const duration = (event.timestamp - event.start_timestamp) * 1000;
            if (duration < 100) {
              return null;
            }
          }
          
          return event;
        },

        // Additional Configuration
        maxBreadcrumbs: 50,
        attachStacktrace: true,
        sendDefaultPii: false, // Don't send personally identifiable information
        
        // Custom Tags
        initialScope: {
          tags: {
            component: 'backend',
            service: 'synapseai',
          },
        },
      });

      this.initialized = true;
      this.logger.log('Sentry initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Sentry:', error);
    }
  }

  /**
   * Capture an exception with context
   */
  captureException(
    error: Error,
    context?: {
      user?: { id: string; email?: string; organizationId?: string };
      tags?: Record<string, string>;
      extra?: Record<string, any>;
      level?: Sentry.SeverityLevel;
    },
  ): string | undefined {
    if (!this.initialized) {
      this.logger.error('Sentry not initialized, cannot capture exception');
      return;
    }

    return Sentry.withScope((scope) => {
      if (context?.user) {
        scope.setUser({
          id: context.user.id,
          email: context.user.email,
        });
        
        if (context.user.organizationId) {
          scope.setTag('organizationId', context.user.organizationId);
        }
      }

      if (context?.tags) {
        Object.entries(context.tags).forEach(([key, value]) => {
          scope.setTag(key, value);
        });
      }

      if (context?.extra) {
        Object.entries(context.extra).forEach(([key, value]) => {
          scope.setExtra(key, value);
        });
      }

      if (context?.level) {
        scope.setLevel(context.level);
      }

      return Sentry.captureException(error);
    });
  }

  /**
   * Capture a message with context
   */
  captureMessage(
    message: string,
    level: Sentry.SeverityLevel = 'info',
    context?: {
      user?: { id: string; email?: string; organizationId?: string };
      tags?: Record<string, string>;
      extra?: Record<string, any>;
    },
  ): string | undefined {
    if (!this.initialized) {
      this.logger.warn('Sentry not initialized, cannot capture message');
      return;
    }

    return Sentry.withScope((scope) => {
      if (context?.user) {
        scope.setUser({
          id: context.user.id,
          email: context.user.email,
        });
        
        if (context.user.organizationId) {
          scope.setTag('organizationId', context.user.organizationId);
        }
      }

      if (context?.tags) {
        Object.entries(context.tags).forEach(([key, value]) => {
          scope.setTag(key, value);
        });
      }

      if (context?.extra) {
        Object.entries(context.extra).forEach(([key, value]) => {
          scope.setExtra(key, value);
        });
      }

      scope.setLevel(level);
      return Sentry.captureMessage(message);
    });
  }

  /**
   * Start a performance transaction
   */
  startTransaction(
    name: string,
    op: string,
    description?: string,
  ): Sentry.Transaction | undefined {
    if (!this.initialized) {
      return;
    }

    return Sentry.startTransaction({
      name,
      op,
      description,
    });
  }

  /**
   * Add breadcrumb for debugging
   */
  addBreadcrumb(
    message: string,
    category: string,
    level: Sentry.SeverityLevel = 'info',
    data?: Record<string, any>,
  ): void {
    if (!this.initialized) {
      return;
    }

    Sentry.addBreadcrumb({
      message,
      category,
      level,
      data,
      timestamp: Date.now() / 1000,
    });
  }

  /**
   * Set user context
   */
  setUser(user: {
    id: string;
    email?: string;
    organizationId?: string;
    role?: string;
  }): void {
    if (!this.initialized) {
      return;
    }

    Sentry.setUser({
      id: user.id,
      email: user.email,
    });

    if (user.organizationId) {
      Sentry.setTag('organizationId', user.organizationId);
    }

    if (user.role) {
      Sentry.setTag('userRole', user.role);
    }
  }

  /**
   * Set custom tags
   */
  setTags(tags: Record<string, string>): void {
    if (!this.initialized) {
      return;
    }

    Sentry.setTags(tags);
  }

  /**
   * Set custom context
   */
  setContext(key: string, context: Record<string, any>): void {
    if (!this.initialized) {
      return;
    }

    Sentry.setContext(key, context);
  }

  /**
   * Flush pending events (useful for graceful shutdown)
   */
  async flush(timeout: number = 2000): Promise<boolean> {
    if (!this.initialized) {
      return true;
    }

    try {
      return await Sentry.flush(timeout);
    } catch (error) {
      this.logger.error('Failed to flush Sentry events:', error);
      return false;
    }
  }

  /**
   * Close Sentry client
   */
  async close(timeout: number = 2000): Promise<boolean> {
    if (!this.initialized) {
      return true;
    }

    try {
      return await Sentry.close(timeout);
    } catch (error) {
      this.logger.error('Failed to close Sentry client:', error);
      return false;
    }
  }

  /**
   * Check if Sentry is initialized
   */
  isInitialized(): boolean {
    return this.initialized;
  }
}
