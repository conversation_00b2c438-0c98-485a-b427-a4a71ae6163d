import { ConfigService } from '@nestjs/config';
import { PrismaService } from '@modules/prisma/prisma.service';
import { LoggerService } from '@services/logger/logger.service';
interface SsoProvider {
    id: string;
    name: string;
    type: 'SAML' | 'OIDC' | 'ACTIVE_DIRECTORY';
    organizationId: string;
    isActive: boolean;
    config: Record<string, any>;
}
interface SsoUserProfile {
    email: string;
    firstName?: string;
    lastName?: string;
    groups?: string[];
    attributes?: Record<string, any>;
}
interface CreateSsoProviderDto {
    name: string;
    type: 'SAML' | 'OIDC' | 'ACTIVE_DIRECTORY';
    organizationId: string;
    config: Record<string, any>;
}
export declare class SsoService {
    private prisma;
    private configService;
    private logger;
    constructor(prisma: PrismaService, configService: ConfigService, logger: LoggerService);
    createSsoProvider(createDto: CreateSsoProviderDto): Promise<SsoProvider>;
    getSsoProviders(organizationId: string): Promise<SsoProvider[]>;
    processSsoCallback(providerId: string, userProfile: SsoUserProfile): Promise<{
        accessToken: string;
        refreshToken: string;
        user: any;
    }>;
    private validateSsoConfig;
    private validateSamlConfig;
    private validateOidcConfig;
    private validateActiveDirectoryConfig;
    private createUserFromSsoProfile;
    private updateUserFromSsoProfile;
    private mapSsoGroupsToRole;
    private generateTokens;
}
export {};
