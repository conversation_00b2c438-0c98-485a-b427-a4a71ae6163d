"use strict";var S=Object.defineProperty;var k=Object.getOwnPropertyDescriptor;var D=Object.getOwnPropertyNames;var R=Object.prototype.hasOwnProperty;var B=(e,t)=>{for(var n in t)S(e,n,{get:t[n],enumerable:!0})},U=(e,t,n,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let _ of D(t))!R.call(e,_)&&_!==n&&S(e,_,{get:()=>t[_],enumerable:!(o=k(t,_))||o.enumerable});return e};var L=e=>U(S({},"__esModule",{value:!0}),e);var Et={};B(Et,{QueryEngine:()=>Q,__wbg_String_8f0eb39a4a4c2f66:()=>H,__wbg_buffer_609cc3eee51ed158:()=>J,__wbg_call_672a4d21634d4a24:()=>K,__wbg_call_7cccdd69e0791ae2:()=>X,__wbg_crypto_805be4ce92f1e370:()=>Y,__wbg_done_769e5ede4b31c67b:()=>Z,__wbg_entries_3265d4158b33e5dc:()=>ee,__wbg_getRandomValues_f6a868620c8bab49:()=>te,__wbg_getTime_46267b1c24877e30:()=>ne,__wbg_get_67b2ba62fc30de12:()=>re,__wbg_get_b9b93047fe3cf45b:()=>oe,__wbg_get_ece95cf6585650d9:()=>_e,__wbg_getwithrefkey_1dc361bd10053bfe:()=>ce,__wbg_has_a5ea9117f258a0ec:()=>ie,__wbg_instanceof_ArrayBuffer_e14585432e3737fc:()=>ue,__wbg_instanceof_Map_f3469ce2244d2430:()=>se,__wbg_instanceof_Promise_935168b8f4b49db3:()=>fe,__wbg_instanceof_Uint8Array_17156bcf118086a9:()=>ae,__wbg_isArray_a1eab7e0d067391b:()=>be,__wbg_isSafeInteger_343e2beeeece1bb0:()=>le,__wbg_iterator_9a24c88df860dc65:()=>ge,__wbg_keys_5c77a08ddc2fb8a6:()=>de,__wbg_length_a446193dc22c12f8:()=>we,__wbg_length_e2d2a49132c1b256:()=>pe,__wbg_msCrypto_2ac4d17c4748234a:()=>xe,__wbg_new0_f788a2397c7ca929:()=>ye,__wbg_new_23a2665fac83c611:()=>me,__wbg_new_405e22f390576ce2:()=>he,__wbg_new_5e0be73521bc8c17:()=>Te,__wbg_new_78feb108b6472713:()=>qe,__wbg_new_a12002a7f91c75be:()=>Se,__wbg_newnoargs_105ed471475aaf50:()=>Ae,__wbg_newwithbyteoffsetandlength_d97e637ebe145a9a:()=>Ie,__wbg_newwithlength_a381634e90c276d4:()=>Ee,__wbg_next_25feadfc0913fea9:()=>Oe,__wbg_next_6574e1a8a62d1055:()=>Fe,__wbg_node_ecc8306b9857f33d:()=>Me,__wbg_now_7fd00a794a07d388:()=>je,__wbg_now_807e54c39636c349:()=>ke,__wbg_now_b3f7572f6ef3d3a9:()=>De,__wbg_process_5cff2739921be718:()=>Re,__wbg_push_737cfc8c1432c2c6:()=>Be,__wbg_queueMicrotask_5a8a9131f3f0b37b:()=>Ue,__wbg_queueMicrotask_6d79674585219521:()=>Le,__wbg_randomFillSync_d3c85af7e31cf1f8:()=>ve,__wbg_require_0c566c6f2eef6c79:()=>Ne,__wbg_resolve_4851785c9c5f573d:()=>$e,__wbg_setTimeout_5d6a1d4fc51ea450:()=>Ce,__wbg_set_37837023f3d740e8:()=>Ve,__wbg_set_3f1d0b984ed272ed:()=>ze,__wbg_set_65595bdd868b3009:()=>We,__wbg_set_8fc6bf8a5b1071d1:()=>Pe,__wbg_set_bb8cecf6a62b9f46:()=>Ge,__wbg_set_wasm:()=>v,__wbg_static_accessor_GLOBAL_88a902d13a557d07:()=>Qe,__wbg_static_accessor_GLOBAL_THIS_56578be7e9f832b0:()=>He,__wbg_static_accessor_SELF_37c5d418e4bf5819:()=>Je,__wbg_static_accessor_WINDOW_5de37043a91a9c40:()=>Ke,__wbg_subarray_aa9065fa9dc5df96:()=>Xe,__wbg_then_44b73946d2fb3e7d:()=>Ye,__wbg_then_48b406749878a531:()=>Ze,__wbg_valueOf_7392193dd78c6b97:()=>et,__wbg_value_cd1ffa7b1ab794f1:()=>tt,__wbg_versions_a8e5a362e1f16442:()=>nt,__wbindgen_as_number:()=>rt,__wbindgen_bigint_from_i64:()=>ot,__wbindgen_bigint_from_u64:()=>_t,__wbindgen_bigint_get_as_i64:()=>ct,__wbindgen_boolean_get:()=>it,__wbindgen_cb_drop:()=>ut,__wbindgen_closure_wrapper7516:()=>st,__wbindgen_debug_string:()=>ft,__wbindgen_error_new:()=>at,__wbindgen_in:()=>bt,__wbindgen_init_externref_table:()=>lt,__wbindgen_is_bigint:()=>gt,__wbindgen_is_function:()=>dt,__wbindgen_is_object:()=>wt,__wbindgen_is_string:()=>pt,__wbindgen_is_undefined:()=>xt,__wbindgen_jsval_eq:()=>yt,__wbindgen_jsval_loose_eq:()=>mt,__wbindgen_memory:()=>ht,__wbindgen_number_get:()=>Tt,__wbindgen_number_new:()=>qt,__wbindgen_string_get:()=>St,__wbindgen_string_new:()=>At,__wbindgen_throw:()=>It,debug_panic:()=>W,getBuildTimeInfo:()=>z});module.exports=L(Et);var y=()=>{};y.prototype=y;let r;function v(e){r=e}let s=0,m=null;function h(){return(m===null||m.byteLength===0)&&(m=new Uint8Array(r.memory.buffer)),m}const N=typeof TextEncoder>"u"?(0,module.require)("util").TextEncoder:TextEncoder;let T=new N("utf-8");const $=typeof T.encodeInto=="function"?function(e,t){return T.encodeInto(e,t)}:function(e,t){const n=T.encode(e);return t.set(n),{read:e.length,written:n.length}};function f(e,t,n){if(n===void 0){const u=T.encode(e),a=t(u.length,1)>>>0;return h().subarray(a,a+u.length).set(u),s=u.length,a}let o=e.length,_=t(o,1)>>>0;const i=h();let c=0;for(;c<o;c++){const u=e.charCodeAt(c);if(u>127)break;i[_+c]=u}if(c!==o){c!==0&&(e=e.slice(c)),_=n(_,o,o=c+e.length*3,1)>>>0;const u=h().subarray(_+c,_+o),a=$(e,u);c+=a.written,_=n(_,o,c,1)>>>0}return s=c,_}let w=null;function g(){return(w===null||w.buffer.detached===!0||w.buffer.detached===void 0&&w.buffer!==r.memory.buffer)&&(w=new DataView(r.memory.buffer)),w}function p(e){const t=r.__externref_table_alloc();return r.__wbindgen_export_4.set(t,e),t}function b(e,t){try{return e.apply(this,t)}catch(n){const o=p(n);r.__wbindgen_exn_store(o)}}const C=typeof TextDecoder>"u"?(0,module.require)("util").TextDecoder:TextDecoder;let I=new C("utf-8",{ignoreBOM:!0,fatal:!0});I.decode();function q(e,t){return e=e>>>0,I.decode(h().subarray(e,e+t))}function l(e){return e==null}const E=typeof FinalizationRegistry>"u"?{register:()=>{},unregister:()=>{}}:new FinalizationRegistry(e=>{r.__wbindgen_export_5.get(e.dtor)(e.a,e.b)});function V(e,t,n,o){const _={a:e,b:t,cnt:1,dtor:n},i=(...c)=>{_.cnt++;const u=_.a;_.a=0;try{return o(u,_.b,...c)}finally{--_.cnt===0?(r.__wbindgen_export_5.get(_.dtor)(u,_.b),E.unregister(_)):_.a=u}};return i.original=_,E.register(i,_,_),i}function A(e){const t=typeof e;if(t=="number"||t=="boolean"||e==null)return`${e}`;if(t=="string")return`"${e}"`;if(t=="symbol"){const _=e.description;return _==null?"Symbol":`Symbol(${_})`}if(t=="function"){const _=e.name;return typeof _=="string"&&_.length>0?`Function(${_})`:"Function"}if(Array.isArray(e)){const _=e.length;let i="[";_>0&&(i+=A(e[0]));for(let c=1;c<_;c++)i+=", "+A(e[c]);return i+="]",i}const n=/\[object ([^\]]+)\]/.exec(toString.call(e));let o;if(n&&n.length>1)o=n[1];else return toString.call(e);if(o=="Object")try{return"Object("+JSON.stringify(e)+")"}catch{return"Object"}return e instanceof Error?`${e.name}: ${e.message}
${e.stack}`:o}function O(e){const t=r.__wbindgen_export_4.get(e);return r.__externref_table_dealloc(e),t}function z(){return r.getBuildTimeInfo()}function W(e){var t=l(e)?0:f(e,r.__wbindgen_malloc,r.__wbindgen_realloc),n=s;const o=r.debug_panic(t,n);if(o[1])throw O(o[0])}function P(e,t,n){r.closure580_externref_shim(e,t,n)}function G(e,t,n,o){r.closure130_externref_shim(e,t,n,o)}const F=typeof FinalizationRegistry>"u"?{register:()=>{},unregister:()=>{}}:new FinalizationRegistry(e=>r.__wbg_queryengine_free(e>>>0,1));class Q{__destroy_into_raw(){const t=this.__wbg_ptr;return this.__wbg_ptr=0,F.unregister(this),t}free(){const t=this.__destroy_into_raw();r.__wbg_queryengine_free(t,0)}constructor(t,n,o){const _=r.queryengine_new(t,n,o);if(_[2])throw O(_[1]);return this.__wbg_ptr=_[0]>>>0,F.register(this,this.__wbg_ptr,this),this}connect(t,n){const o=f(t,r.__wbindgen_malloc,r.__wbindgen_realloc),_=s,i=f(n,r.__wbindgen_malloc,r.__wbindgen_realloc),c=s;return r.queryengine_connect(this.__wbg_ptr,o,_,i,c)}disconnect(t,n){const o=f(t,r.__wbindgen_malloc,r.__wbindgen_realloc),_=s,i=f(n,r.__wbindgen_malloc,r.__wbindgen_realloc),c=s;return r.queryengine_disconnect(this.__wbg_ptr,o,_,i,c)}query(t,n,o,_){const i=f(t,r.__wbindgen_malloc,r.__wbindgen_realloc),c=s,u=f(n,r.__wbindgen_malloc,r.__wbindgen_realloc),a=s;var d=l(o)?0:f(o,r.__wbindgen_malloc,r.__wbindgen_realloc),x=s;const M=f(_,r.__wbindgen_malloc,r.__wbindgen_realloc),j=s;return r.queryengine_query(this.__wbg_ptr,i,c,u,a,d,x,M,j)}startTransaction(t,n,o){const _=f(t,r.__wbindgen_malloc,r.__wbindgen_realloc),i=s,c=f(n,r.__wbindgen_malloc,r.__wbindgen_realloc),u=s,a=f(o,r.__wbindgen_malloc,r.__wbindgen_realloc),d=s;return r.queryengine_startTransaction(this.__wbg_ptr,_,i,c,u,a,d)}commitTransaction(t,n,o){const _=f(t,r.__wbindgen_malloc,r.__wbindgen_realloc),i=s,c=f(n,r.__wbindgen_malloc,r.__wbindgen_realloc),u=s,a=f(o,r.__wbindgen_malloc,r.__wbindgen_realloc),d=s;return r.queryengine_commitTransaction(this.__wbg_ptr,_,i,c,u,a,d)}rollbackTransaction(t,n,o){const _=f(t,r.__wbindgen_malloc,r.__wbindgen_realloc),i=s,c=f(n,r.__wbindgen_malloc,r.__wbindgen_realloc),u=s,a=f(o,r.__wbindgen_malloc,r.__wbindgen_realloc),d=s;return r.queryengine_rollbackTransaction(this.__wbg_ptr,_,i,c,u,a,d)}metrics(t){const n=f(t,r.__wbindgen_malloc,r.__wbindgen_realloc),o=s;return r.queryengine_metrics(this.__wbg_ptr,n,o)}trace(t){const n=f(t,r.__wbindgen_malloc,r.__wbindgen_realloc),o=s;return r.queryengine_trace(this.__wbg_ptr,n,o)}}function H(e,t){const n=String(t),o=f(n,r.__wbindgen_malloc,r.__wbindgen_realloc),_=s;g().setInt32(e+4*1,_,!0),g().setInt32(e+4*0,o,!0)}function J(e){return e.buffer}function K(){return b(function(e,t){return e.call(t)},arguments)}function X(){return b(function(e,t,n){return e.call(t,n)},arguments)}function Y(e){return e.crypto}function Z(e){return e.done}function ee(e){return Object.entries(e)}function te(){return b(function(e,t){e.getRandomValues(t)},arguments)}function ne(e){return e.getTime()}function re(){return b(function(e,t){return Reflect.get(e,t)},arguments)}function oe(e,t){return e[t>>>0]}function _e(){return b(function(e,t){return e[t]},arguments)}function ce(e,t){return e[t]}function ie(){return b(function(e,t){return Reflect.has(e,t)},arguments)}function ue(e){let t;try{t=e instanceof ArrayBuffer}catch{t=!1}return t}function se(e){let t;try{t=e instanceof Map}catch{t=!1}return t}function fe(e){let t;try{t=e instanceof Promise}catch{t=!1}return t}function ae(e){let t;try{t=e instanceof Uint8Array}catch{t=!1}return t}function be(e){return Array.isArray(e)}function le(e){return Number.isSafeInteger(e)}function ge(){return Symbol.iterator}function de(e){return Object.keys(e)}function we(e){return e.length}function pe(e){return e.length}function xe(e){return e.msCrypto}function ye(){return new Date}function me(e,t){try{var n={a:e,b:t},o=(i,c)=>{const u=n.a;n.a=0;try{return G(u,n.b,i,c)}finally{n.a=u}};return new Promise(o)}finally{n.a=n.b=0}}function he(){return new Object}function Te(){return new Map}function qe(){return new Array}function Se(e){return new Uint8Array(e)}function Ae(e,t){return new y(q(e,t))}function Ie(e,t,n){return new Uint8Array(e,t>>>0,n>>>0)}function Ee(e){return new Uint8Array(e>>>0)}function Oe(e){return e.next}function Fe(){return b(function(e){return e.next()},arguments)}function Me(e){return e.node}function je(e){return e.now()}function ke(){return Date.now()}function De(){return b(function(){return Date.now()},arguments)}function Re(e){return e.process}function Be(e,t){return e.push(t)}function Ue(e){return e.queueMicrotask}function Le(e){queueMicrotask(e)}function ve(){return b(function(e,t){e.randomFillSync(t)},arguments)}function Ne(){return b(function(){return module.require},arguments)}function $e(e){return Promise.resolve(e)}function Ce(e,t){return setTimeout(e,t>>>0)}function Ve(e,t,n){e[t>>>0]=n}function ze(e,t,n){e[t]=n}function We(e,t,n){e.set(t,n>>>0)}function Pe(e,t,n){return e.set(t,n)}function Ge(){return b(function(e,t,n){return Reflect.set(e,t,n)},arguments)}function Qe(){const e=typeof global>"u"?null:global;return l(e)?0:p(e)}function He(){const e=typeof globalThis>"u"?null:globalThis;return l(e)?0:p(e)}function Je(){const e=typeof self>"u"?null:self;return l(e)?0:p(e)}function Ke(){const e=typeof window>"u"?null:window;return l(e)?0:p(e)}function Xe(e,t,n){return e.subarray(t>>>0,n>>>0)}function Ye(e,t){return e.then(t)}function Ze(e,t,n){return e.then(t,n)}function et(e){return e.valueOf()}function tt(e){return e.value}function nt(e){return e.versions}function rt(e){return+e}function ot(e){return e}function _t(e){return BigInt.asUintN(64,e)}function ct(e,t){const n=t,o=typeof n=="bigint"?n:void 0;g().setBigInt64(e+8*1,l(o)?BigInt(0):o,!0),g().setInt32(e+4*0,!l(o),!0)}function it(e){const t=e;return typeof t=="boolean"?t?1:0:2}function ut(e){const t=e.original;return t.cnt--==1?(t.a=0,!0):!1}function st(e,t,n){return V(e,t,581,P)}function ft(e,t){const n=A(t),o=f(n,r.__wbindgen_malloc,r.__wbindgen_realloc),_=s;g().setInt32(e+4*1,_,!0),g().setInt32(e+4*0,o,!0)}function at(e,t){return new Error(q(e,t))}function bt(e,t){return e in t}function lt(){const e=r.__wbindgen_export_4,t=e.grow(4);e.set(0,void 0),e.set(t+0,void 0),e.set(t+1,null),e.set(t+2,!0),e.set(t+3,!1)}function gt(e){return typeof e=="bigint"}function dt(e){return typeof e=="function"}function wt(e){const t=e;return typeof t=="object"&&t!==null}function pt(e){return typeof e=="string"}function xt(e){return e===void 0}function yt(e,t){return e===t}function mt(e,t){return e==t}function ht(){return r.memory}function Tt(e,t){const n=t,o=typeof n=="number"?n:void 0;g().setFloat64(e+8*1,l(o)?0:o,!0),g().setInt32(e+4*0,!l(o),!0)}function qt(e){return e}function St(e,t){const n=t,o=typeof n=="string"?n:void 0;var _=l(o)?0:f(o,r.__wbindgen_malloc,r.__wbindgen_realloc),i=s;g().setInt32(e+4*1,i,!0),g().setInt32(e+4*0,_,!0)}function At(e,t){return q(e,t)}function It(e,t){throw new Error(q(e,t))}0&&(module.exports={QueryEngine,__wbg_String_8f0eb39a4a4c2f66,__wbg_buffer_609cc3eee51ed158,__wbg_call_672a4d21634d4a24,__wbg_call_7cccdd69e0791ae2,__wbg_crypto_805be4ce92f1e370,__wbg_done_769e5ede4b31c67b,__wbg_entries_3265d4158b33e5dc,__wbg_getRandomValues_f6a868620c8bab49,__wbg_getTime_46267b1c24877e30,__wbg_get_67b2ba62fc30de12,__wbg_get_b9b93047fe3cf45b,__wbg_get_ece95cf6585650d9,__wbg_getwithrefkey_1dc361bd10053bfe,__wbg_has_a5ea9117f258a0ec,__wbg_instanceof_ArrayBuffer_e14585432e3737fc,__wbg_instanceof_Map_f3469ce2244d2430,__wbg_instanceof_Promise_935168b8f4b49db3,__wbg_instanceof_Uint8Array_17156bcf118086a9,__wbg_isArray_a1eab7e0d067391b,__wbg_isSafeInteger_343e2beeeece1bb0,__wbg_iterator_9a24c88df860dc65,__wbg_keys_5c77a08ddc2fb8a6,__wbg_length_a446193dc22c12f8,__wbg_length_e2d2a49132c1b256,__wbg_msCrypto_2ac4d17c4748234a,__wbg_new0_f788a2397c7ca929,__wbg_new_23a2665fac83c611,__wbg_new_405e22f390576ce2,__wbg_new_5e0be73521bc8c17,__wbg_new_78feb108b6472713,__wbg_new_a12002a7f91c75be,__wbg_newnoargs_105ed471475aaf50,__wbg_newwithbyteoffsetandlength_d97e637ebe145a9a,__wbg_newwithlength_a381634e90c276d4,__wbg_next_25feadfc0913fea9,__wbg_next_6574e1a8a62d1055,__wbg_node_ecc8306b9857f33d,__wbg_now_7fd00a794a07d388,__wbg_now_807e54c39636c349,__wbg_now_b3f7572f6ef3d3a9,__wbg_process_5cff2739921be718,__wbg_push_737cfc8c1432c2c6,__wbg_queueMicrotask_5a8a9131f3f0b37b,__wbg_queueMicrotask_6d79674585219521,__wbg_randomFillSync_d3c85af7e31cf1f8,__wbg_require_0c566c6f2eef6c79,__wbg_resolve_4851785c9c5f573d,__wbg_setTimeout_5d6a1d4fc51ea450,__wbg_set_37837023f3d740e8,__wbg_set_3f1d0b984ed272ed,__wbg_set_65595bdd868b3009,__wbg_set_8fc6bf8a5b1071d1,__wbg_set_bb8cecf6a62b9f46,__wbg_set_wasm,__wbg_static_accessor_GLOBAL_88a902d13a557d07,__wbg_static_accessor_GLOBAL_THIS_56578be7e9f832b0,__wbg_static_accessor_SELF_37c5d418e4bf5819,__wbg_static_accessor_WINDOW_5de37043a91a9c40,__wbg_subarray_aa9065fa9dc5df96,__wbg_then_44b73946d2fb3e7d,__wbg_then_48b406749878a531,__wbg_valueOf_7392193dd78c6b97,__wbg_value_cd1ffa7b1ab794f1,__wbg_versions_a8e5a362e1f16442,__wbindgen_as_number,__wbindgen_bigint_from_i64,__wbindgen_bigint_from_u64,__wbindgen_bigint_get_as_i64,__wbindgen_boolean_get,__wbindgen_cb_drop,__wbindgen_closure_wrapper7516,__wbindgen_debug_string,__wbindgen_error_new,__wbindgen_in,__wbindgen_init_externref_table,__wbindgen_is_bigint,__wbindgen_is_function,__wbindgen_is_object,__wbindgen_is_string,__wbindgen_is_undefined,__wbindgen_jsval_eq,__wbindgen_jsval_loose_eq,__wbindgen_memory,__wbindgen_number_get,__wbindgen_number_new,__wbindgen_string_get,__wbindgen_string_new,__wbindgen_throw,debug_panic,getBuildTimeInfo});
