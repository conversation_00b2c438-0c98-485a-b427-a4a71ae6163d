#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="C:\laragon\www\max\trae\kilo-teset\node_modules\.pnpm\@sentry+profiling-node@1.3.5_@sentry+node@7.120.3\node_modules\@sentry\profiling-node\scripts\node_modules;C:\laragon\www\max\trae\kilo-teset\node_modules\.pnpm\@sentry+profiling-node@1.3.5_@sentry+node@7.120.3\node_modules\@sentry\profiling-node\node_modules;C:\laragon\www\max\trae\kilo-teset\node_modules\.pnpm\@sentry+profiling-node@1.3.5_@sentry+node@7.120.3\node_modules\@sentry\node_modules;C:\laragon\www\max\trae\kilo-teset\node_modules\.pnpm\@sentry+profiling-node@1.3.5_@sentry+node@7.120.3\node_modules;C:\laragon\www\max\trae\kilo-teset\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/c/laragon/www/max/trae/kilo-teset/node_modules/.pnpm/@sentry+profiling-node@1.3.5_@sentry+node@7.120.3/node_modules/@sentry/profiling-node/scripts/node_modules:/mnt/c/laragon/www/max/trae/kilo-teset/node_modules/.pnpm/@sentry+profiling-node@1.3.5_@sentry+node@7.120.3/node_modules/@sentry/profiling-node/node_modules:/mnt/c/laragon/www/max/trae/kilo-teset/node_modules/.pnpm/@sentry+profiling-node@1.3.5_@sentry+node@7.120.3/node_modules/@sentry/node_modules:/mnt/c/laragon/www/max/trae/kilo-teset/node_modules/.pnpm/@sentry+profiling-node@1.3.5_@sentry+node@7.120.3/node_modules:/mnt/c/laragon/www/max/trae/kilo-teset/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../../../../node_modules/.pnpm/@sentry+profiling-node@1.3.5_@sentry+node@7.120.3/node_modules/@sentry/profiling-node/scripts/prune-profiler-binaries.mjs" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../../../../node_modules/.pnpm/@sentry+profiling-node@1.3.5_@sentry+node@7.120.3/node_modules/@sentry/profiling-node/scripts/prune-profiler-binaries.mjs" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../../../../node_modules/.pnpm/@sentry+profiling-node@1.3.5_@sentry+node@7.120.3/node_modules/@sentry/profiling-node/scripts/prune-profiler-binaries.mjs" $args
  } else {
    & "node$exe"  "$basedir/../../../../node_modules/.pnpm/@sentry+profiling-node@1.3.5_@sentry+node@7.120.3/node_modules/@sentry/profiling-node/scripts/prune-profiler-binaries.mjs" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
