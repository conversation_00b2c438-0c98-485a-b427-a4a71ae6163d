"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SsoService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const prisma_service_1 = require("../../prisma/prisma.service");
const logger_service_1 = require("../../../services/logger/logger.service");
const jwt = require("jsonwebtoken");
var UserRole;
(function (UserRole) {
    UserRole["SUPER_ADMIN"] = "SUPER_ADMIN";
    UserRole["ORG_ADMIN"] = "ORG_ADMIN";
    UserRole["DEVELOPER"] = "DEVELOPER";
    UserRole["VIEWER"] = "VIEWER";
})(UserRole || (UserRole = {}));
let SsoService = class SsoService {
    constructor(prisma, configService, logger) {
        this.prisma = prisma;
        this.configService = configService;
        this.logger = logger;
    }
    async createSsoProvider(createDto) {
        try {
            this.validateSsoConfig(createDto.type, createDto.config);
            const provider = await this.prisma.ssoProvider.create({
                data: {
                    name: createDto.name,
                    type: createDto.type,
                    organizationId: createDto.organizationId,
                    config: createDto.config,
                    isActive: true,
                },
            });
            this.logger.audit('SSO_PROVIDER_CREATED', 'success', {
                organizationId: createDto.organizationId,
                resource: 'sso_provider',
                details: {
                    providerId: provider.id,
                    type: createDto.type,
                    name: createDto.name,
                },
            });
            return provider;
        }
        catch (error) {
            this.logger.error('Failed to create SSO provider', error);
            throw new common_1.BadRequestException('Failed to create SSO provider');
        }
    }
    async getSsoProviders(organizationId) {
        return this.prisma.ssoProvider.findMany({
            where: {
                organizationId,
                isActive: true,
            },
        });
    }
    async processSsoCallback(providerId, userProfile) {
        try {
            const provider = await this.prisma.ssoProvider.findUnique({
                where: { id: providerId },
                include: { organization: true },
            });
            if (!provider || !provider.isActive) {
                throw new common_1.UnauthorizedException('SSO provider not found or inactive');
            }
            let user = await this.prisma.user.findFirst({
                where: {
                    email: userProfile.email,
                    organizationId: provider.organizationId,
                },
            });
            if (!user) {
                user = await this.createUserFromSsoProfile(userProfile, provider.organizationId, provider.config);
            }
            else {
                user = await this.updateUserFromSsoProfile(user.id, userProfile);
            }
            const tokens = await this.generateTokens(user);
            this.logger.audit('SSO_LOGIN', 'success', {
                userId: user.id,
                organizationId: provider.organizationId,
                resource: 'sso_authentication',
                details: {
                    providerId,
                    providerType: provider.type,
                    email: userProfile.email,
                },
            });
            return {
                ...tokens,
                user: {
                    id: user.id,
                    email: user.email,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    role: user.role,
                    organizationId: user.organizationId,
                },
            };
        }
        catch (error) {
            this.logger.error('SSO callback processing failed', error);
            throw new common_1.UnauthorizedException('SSO authentication failed');
        }
    }
    validateSsoConfig(type, config) {
        switch (type) {
            case 'SAML':
                this.validateSamlConfig(config);
                break;
            case 'OIDC':
                this.validateOidcConfig(config);
                break;
            case 'ACTIVE_DIRECTORY':
                this.validateActiveDirectoryConfig(config);
                break;
            default:
                throw new common_1.BadRequestException('Invalid SSO provider type');
        }
    }
    validateSamlConfig(config) {
        const required = ['entryPoint', 'issuer', 'cert'];
        for (const field of required) {
            if (!config[field]) {
                throw new common_1.BadRequestException(`SAML configuration missing: ${field}`);
            }
        }
    }
    validateOidcConfig(config) {
        const required = ['issuer', 'clientId', 'clientSecret'];
        for (const field of required) {
            if (!config[field]) {
                throw new common_1.BadRequestException(`OIDC configuration missing: ${field}`);
            }
        }
    }
    validateActiveDirectoryConfig(config) {
        const required = ['url', 'baseDN', 'username', 'password'];
        for (const field of required) {
            if (!config[field]) {
                throw new common_1.BadRequestException(`Active Directory configuration missing: ${field}`);
            }
        }
    }
    async createUserFromSsoProfile(profile, organizationId, providerConfig) {
        const role = this.mapSsoGroupsToRole(profile.groups || [], providerConfig);
        return this.prisma.user.create({
            data: {
                email: profile.email,
                firstName: profile.firstName || '',
                lastName: profile.lastName || '',
                role,
                organizationId,
                isActive: true,
                ssoProvider: true,
                lastLoginAt: new Date(),
            },
        });
    }
    async updateUserFromSsoProfile(userId, profile) {
        return this.prisma.user.update({
            where: { id: userId },
            data: {
                firstName: profile.firstName || undefined,
                lastName: profile.lastName || undefined,
                lastLoginAt: new Date(),
            },
        });
    }
    mapSsoGroupsToRole(groups, config) {
        const roleMapping = config.roleMapping || {};
        if (groups.some(group => roleMapping.admin?.includes(group))) {
            return UserRole.ORG_ADMIN;
        }
        if (groups.some(group => roleMapping.developer?.includes(group))) {
            return UserRole.DEVELOPER;
        }
        return UserRole.VIEWER;
    }
    async generateTokens(user) {
        const jwtSecret = this.configService.get('JWT_SECRET');
        const jwtExpiresIn = this.configService.get('JWT_EXPIRES_IN', '1h');
        const payload = {
            userId: user.id,
            email: user.email,
            role: user.role,
            organizationId: user.organizationId,
        };
        const accessToken = jwt.sign(payload, jwtSecret, { expiresIn: jwtExpiresIn });
        const refreshToken = jwt.sign({ userId: user.id }, jwtSecret, { expiresIn: '7d' });
        return { accessToken, refreshToken };
    }
};
exports.SsoService = SsoService;
exports.SsoService = SsoService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        config_1.ConfigService,
        logger_service_1.LoggerService])
], SsoService);
//# sourceMappingURL=sso.service.js.map