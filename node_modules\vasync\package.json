{"name": "vasync", "version": "2.2.1", "description": "utilities for observable asynchronous control flow", "main": "./lib/vasync.js", "repository": {"type": "git", "url": "https://github.com/joyent/node-vasync.git"}, "scripts": {"test": "./node_modules/.bin/tap --stdout tests/ && ./node_modules/.bin/nodeunit tests/compat.js && ./node_modules/.bin/nodeunit tests/compat_tryEach.js"}, "devDependencies": {"tap": "~0.4.8", "nodeunit": "0.8.7"}, "dependencies": {"verror": "1.10.0"}, "engines": ["node >=0.6.0"], "license": "MIT"}