import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  UseGuards,
  Req,
  Res,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { SsoService } from './sso.service';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { RolesGuard } from '../guards/roles.guard';
import { Roles } from '../decorators/roles.decorator';
import { CurrentUser } from '../decorators/current-user.decorator';
import { IsString, IsObject, IsEnum } from 'class-validator';

enum UserRole {
  SUPER_ADMIN = 'SUPER_ADMIN',
  ORG_ADMIN = 'ORG_ADMIN',
  DEVELOPER = 'DEVELOPER',
  VIEWER = 'VIEWER'
}

class CreateSsoProviderDto {
  @ApiProperty({ example: 'Company SAML' })
  @IsString()
  name: string;

  @ApiProperty({ example: 'SAML', enum: ['SAML', 'OIDC', 'ACTIVE_DIRECTORY'] })
  @IsEnum(['SAML', 'OIDC', 'ACTIVE_DIRECTORY'])
  type: 'SAML' | 'OIDC' | 'ACTIVE_DIRECTORY';

  @ApiProperty({ 
    example: { 
      entryPoint: 'https://company.okta.com/sso/saml',
      issuer: 'company-saml',
      cert: '-----BEGIN CERTIFICATE-----...'
    }
  })
  @IsObject()
  config: Record<string, any>;
}

@ApiTags('SSO Authentication')
@Controller('api/v1/auth/sso')
export class SsoController {
  constructor(private ssoService: SsoService) {}

  @Post('providers')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ORG_ADMIN, UserRole.SUPER_ADMIN)
  @HttpCode(HttpStatus.CREATED)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create SSO provider configuration' })
  @ApiResponse({
    status: 201,
    description: 'SSO provider created successfully',
  })
  async createSsoProvider(
    @Body() createDto: CreateSsoProviderDto,
    @CurrentUser() user: any,
  ) {
    return this.ssoService.createSsoProvider({
      ...createDto,
      organizationId: user.organizationId,
    });
  }

  @Get('providers')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ORG_ADMIN, UserRole.SUPER_ADMIN, UserRole.DEVELOPER)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get SSO providers for organization' })
  @ApiResponse({
    status: 200,
    description: 'SSO providers retrieved successfully',
  })
  async getSsoProviders(@CurrentUser() user: any) {
    return this.ssoService.getSsoProviders(user.organizationId);
  }

  @Get('login/:providerId')
  @ApiOperation({ summary: 'Initiate SSO login' })
  @ApiResponse({
    status: 302,
    description: 'Redirect to SSO provider',
  })
  async initiateSsoLogin(
    @Param('providerId') providerId: string,
    @Res() res: Response,
  ) {
    // This would redirect to the SSO provider
    // Implementation depends on the specific SSO strategy
    res.redirect(`/api/v1/auth/sso/callback/${providerId}`);
  }

  @Post('callback/:providerId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Handle SSO callback' })
  @ApiResponse({
    status: 200,
    description: 'SSO authentication successful',
  })
  async handleSsoCallback(
    @Param('providerId') providerId: string,
    @Req() req: Request,
  ) {
    // Extract user profile from SSO response
    // This would be populated by the SSO strategy middleware
    const userProfile = (req as any).user;
    
    return this.ssoService.processSsoCallback(providerId, userProfile);
  }

  @Get('metadata/:providerId')
  @ApiOperation({ summary: 'Get SSO provider metadata' })
  @ApiResponse({
    status: 200,
    description: 'SSO provider metadata',
  })
  async getSsoMetadata(
    @Param('providerId') providerId: string,
    @Res() res: Response,
  ) {
    // Return SAML metadata or OIDC discovery document
    // Implementation depends on provider type
    res.setHeader('Content-Type', 'application/xml');
    res.send('<?xml version="1.0"?><EntityDescriptor>...</EntityDescriptor>');
  }
}
