export interface Feature {
  id: string;
  name: string;
  description: string;
  category: 'core' | 'advanced' | 'premium' | 'experimental';
  complexity: 'beginner' | 'intermediate' | 'advanced';
  prerequisites: string[];
  unlockConditions: Array<{
    type: 'usage' | 'time' | 'achievement' | 'manual';
    condition: string;
    value?: any;
    description: string;
  }>;
  benefits: string[];
  risks?: string[];
  documentation: {
    quickStart: string;
    fullGuide: string;
    videoUrl?: string;
    examples: Array<{
      title: string;
      description: string;
      config?: Record<string, any>;
    }>;
  };
  ui: {
    icon: string;
    color: string;
    badge?: string;
    spotlight?: {
      selector: string;
      position: 'top' | 'bottom' | 'left' | 'right';
      message: string;
    };
  };
  analytics: {
    adoptionRate: number;
    successRate: number;
    averageTimeToMaster: number;
    commonIssues: string[];
  };
}

export interface UserProgress {
  userId: string;
  unlockedFeatures: string[];
  activeFeatures: string[];
  completedTutorials: string[];
  usageStats: Record<string, {
    firstUsed: number;
    lastUsed: number;
    usageCount: number;
    successCount: number;
    errorCount: number;
  }>;
  achievements: Array<{
    id: string;
    unlockedAt: number;
    category: string;
  }>;
  preferences: {
    discoveryMode: 'guided' | 'self_paced' | 'minimal';
    showSpotlights: boolean;
    autoActivateFeatures: boolean;
    complexityLevel: 'beginner' | 'intermediate' | 'advanced';
  };
}

export interface DiscoveryEvent {
  type: 'feature_unlocked' | 'feature_activated' | 'tutorial_started' | 'tutorial_completed' | 'achievement_earned';
  featureId?: string;
  tutorialId?: string;
  achievementId?: string;
  timestamp: number;
  context?: Record<string, any>;
}

class FeatureDiscovery {
  private features: Map<string, Feature> = new Map();
  private userProgress: UserProgress;
  private eventListeners: Map<string, Function[]> = new Map();
  private discoveryQueue: string[] = [];
  private activeSpotlights: Set<string> = new Set();

  constructor(initialProgress?: Partial<UserProgress>) {
    this.userProgress = {
      userId: '',
      unlockedFeatures: [],
      activeFeatures: [],
      completedTutorials: [],
      usageStats: {},
      achievements: [],
      preferences: {
        discoveryMode: 'guided',
        showSpotlights: true,
        autoActivateFeatures: false,
        complexityLevel: 'beginner',
      },
      ...initialProgress,
    };

    this.initializeFeatures();
    this.setupProgressTracking();
  }

  /**
   * Check for newly unlocked features
   */
  checkUnlocks(): string[] {
    const newlyUnlocked: string[] = [];

    for (const [featureId, feature] of this.features.entries()) {
      // Skip if already unlocked
      if (this.userProgress.unlockedFeatures.includes(featureId)) continue;

      // Check prerequisites
      const hasPrerequisites = feature.prerequisites.every(prereq =>
        this.userProgress.unlockedFeatures.includes(prereq)
      );
      if (!hasPrerequisites) continue;

      // Check unlock conditions
      const meetsConditions = feature.unlockConditions.every(condition =>
        this.evaluateUnlockCondition(condition)
      );

      if (meetsConditions) {
        this.unlockFeature(featureId);
        newlyUnlocked.push(featureId);
      }
    }

    return newlyUnlocked;
  }

  /**
   * Unlock a feature
   */
  unlockFeature(featureId: string): boolean {
    const feature = this.features.get(featureId);
    if (!feature) return false;

    if (this.userProgress.unlockedFeatures.includes(featureId)) return true;

    this.userProgress.unlockedFeatures.push(featureId);
    this.discoveryQueue.push(featureId);

    this.emit('feature_unlocked', {
      type: 'feature_unlocked',
      featureId,
      timestamp: Date.now(),
    });

    // Auto-activate if preference is set and feature is beginner-level
    if (this.userProgress.preferences.autoActivateFeatures && 
        feature.complexity === 'beginner') {
      this.activateFeature(featureId);
    }

    return true;
  }

  /**
   * Activate a feature
   */
  activateFeature(featureId: string): boolean {
    const feature = this.features.get(featureId);
    if (!feature) return false;

    if (!this.userProgress.unlockedFeatures.includes(featureId)) return false;
    if (this.userProgress.activeFeatures.includes(featureId)) return true;

    this.userProgress.activeFeatures.push(featureId);

    // Initialize usage stats
    if (!this.userProgress.usageStats[featureId]) {
      this.userProgress.usageStats[featureId] = {
        firstUsed: Date.now(),
        lastUsed: Date.now(),
        usageCount: 0,
        successCount: 0,
        errorCount: 0,
      };
    }

    this.emit('feature_activated', {
      type: 'feature_activated',
      featureId,
      timestamp: Date.now(),
    });

    // Show spotlight if enabled
    if (this.userProgress.preferences.showSpotlights && feature.ui.spotlight) {
      this.showSpotlight(featureId);
    }

    return true;
  }

  /**
   * Deactivate a feature
   */
  deactivateFeature(featureId: string): boolean {
    const index = this.userProgress.activeFeatures.indexOf(featureId);
    if (index === -1) return false;

    this.userProgress.activeFeatures.splice(index, 1);
    this.hideSpotlight(featureId);

    this.emit('feature_deactivated', {
      type: 'feature_deactivated' as any,
      featureId,
      timestamp: Date.now(),
    });

    return true;
  }

  /**
   * Record feature usage
   */
  recordUsage(featureId: string, success: boolean = true): void {
    if (!this.userProgress.activeFeatures.includes(featureId)) return;

    const stats = this.userProgress.usageStats[featureId];
    if (!stats) return;

    stats.lastUsed = Date.now();
    stats.usageCount++;
    
    if (success) {
      stats.successCount++;
    } else {
      stats.errorCount++;
    }

    // Check for achievements
    this.checkAchievements(featureId);

    this.emit('feature_used', {
      type: 'feature_used' as any,
      featureId,
      success,
      timestamp: Date.now(),
    });
  }

  /**
   * Get next features to discover
   */
  getDiscoveryQueue(): Feature[] {
    return this.discoveryQueue
      .map(id => this.features.get(id))
      .filter(Boolean) as Feature[];
  }

  /**
   * Get recommended features based on user progress
   */
  getRecommendedFeatures(): Feature[] {
    const userLevel = this.userProgress.preferences.complexityLevel;
    const activeCount = this.userProgress.activeFeatures.length;

    return Array.from(this.features.values())
      .filter(feature => {
        // Must be unlocked but not active
        if (!this.userProgress.unlockedFeatures.includes(feature.id)) return false;
        if (this.userProgress.activeFeatures.includes(feature.id)) return false;

        // Match complexity level
        const complexityOrder = { beginner: 1, intermediate: 2, advanced: 3 };
        const userOrder = complexityOrder[userLevel];
        const featureOrder = complexityOrder[feature.complexity];
        
        if (featureOrder > userOrder + 1) return false;

        // Don't overwhelm beginners
        if (userLevel === 'beginner' && activeCount >= 3) return false;

        return true;
      })
      .sort((a, b) => {
        // Sort by adoption rate and complexity
        const aScore = a.analytics.adoptionRate * (a.complexity === userLevel ? 1.5 : 1);
        const bScore = b.analytics.adoptionRate * (b.complexity === userLevel ? 1.5 : 1);
        return bScore - aScore;
      })
      .slice(0, 5);
  }

  /**
   * Get feature by ID
   */
  getFeature(featureId: string): Feature | null {
    return this.features.get(featureId) || null;
  }

  /**
   * Get all features by category
   */
  getFeaturesByCategory(category: Feature['category']): Feature[] {
    return Array.from(this.features.values())
      .filter(feature => feature.category === category);
  }

  /**
   * Get user progress
   */
  getUserProgress(): UserProgress {
    return { ...this.userProgress };
  }

  /**
   * Update user preferences
   */
  updatePreferences(preferences: Partial<UserProgress['preferences']>): void {
    this.userProgress.preferences = { ...this.userProgress.preferences, ...preferences };
    this.emit('preferences_updated', { preferences: this.userProgress.preferences });
  }

  /**
   * Show feature spotlight
   */
  showSpotlight(featureId: string): void {
    const feature = this.features.get(featureId);
    if (!feature?.ui.spotlight) return;

    this.activeSpotlights.add(featureId);
    this.emit('spotlight_shown', { featureId, spotlight: feature.ui.spotlight });
  }

  /**
   * Hide feature spotlight
   */
  hideSpotlight(featureId: string): void {
    this.activeSpotlights.delete(featureId);
    this.emit('spotlight_hidden', { featureId });
  }

  /**
   * Clear discovery queue
   */
  clearDiscoveryQueue(): void {
    this.discoveryQueue = [];
  }

  /**
   * Add event listener
   */
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  /**
   * Remove event listener
   */
  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * Private methods
   */
  private initializeFeatures(): void {
    // Core features
    this.features.set('visual_builder', {
      id: 'visual_builder',
      name: 'Visual Workflow Builder',
      description: 'Drag-and-drop interface for building AI workflows',
      category: 'core',
      complexity: 'beginner',
      prerequisites: [],
      unlockConditions: [
        {
          type: 'manual',
          condition: 'always_available',
          description: 'Available from the start',
        },
      ],
      benefits: [
        'No coding required',
        'Visual workflow design',
        'Real-time preview',
      ],
      documentation: {
        quickStart: 'Create your first workflow in 5 minutes',
        fullGuide: '/docs/visual-builder',
        videoUrl: '/videos/visual-builder-intro.mp4',
        examples: [
          {
            title: 'Simple Chatbot',
            description: 'A basic question-answering bot',
          },
        ],
      },
      ui: {
        icon: '🎨',
        color: '#3b82f6',
        spotlight: {
          selector: '.visual-canvas',
          position: 'center',
          message: 'Build AI workflows visually with drag and drop!',
        },
      },
      analytics: {
        adoptionRate: 0.95,
        successRate: 0.87,
        averageTimeToMaster: 30,
        commonIssues: ['Connection confusion', 'Node configuration'],
      },
    });

    this.features.set('ai_suggestions', {
      id: 'ai_suggestions',
      name: 'AI-Powered Suggestions',
      description: 'Get intelligent recommendations while building',
      category: 'advanced',
      complexity: 'intermediate',
      prerequisites: ['visual_builder'],
      unlockConditions: [
        {
          type: 'usage',
          condition: 'visual_builder_usage',
          value: 5,
          description: 'Use visual builder 5 times',
        },
      ],
      benefits: [
        'Smart component recommendations',
        'Configuration optimization',
        'Best practice guidance',
      ],
      documentation: {
        quickStart: 'Enable AI suggestions in settings',
        fullGuide: '/docs/ai-suggestions',
        examples: [
          {
            title: 'Optimized Agent',
            description: 'AI-suggested agent configuration',
          },
        ],
      },
      ui: {
        icon: '✨',
        color: '#8b5cf6',
        badge: 'AI',
        spotlight: {
          selector: '.suggestion-panel',
          position: 'right',
          message: 'AI suggestions help optimize your workflows!',
        },
      },
      analytics: {
        adoptionRate: 0.73,
        successRate: 0.91,
        averageTimeToMaster: 15,
        commonIssues: ['Suggestion overload', 'Relevance concerns'],
      },
    });

    this.features.set('advanced_debugging', {
      id: 'advanced_debugging',
      name: 'Advanced Debugging Tools',
      description: 'Deep insights into workflow execution',
      category: 'advanced',
      complexity: 'advanced',
      prerequisites: ['visual_builder', 'ai_suggestions'],
      unlockConditions: [
        {
          type: 'achievement',
          condition: 'workflow_master',
          description: 'Complete 10 successful workflows',
        },
      ],
      benefits: [
        'Step-by-step execution tracing',
        'Performance analytics',
        'Error diagnosis',
      ],
      risks: [
        'Information overload for beginners',
        'Performance impact when enabled',
      ],
      documentation: {
        quickStart: 'Enable debugging in workflow settings',
        fullGuide: '/docs/debugging',
        examples: [
          {
            title: 'Performance Optimization',
            description: 'Using debug tools to optimize workflow speed',
          },
        ],
      },
      ui: {
        icon: '🔍',
        color: '#ef4444',
        spotlight: {
          selector: '.debug-panel',
          position: 'bottom',
          message: 'Advanced debugging tools for power users!',
        },
      },
      analytics: {
        adoptionRate: 0.45,
        successRate: 0.82,
        averageTimeToMaster: 60,
        commonIssues: ['Complexity', 'Performance impact'],
      },
    });
  }

  private setupProgressTracking(): void {
    // Auto-check for unlocks periodically
    setInterval(() => {
      this.checkUnlocks();
    }, 30000); // Check every 30 seconds
  }

  private evaluateUnlockCondition(condition: Feature['unlockConditions'][0]): boolean {
    switch (condition.type) {
      case 'manual':
        return condition.condition === 'always_available';
      
      case 'usage':
        const stats = this.userProgress.usageStats[condition.condition];
        return stats ? stats.usageCount >= (condition.value || 1) : false;
      
      case 'time':
        // Check if enough time has passed since first usage
        const firstUsage = this.userProgress.usageStats[condition.condition]?.firstUsed;
        if (!firstUsage) return false;
        const timePassed = Date.now() - firstUsage;
        return timePassed >= (condition.value || 0);
      
      case 'achievement':
        return this.userProgress.achievements.some(a => a.id === condition.condition);
      
      default:
        return false;
    }
  }

  private checkAchievements(featureId: string): void {
    const stats = this.userProgress.usageStats[featureId];
    if (!stats) return;

    // Check for usage milestones
    const milestones = [5, 10, 25, 50, 100];
    for (const milestone of milestones) {
      if (stats.usageCount === milestone) {
        const achievementId = `${featureId}_usage_${milestone}`;
        if (!this.userProgress.achievements.some(a => a.id === achievementId)) {
          this.userProgress.achievements.push({
            id: achievementId,
            unlockedAt: Date.now(),
            category: 'usage',
          });
          
          this.emit('achievement_earned', {
            type: 'achievement_earned',
            achievementId,
            timestamp: Date.now(),
          });
        }
      }
    }

    // Check for success rate achievements
    if (stats.usageCount >= 10) {
      const successRate = stats.successCount / stats.usageCount;
      if (successRate >= 0.9) {
        const achievementId = `${featureId}_expert`;
        if (!this.userProgress.achievements.some(a => a.id === achievementId)) {
          this.userProgress.achievements.push({
            id: achievementId,
            unlockedAt: Date.now(),
            category: 'mastery',
          });
          
          this.emit('achievement_earned', {
            type: 'achievement_earned',
            achievementId,
            timestamp: Date.now(),
          });
        }
      }
    }
  }

  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => callback(data));
    }
  }
}

export { FeatureDiscovery };
