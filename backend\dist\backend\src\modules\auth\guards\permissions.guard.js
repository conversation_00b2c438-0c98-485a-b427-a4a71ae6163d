"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PermissionsGuard = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const prisma_service_1 = require("../../prisma/prisma.service");
const permissions_decorator_1 = require("../decorators/permissions.decorator");
let PermissionsGuard = class PermissionsGuard {
    constructor(reflector, prismaService) {
        this.reflector = reflector;
        this.prismaService = prismaService;
    }
    async canActivate(context) {
        const requiredPermissions = this.reflector.getAllAndOverride(permissions_decorator_1.PERMISSIONS_KEY, [
            context.getHandler(),
            context.getClass(),
        ]);
        if (!requiredPermissions) {
            return true;
        }
        const { user } = context.switchToHttp().getRequest();
        if (!user) {
            throw new common_1.ForbiddenException('User not authenticated');
        }
        const userPermissions = await this.getUserPermissions(user.userId, user.organizationId);
        const hasAllPermissions = requiredPermissions.every(permission => userPermissions.includes(permission));
        if (!hasAllPermissions) {
            throw new common_1.ForbiddenException(`Access denied. Required permissions: ${requiredPermissions.join(', ')}`);
        }
        return true;
    }
    async getUserPermissions(userId, organizationId) {
        try {
            const user = await this.prismaService.user.findFirst({
                where: { id: userId, organizationId },
                include: {
                    organization: {
                        include: {
                            roles: true,
                        },
                    },
                },
            });
            if (!user) {
                return [];
            }
            const rolePermissions = user.organization.roles
                .filter(role => role.name === user.role)
                .flatMap(role => role.permissions);
            const defaultPermissions = this.getDefaultPermissions(user.role);
            return [...new Set([...rolePermissions, ...defaultPermissions])];
        }
        catch (error) {
            return [];
        }
    }
    getDefaultPermissions(role) {
        const permissions = {
            SUPER_ADMIN: [
                'organizations:*',
                'users:*',
                'agents:*',
                'tools:*',
                'workflows:*',
                'analytics:*',
                'billing:*',
                'widgets:*',
                'templates:*',
                'providers:*',
                'hitl:*',
                'knowledge:*',
                'sessions:*',
                'notifications:*',
                'sandboxes:*',
            ],
            ORG_ADMIN: [
                'users:read',
                'users:write',
                'agents:*',
                'tools:*',
                'workflows:*',
                'analytics:read',
                'billing:read',
                'widgets:*',
                'templates:*',
                'providers:read',
                'hitl:*',
                'knowledge:*',
                'sessions:*',
                'notifications:*',
                'sandboxes:*',
            ],
            DEVELOPER: [
                'agents:read',
                'agents:write',
                'tools:read',
                'tools:write',
                'workflows:read',
                'workflows:write',
                'analytics:read',
                'widgets:read',
                'widgets:write',
                'templates:read',
                'templates:write',
                'providers:read',
                'hitl:read',
                'knowledge:read',
                'knowledge:write',
                'sessions:read',
                'sessions:write',
                'notifications:read',
                'sandboxes:read',
                'sandboxes:write',
            ],
            VIEWER: [
                'agents:read',
                'tools:read',
                'workflows:read',
                'analytics:read',
                'widgets:read',
                'templates:read',
                'providers:read',
                'hitl:read',
                'knowledge:read',
                'sessions:read',
                'notifications:read',
                'sandboxes:read',
            ],
        };
        return permissions[role] || [];
    }
};
exports.PermissionsGuard = PermissionsGuard;
exports.PermissionsGuard = PermissionsGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Reflector,
        prisma_service_1.PrismaService])
], PermissionsGuard);
//# sourceMappingURL=permissions.guard.js.map