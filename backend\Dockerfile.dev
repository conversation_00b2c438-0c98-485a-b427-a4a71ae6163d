# Development Dockerfile for SynapseAI Backend
# Optimized for development with hot reload and debugging

FROM node:18-alpine AS base

# Install system dependencies
RUN apk add --no-cache \
    curl \
    bash \
    git \
    python3 \
    make \
    g++

# Set working directory
WORKDIR /app

# Development stage
FROM base AS dev

# Install development dependencies
RUN npm install -g nodemon ts-node @nestjs/cli

# Copy package files
COPY backend/package*.json ./backend/
COPY shared/package*.json ./shared/

# Install dependencies
RUN cd backend && npm ci
RUN cd shared && npm ci

# Copy source code
COPY backend/ ./backend/
COPY shared/ ./shared/

# Generate Prisma client
RUN cd backend && npx prisma generate

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001
RUN chown -R nestjs:nodejs /app
USER nestjs

# Expose port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3001/health || exit 1

# Start development server with hot reload
CMD ["npm", "run", "start:dev"]
