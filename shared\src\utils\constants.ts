/**
 * Shared constants used across SynapseAI platform
 */

// API Configuration
export const API_VERSION = 'v1';
export const API_PREFIX = `/api/${API_VERSION}`;

// Pagination defaults
export const DEFAULT_PAGE_SIZE = 20;
export const MAX_PAGE_SIZE = 100;

// Session configuration
export const SESSION_TIMEOUT = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
export const SESSION_CLEANUP_INTERVAL = 60 * 60 * 1000; // 1 hour in milliseconds

// Rate limiting
export const DEFAULT_RATE_LIMIT = {
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 100,
};

export const AUTH_RATE_LIMIT = {
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 5,
};

// File upload limits
export const MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
export const ALLOWED_FILE_TYPES = [
  'application/pdf',
  'text/plain',
  'text/csv',
  'application/json',
  'image/jpeg',
  'image/png',
  'image/webp',
];

// Validation constraints
export const VALIDATION_RULES = {
  email: {
    maxLength: 255,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  },
  password: {
    minLength: 8,
    maxLength: 128,
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
  },
  organizationSlug: {
    minLength: 3,
    maxLength: 50,
    pattern: /^[a-z0-9-]+$/,
  },
  name: {
    minLength: 1,
    maxLength: 100,
  },
};

// Error codes
export const ERROR_CODES = {
  // Authentication errors
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  TOKEN_INVALID: 'TOKEN_INVALID',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',

  // Validation errors
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INVALID_INPUT: 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',

  // Resource errors
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
  RESOURCE_ALREADY_EXISTS: 'RESOURCE_ALREADY_EXISTS',
  RESOURCE_CONFLICT: 'RESOURCE_CONFLICT',

  // System errors
  INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  DATABASE_ERROR: 'DATABASE_ERROR',
  EXTERNAL_SERVICE_ERROR: 'EXTERNAL_SERVICE_ERROR',

  // Rate limiting
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',

  // Organization errors
  ORGANIZATION_LIMIT_EXCEEDED: 'ORGANIZATION_LIMIT_EXCEEDED',
  FEATURE_NOT_AVAILABLE: 'FEATURE_NOT_AVAILABLE',
} as const;

// HTTP status codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
} as const;

// WebSocket event types
export const WEBSOCKET_EVENTS = {
  CONNECTION: 'connection',
  DISCONNECT: 'disconnect',
  ERROR: 'error',
  
  // APIX Protocol events
  USER_MESSAGE: 'user_message',
  THINKING_STATUS: 'thinking_status',
  TEXT_CHUNK: 'text_chunk',
  TOOL_CALL_START: 'tool_call_start',
  TOOL_CALL_RESULT: 'tool_call_result',
  TOOL_CALL_ERROR: 'tool_call_error',
  REQUEST_USER_INPUT: 'request_user_input',
  USER_RESPONSE: 'user_response',
  STATE_UPDATE: 'state_update',
  CONTROL_SIGNAL: 'control_signal',
} as const;

// Queue names
export const QUEUE_NAMES = {
  AGENT_EXECUTION: 'agent-execution',
  TOOL_EXECUTION: 'tool-execution',
  WORKFLOW_EXECUTION: 'workflow-execution',
  NOTIFICATION: 'notification',
  ANALYTICS: 'analytics',
  BILLING: 'billing',
} as const;

// Cache keys
export const CACHE_KEYS = {
  USER_SESSION: (sessionId: string) => `session:${sessionId}`,
  USER_PERMISSIONS: (userId: string) => `permissions:${userId}`,
  ORGANIZATION_SETTINGS: (orgId: string) => `org:${orgId}:settings`,
  RATE_LIMIT: (key: string) => `rate_limit:${key}`,
  FEATURE_FLAGS: (orgId: string) => `feature_flags:${orgId}`,
} as const;

// Default configuration values
export const DEFAULT_CONFIG = {
  jwt: {
    expiresIn: '1h',
    refreshExpiresIn: '7d',
  },
  pagination: {
    defaultPage: 1,
    defaultLimit: DEFAULT_PAGE_SIZE,
    maxLimit: MAX_PAGE_SIZE,
  },
  upload: {
    maxFileSize: MAX_FILE_SIZE,
    allowedTypes: ALLOWED_FILE_TYPES,
  },
} as const;
